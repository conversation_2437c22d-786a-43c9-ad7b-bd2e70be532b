import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

interface SentenceAnalysis {
  sentence: string;
  domain: string;
  domainDescription: string;
  isCompliant: boolean;
  completenessScore: number;
  issues: string[];
  suggestions: string[];
}

interface PolicyAnalysis {
  sentences: SentenceAnalysis[];
  overallScore: number;
  missingDomains: string[];
  recommendations: string[];
  complianceLevel: 'Excellent' | 'Good' | 'Needs Improvement' | 'Poor';
}

export async function POST(request: NextRequest) {
  try {
    const { policyContent, systemId, systemName } = await request.json();

    if (!policyContent || !policyContent.trim()) {
      return NextResponse.json(
        { error: 'Policy content is required' },
        { status: 400 }
      );
    }

    // Detect language
    const isArabic = /[\u0600-\u06FF]/.test(policyContent);
    
    const model = genAI.getGenerativeModel({
      model: "gemini-1.5-pro",
      generationConfig: {
        maxOutputTokens: 32768, // Increased for comprehensive analysis
        temperature: 0.1,
      },
    });

    const prompt = `
You are an expert privacy policy analyst specializing in Saudi Arabia's Personal Data Protection Law (PDPL) compliance analysis.

**IMPORTANT**: Respond in ${isArabic ? 'Arabic' : 'English'} language only.

**Privacy Policy Content to Analyze:**
${policyContent}

**System Information:**
- System ID: ${systemId}
- System Name: ${systemName}

**SENTENCE EXTRACTION RULES:**
1. **SKIP ONLY**: Pure headers like "Privacy Policy", "Section 1", "Table of Contents", standalone section numbers
2. **EXTRACT EVERYTHING ELSE**: All sentences that contain ANY substantive information about data practices
3. **INCLUDE**: Policy statements, data collection descriptions, user rights, contact information, legal bases, processing purposes, retention periods, sharing practices, security measures, etc.
4. **INCLUDE**: Sentences with specific details like "We collect name and email", "Data is stored for 5 years", "Contact <NAME_EMAIL>"
5. **INCLUDE**: Regulatory references, compliance statements, user obligations, company commitments
6. **BE COMPREHENSIVE**: Extract 90%+ of all meaningful content - err on the side of including too much rather than too little

**Analysis Requirements:**

Analyze EVERY SENTENCE that contains substantive privacy policy information. Only skip pure formatting elements like standalone headers. Be extremely comprehensive in sentence extraction.

**EXPECTED ANALYSIS SCOPE:**
- For a typical privacy policy: Extract 20-50+ sentences minimum
- For comprehensive policies: Extract 50-100+ sentences
- Include ALL data collection statements, processing descriptions, user rights, contact information, legal bases, retention periods, sharing practices, security measures, etc.
- If you extract fewer than 15 sentences, you are likely being too restrictive
- Better to over-include than under-include sentences

**The 10 Mandatory PDPL Domains:**
1. **Entity Name and Activity** - Official entity name, business description, core activities, target audience
2. **Contact Information and Update Record** - Phone, email, address, DPO details, update dates and history
3. **Personal Data to Be Collected** - Specific data types, categories (account, payment, cookies, location), mandatory vs optional
4. **Collecting Personal Data Methods and Purposes** - Direct/indirect collection methods, specific purposes, legal bases under PDPL
5. **Personal Data Processing** - Processing operations, data lifecycle stages, usage mechanisms
6. **Personal Data Sharing** - Third-party disclosure, recipients, sharing purposes, frequency
7. **Personal Data Storage, Retention Period, and Destruction** - Storage locations, retention periods, destruction methods, security measures
8. **Personal Data Subjects Rights** - 8 specific PDPL rights, exercise procedures, response timeframes
9. **Complaint and Objection Filing Mechanism** - Complaint procedures, responsible departments, SDAIA contact information
10. **Availing and Providing Access to Privacy Policy** - Accessibility methods, language options, notification mechanisms

**DETAILED ANALYSIS REQUIREMENTS:**

For each meaningful sentence, provide:
- **Exact sentence text** (no headers or titles)
- **Domain classification** with specific reasoning
- **Detailed compliance assessment** with specific PDPL article references
- **Specific issues** with exact regulatory gaps identified
- **Detailed enhancement suggestions** with specific implementation steps

**ISSUE DETAIL REQUIREMENTS:**
- Reference specific PDPL articles with detailed explanations (e.g., "Missing Article 12 requirement for explicit consent mechanism. PDPL Article 12 mandates that controllers must obtain clear, informed consent before processing personal data. This sentence lacks any mention of consent procedures, user choice mechanisms, or opt-in/opt-out capabilities.")
- Identify exact regulatory gaps with comprehensive context (e.g., "No legal basis specified as required by Article 15 of PDPL. The law requires controllers to clearly state which of the 10 legal bases they rely on: consent, legitimate interest, contractual necessity, legal obligation, vital interests, public task, etc. This sentence fails to specify any legal justification.")
- Point out missing mandatory elements with detailed requirements (e.g., "DPO contact information missing per Article 18. PDPL requires appointment of Data Protection Officer for certain controllers and mandates disclosure of DPO name, email, phone number, and physical address for data subject inquiries.")
- Explain the compliance impact and consequences of each issue
- Provide context about why each requirement exists and its importance

**ENHANCEMENT SUGGESTION REQUIREMENTS:**
- Provide comprehensive implementation steps with exact wording (e.g., "Add dedicated section stating: 'Data Protection Officer: Our appointed DPO, [Name], oversees all data protection activities. Contact: Email: <EMAIL>, Phone: +966-11-XXX-XXXX, Address: [Physical Address]. The DPO is available to assist with data subject requests, privacy concerns, and regulatory compliance matters.'")
- Include multiple specific wording options and alternatives
- Reference specific PDPL compliance requirements with article numbers
- Suggest comprehensive data categories with examples (e.g., "Specify data types: 'We collect the following personal data: (1) Identity Data: full name, national ID, passport number; (2) Contact Data: email address, phone number, postal address; (3) Financial Data: bank account details, payment card information'")
- Recommend exact contact information format with Saudi-specific requirements
- Provide step-by-step implementation guidance
- Include compliance verification steps

**CRITICAL JSON OUTPUT REQUIREMENTS:**
- Return ONLY valid JSON - no comments, no extra text, no markdown
- Use double quotes for all strings
- No trailing commas
- No JavaScript-style comments (//)
- Ensure all brackets and braces are properly closed

**Output Format (Valid JSON Only):**
{
  "sentences": [
    {
      "sentence": "exact meaningful sentence text only",
      "domain": "Specific Domain Name",
      "domainDescription": "Detailed description of domain requirements",
      "isCompliant": true,
      "completenessScore": 85,
      "issues": [
        "Specific issue with PDPL article reference and exact gap identified",
        "Another detailed issue with regulatory requirement missing"
      ],
      "suggestions": [
        "Detailed enhancement with specific implementation steps and exact wording",
        "Another specific suggestion with PDPL compliance guidance"
      ]
    }
  ],
  "overallScore": 75,
  "missingDomains": ["Complete domain names that are entirely missing"],
  "recommendations": [
    "Specific overall recommendation with implementation guidance",
    "Another detailed recommendation with PDPL compliance steps"
  ],
  "complianceLevel": "Good"
}

**CRITICAL REQUIREMENTS:**
1. **RETURN ONLY VALID JSON** - no comments, no extra text, no explanations
2. **BE EXTREMELY COMPREHENSIVE** - extract 90%+ of all meaningful sentences, not just a few
3. **EXTRACT MANY SENTENCES** - aim for 20-50+ sentences for a typical privacy policy
4. **INCLUDE ALL SUBSTANTIVE CONTENT** - data collection, processing, sharing, rights, contact info, etc.
5. **Provide highly detailed issues** with specific PDPL article references and full explanations
6. **Give comprehensive enhancement suggestions** with exact implementation steps and multiple options
7. **Focus on Saudi PDPL compliance** with article-specific requirements and detailed context
8. **Be extremely specific** about what is missing and how to fix it with step-by-step guidance
9. **Include exact wording suggestions** with multiple alternatives and Saudi-specific requirements
10. **Use proper JSON boolean values** (true/false, not "true"/"false")
11. **Use proper JSON number values** (85, not "85")
12. **PRIORITIZE COMPREHENSIVENESS** - better to include too much detail than too little
`;

    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    // Parse JSON response
    let analysis: PolicyAnalysis;
    try {
      // Extract JSON from response if it's wrapped in markdown
      let jsonText = text;

      // Try to extract JSON from markdown code blocks
      const jsonMatch = text.match(/```json\n([\s\S]*?)\n```/) || text.match(/```\n([\s\S]*?)\n```/);
      if (jsonMatch && jsonMatch[1]) {
        jsonText = jsonMatch[1];
      }

      // Clean up the JSON text
      jsonText = jsonText
        .replace(/\/\/.*$/gm, '') // Remove single-line comments
        .replace(/\/\*[\s\S]*?\*\//g, '') // Remove multi-line comments
        .replace(/,\s*}/g, '}') // Remove trailing commas before closing braces
        .replace(/,\s*]/g, ']') // Remove trailing commas before closing brackets
        .trim();

      // Try to find JSON object boundaries if the response contains extra text
      const jsonStart = jsonText.indexOf('{');
      const jsonEnd = jsonText.lastIndexOf('}');

      if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
        jsonText = jsonText.substring(jsonStart, jsonEnd + 1);
      }

      analysis = JSON.parse(jsonText);

      // Validate the analysis structure
      if (!analysis.sentences || !Array.isArray(analysis.sentences)) {
        throw new Error('Invalid analysis structure: missing sentences array');
      }

    } catch (parseError) {
      console.error('JSON parsing error:', parseError);
      console.error('Raw AI response:', text);

      // Try to create a fallback response if JSON parsing fails
      try {
        // Create a basic analysis structure as fallback
        analysis = {
          sentences: [
            {
              sentence: "Analysis failed due to AI response formatting issues",
              domain: "System Error",
              domainDescription: "The AI analysis could not be completed due to response formatting issues",
              isCompliant: false,
              completenessScore: 0,
              issues: ["AI response contained invalid JSON format", "Unable to parse detailed analysis"],
              suggestions: ["Please try re-analyzing the policy", "Contact support if the issue persists"]
            }
          ],
          overallScore: 0,
          missingDomains: ["Analysis incomplete due to parsing error"],
          recommendations: [
            "Re-run the analysis to get proper results",
            "Ensure the privacy policy content is properly formatted"
          ],
          complianceLevel: "Poor" as const
        };

        console.log('Using fallback analysis structure');
      } catch (fallbackError) {
        console.error('Fallback creation failed:', fallbackError);
        return NextResponse.json(
          {
            error: 'Failed to parse AI response and create fallback analysis',
            details: parseError instanceof Error ? parseError.message : 'Unknown parsing error'
          },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({ analysis });

  } catch (error) {
    console.error('Privacy policy analysis error:', error);
    return NextResponse.json(
      { error: 'Failed to analyze privacy policy' },
      { status: 500 }
    );
  }
}
