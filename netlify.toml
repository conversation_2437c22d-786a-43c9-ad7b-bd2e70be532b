[build]
  command = "npm install --legacy-peer-deps && npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18"
  NEXT_TELEMETRY_DISABLED = "1"

[[plugins]]
  package = "@netlify/plugin-nextjs"

# Handle Next.js routing
[[redirects]]
  from = "/_next/static/*"
  to = "/_next/static/:splat"
  status = 200

[[redirects]]
  from = "/_next/data/*"
  to = "/_next/data/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/.netlify/functions/next"
  status = 200
