"use client";

import React, { useState, useEffect } from "react";
import { X, Save, ArrowRight, Database, Users, Edit3 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { SystemData } from "@/Firebase/firestore/SystemsService";

interface PersonalDataGroup {
  id: string;
  name: string;
  description: string;
  attributes: string[];
  purpose: string;
  legalBasis: string;
  retentionPeriod: string;
  dataSubjects: string[];
  createdAt: Date;
  updatedAt: Date;
  attributeReasons?: Array<{
    attributeId: string;
    reason: string;
  }>;
}

interface EditGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
  group: PersonalDataGroup | null;
  allGroups: PersonalDataGroup[];
  personalData: SystemData[];
  onSave: (updatedGroup: PersonalDataGroup, attributeTransfers: Array<{fromGroup: string, toGroup: string, attributeId: string}>) => void;
  lang: string;
}

export function EditGroupModal({
  isOpen,
  onClose,
  group,
  allGroups,
  personalData,
  onSave,
  lang
}: EditGroupModalProps) {
  const [editedGroup, setEditedGroup] = useState<PersonalDataGroup | null>(null);
  const [attributeTransfers, setAttributeTransfers] = useState<Array<{fromGroup: string, toGroup: string, attributeId: string}>>([]);
  const [selectedAttributes, setSelectedAttributes] = useState<Set<string>>(new Set());
  const [targetGroupId, setTargetGroupId] = useState<string>("");
  
  const isRTL = lang === "ar";

  useEffect(() => {
    if (group) {
      setEditedGroup({ ...group });
      setAttributeTransfers([]);
      setSelectedAttributes(new Set());
      setTargetGroupId("");
    }
  }, [group]);

  if (!isOpen || !group || !editedGroup) return null;

  // Create a lookup map for personal data
  const dataLookup = new Map();
  personalData.forEach(item => {
    const key = `${item.tableName}.${item.columnName}`;
    dataLookup.set(key, item);
    dataLookup.set(item.id, item);
  });

  // Get current group attributes with full data
  const currentAttributes = editedGroup.attributes
    .map(attr => dataLookup.get(attr))
    .filter(Boolean);

  const handleAttributeSelect = (attributeKey: string) => {
    const newSelected = new Set(selectedAttributes);
    if (newSelected.has(attributeKey)) {
      newSelected.delete(attributeKey);
    } else {
      newSelected.add(attributeKey);
    }
    setSelectedAttributes(newSelected);
  };

  const handleTransferAttributes = () => {
    if (selectedAttributes.size === 0 || !targetGroupId) return;

    const targetGroup = allGroups.find(g => g.id === targetGroupId);
    if (!targetGroup) return;

    // Create transfer records
    const newTransfers = Array.from(selectedAttributes).map(attrKey => {
      const attr = dataLookup.get(attrKey);
      return {
        fromGroup: editedGroup.id,
        toGroup: targetGroupId,
        attributeId: attr.id
      };
    });

    // Update the edited group by removing transferred attributes
    const remainingAttributes = editedGroup.attributes.filter(attr => {
      const attrData = dataLookup.get(attr);
      return !selectedAttributes.has(attr) && !selectedAttributes.has(attrData?.id);
    });

    setEditedGroup({
      ...editedGroup,
      attributes: remainingAttributes
    });

    setAttributeTransfers([...attributeTransfers, ...newTransfers]);
    setSelectedAttributes(new Set());
    setTargetGroupId("");
  };

  const handleSave = () => {
    if (editedGroup) {
      onSave(editedGroup, attributeTransfers);
      onClose();
    }
  };

  const getPersonalDataTypeColor = (type: string) => {
    switch (type) {
      case 'direct': return 'bg-blue-100 text-blue-800';
      case 'indirect': return 'bg-purple-100 text-purple-800';
      case 'pseudonymous': return 'bg-orange-100 text-orange-800';
      case 'anonymous': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSpecialCategoryColor = (category: string) => {
    switch (category) {
      case 'PII': return 'bg-green-100 text-green-800';
      case 'PHI': return 'bg-red-100 text-red-800';
      case 'PCI': return 'bg-yellow-100 text-yellow-800';
      case 'Genetic': return 'bg-pink-100 text-pink-800';
      case 'Biometric': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                <Edit3 className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">
                  {isRTL ? "تعديل المجموعة" : "Edit Group"}
                </h2>
                <p className="text-white/80">
                  {isRTL ? "تعديل تفاصيل المجموعة ونقل البيانات" : "Edit group details and transfer attributes"}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-white/20 h-10 w-10 p-0"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Group Details */}
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Database className="w-5 h-5 text-[var(--brand-blue)]" />
                  {isRTL ? "تفاصيل المجموعة" : "Group Details"}
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {isRTL ? "اسم المجموعة" : "Group Name"}
                    </label>
                    <Input
                      value={editedGroup.name}
                      onChange={(e) => setEditedGroup({...editedGroup, name: e.target.value})}
                      className="w-full"
                      placeholder={isRTL ? "أدخل اسم المجموعة" : "Enter group name"}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {isRTL ? "وصف المجموعة" : "Group Description"}
                    </label>
                    <Textarea
                      value={editedGroup.description}
                      onChange={(e) => setEditedGroup({...editedGroup, description: e.target.value})}
                      className="w-full min-h-[120px]"
                      placeholder={isRTL ? "أدخل وصف المجموعة" : "Enter group description"}
                    />
                  </div>
                </div>
              </div>

              {/* Transfer Section */}
              {allGroups.length > 1 && (
                <div className="bg-gradient-to-br from-blue-50 to-white rounded-xl p-6 border border-blue-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <ArrowRight className="w-5 h-5 text-[var(--brand-blue)]" />
                    {isRTL ? "نقل البيانات" : "Transfer Attributes"}
                  </h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL ? "نقل إلى المجموعة" : "Transfer to Group"}
                      </label>
                      <select
                        value={targetGroupId}
                        onChange={(e) => setTargetGroupId(e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-transparent"
                      >
                        <option value="">
                          {isRTL ? "اختر المجموعة المستهدفة" : "Select target group"}
                        </option>
                        {allGroups
                          .filter(g => g.id !== editedGroup.id)
                          .map(g => (
                            <option key={g.id} value={g.id}>
                              {g.name} ({g.attributes.length} {isRTL ? "عنصر" : "attributes"})
                            </option>
                          ))
                        }
                      </select>
                    </div>
                    
                    <Button
                      onClick={handleTransferAttributes}
                      disabled={selectedAttributes.size === 0 || !targetGroupId}
                      className="w-full bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
                    >
                      <ArrowRight className="w-4 h-4 mr-2" />
                      {isRTL 
                        ? `نقل ${selectedAttributes.size} عنصر محدد`
                        : `Transfer ${selectedAttributes.size} Selected`
                      }
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Attributes List */}
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Users className="w-5 h-5 text-[var(--brand-blue)]" />
                  {isRTL ? "عناصر المجموعة" : "Group Attributes"}
                  <Badge variant="outline" className="ml-2">
                    {currentAttributes.length}
                  </Badge>
                </h3>
                
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {currentAttributes.map((attr, index) => {
                    const attrKey = `${attr.tableName}.${attr.columnName}`;
                    const isSelected = selectedAttributes.has(attrKey) || selectedAttributes.has(attr.id);
                    
                    return (
                      <div
                        key={index}
                        className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                          isSelected
                            ? 'border-[var(--brand-blue)] bg-[var(--brand-blue)]/5'
                            : 'border-gray-200 hover:border-gray-300 bg-white'
                        }`}
                        onClick={() => handleAttributeSelect(attrKey)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="font-medium text-gray-900">
                                {attr.tableName}.{attr.columnName}
                              </span>
                              {isSelected && (
                                <Badge className="bg-[var(--brand-blue)] text-white text-xs">
                                  {isRTL ? "محدد" : "Selected"}
                                </Badge>
                              )}
                            </div>
                            
                            <div className="flex flex-wrap gap-2 mb-2">
                              <Badge variant="outline" className="text-xs">
                                {attr.dataType}
                              </Badge>
                              {attr.personalDataType && (
                                <Badge className={`text-xs ${getPersonalDataTypeColor(attr.personalDataType)}`}>
                                  {attr.personalDataType}
                                </Badge>
                              )}
                              {attr.specialCategoryType && attr.specialCategoryType !== 'none' && (
                                <Badge className={`text-xs ${getSpecialCategoryColor(attr.specialCategoryType)}`}>
                                  {attr.specialCategoryType}
                                </Badge>
                              )}
                            </div>
                            
                            {attr.personalDataReasoning && (
                              <p className="text-xs text-gray-600 italic">
                                {attr.personalDataReasoning}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* Transfer Summary */}
          {attributeTransfers.length > 0 && (
            <div className="mt-6 p-4 bg-green-50 rounded-xl border border-green-200">
              <h4 className="font-semibold text-green-900 mb-2">
                {isRTL ? "عمليات النقل المجدولة" : "Scheduled Transfers"}
              </h4>
              <div className="space-y-2">
                {attributeTransfers.map((transfer, index) => {
                  const attr = dataLookup.get(transfer.attributeId);
                  const targetGroup = allGroups.find(g => g.id === transfer.toGroup);
                  return (
                    <div key={index} className="flex items-center gap-2 text-sm text-green-800">
                      <span className="font-medium">
                        {attr?.tableName}.{attr?.columnName}
                      </span>
                      <ArrowRight className="w-4 h-4" />
                      <span>{targetGroup?.name}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-6 bg-gray-50">
          <div className="flex items-center justify-end gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="px-6"
            >
              {isRTL ? "إلغاء" : "Cancel"}
            </Button>
            <Button
              onClick={handleSave}
              className="px-6 bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
            >
              <Save className="w-4 h-4 mr-2" />
              {isRTL ? "حفظ التغييرات" : "Save Changes"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
