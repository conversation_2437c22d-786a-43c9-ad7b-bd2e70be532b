"use client";

import React from "react";
import { useFormContext } from "react-hook-form";
import {
  FormField as BaseFormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";

interface FormFieldProps {
  name: string;
  label?: string;
  placeholder?: string;
  description?: string;
  type?: string;
  className?: string;
  required?: boolean;
  autoComplete?: string;
  isCheckbox?: boolean;
  checkboxLabel?: string;
}

export function FormField({
  name,
  label,
  placeholder,
  description,
  type = "text",
  className,
  required = false,
  autoComplete,
  isCheckbox = false,
  checkboxLabel,
}: FormFieldProps) {
  const { control } = useFormContext();

  return (
    <BaseFormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn(isCheckbox && "flex flex-row items-start space-x-3 space-y-0", className)}>
          {!isCheckbox && label && (
            <FormLabel>
              {label}
              {required && <span className="text-destructive ml-1">*</span>}
            </FormLabel>
          )}
          <FormControl>
            {isCheckbox ? (
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  id={name}
                />
                {checkboxLabel && (
                  <label
                    htmlFor={name}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {checkboxLabel}
                  </label>
                )}
              </div>
            ) : (
              <Input
                placeholder={placeholder}
                type={type}
                {...field}
                value={field.value || ""}
                autoComplete={autoComplete}
              />
            )}
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
