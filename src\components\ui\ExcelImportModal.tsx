"use client";

import React, { useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Upload, FileSpreadsheet, CheckCircle, AlertCircle, Download, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import * as XLSX from 'xlsx';
import { SystemData } from "@/Firebase/firestore/SystemsService";

interface ExcelImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (data: Omit<SystemData, 'id' | 'createdAt' | 'systemId'>[], importMode: 'add' | 'replace') => Promise<void>;
  isRTL: boolean;
}

interface ParsedData {
  schemaName?: string;
  tableName: string;
  columnName: string;
  dataType: string;
  maxLength?: number | null;
  isNullable?: boolean | null;
  columnOrder?: number | null;
  lastSeek?: string | null;
  lastScan?: string | null;
  lastLookup?: string | null;
  lastUpdate?: string | null;
  importOrder?: number;
}

export function ExcelImportModal({ isOpen, onClose, onImport, isRTL }: ExcelImportModalProps) {
  const [dragActive, setDragActive] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [parsedData, setParsedData] = useState<ParsedData[]>([]);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [step, setStep] = useState<'upload' | 'preview' | 'confirm'>('upload');
  const [importMode, setImportMode] = useState<'add' | 'replace'>('add');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const resetModal = () => {
    setFile(null);
    setParsedData([]);
    setValidationErrors([]);
    setStep('upload');
    setIsProcessing(false);
    setDragActive(false);
    setImportMode('add');
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const validateData = (data: Record<string, unknown>[]): { valid: ParsedData[], errors: string[] } => {
    const valid: ParsedData[] = [];
    const errors: string[] = [];

    data.forEach((row, index) => {
      const rowNumber = index + 2; // Account for header row
      
      // Check mandatory fields
      const tableNameStr = String(row.TableName || '');
      const columnNameStr = String(row.ColumnName || '');
      const dataTypeStr = String(row.DataType || '');
      
      if (!tableNameStr || tableNameStr.trim() === '') {
        errors.push(`Row ${rowNumber}: Table Name is required`);
        return;
      }
      
      if (!columnNameStr || columnNameStr.trim() === '') {
        errors.push(`Row ${rowNumber}: Column Name is required`);
        return;
      }
      
      if (!dataTypeStr || dataTypeStr.trim() === '') {
        errors.push(`Row ${rowNumber}: Data Type is required`);
        return;
      }

      // Parse the row data
      const parsedRow: ParsedData = {
        tableName: tableNameStr.trim(),
        columnName: columnNameStr.trim(),
        dataType: dataTypeStr.trim(),
        schemaName: row.SchemaName ? String(row.SchemaName).trim() : undefined,
        maxLength: row.MaxLength ? (isNaN(Number(row.MaxLength)) ? null : Number(row.MaxLength)) : null,
        isNullable: row.IsNullable !== undefined ? Boolean(row.IsNullable) : null,
        columnOrder: row.ColumnOrder ? (isNaN(Number(row.ColumnOrder)) ? null : Number(row.ColumnOrder)) : null,
        lastSeek: row.LastSeek ? String(row.LastSeek).trim() : null,
        lastScan: row.LastScan ? String(row.LastScan).trim() : null,
        lastLookup: row.LastLookup ? String(row.LastLookup).trim() : null,
        lastUpdate: row.LastUpdate ? String(row.LastUpdate).trim() : null,
        importOrder: index + 1, // Automatically assign import order based on Excel row sequence
      };

      valid.push(parsedRow);
    });

    return { valid, errors };
  };

  const processFile = async (selectedFile: File) => {
    setIsProcessing(true);
    
    try {
      const data = await selectedFile.arrayBuffer();
      const workbook = XLSX.read(data, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      
      // Convert to JSON with header row
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1,
        defval: '',
        blankrows: false
      });

      if (jsonData.length < 2) {
        throw new Error('Excel file must contain at least a header row and one data row');
      }

      // Get headers and convert to expected format
      const headers = jsonData[0] as string[];
      const dataRows = jsonData.slice(1) as unknown[][];

      // Convert array format to object format
      const objectData = dataRows.map(row => {
        const obj: Record<string, unknown> = {};
        headers.forEach((header, index) => {
          obj[header] = row[index] || '';
        });
        return obj;
      });

      // Validate the data
      const { valid, errors } = validateData(objectData);
      
      if (errors.length > 0) {
        setValidationErrors(errors);
        setStep('preview');
      } else {
        setParsedData(valid);
        setValidationErrors([]);
        setStep('preview');
      }

    } catch (error) {
      toast({
        title: isRTL ? "خطأ في معالجة الملف" : "File Processing Error",
        description: error instanceof Error ? error.message : "Failed to process Excel file",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      const selectedFile = files[0];
      if (selectedFile.type.includes('excel') || selectedFile.type.includes('spreadsheet') || selectedFile.name.endsWith('.xlsx') || selectedFile.name.endsWith('.xls')) {
        setFile(selectedFile);
        processFile(selectedFile);
      } else {
        toast({
          title: isRTL ? "نوع ملف غير صحيح" : "Invalid file type",
          description: isRTL ? "يرجى اختيار ملف Excel" : "Please select an Excel file",
          variant: "destructive",
        });
      }
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      const selectedFile = files[0];
      setFile(selectedFile);
      processFile(selectedFile);
    }
  };

  const handleConfirmImport = async () => {
    if (parsedData.length === 0) return;

    setStep('confirm');
  };

  const handleFinalImport = async () => {
    try {
      setIsProcessing(true);
      await onImport(parsedData, importMode);
      toast({
        title: isRTL ? "تم الاستيراد بنجاح" : "Import Successful",
        description: isRTL ? `تم استيراد ${parsedData.length} صف` : `Successfully imported ${parsedData.length} rows`,
      });
      handleClose();
    } catch (error) {
      toast({
        title: isRTL ? "خطأ في الاستيراد" : "Import Error",
        description: error instanceof Error ? error.message : "Failed to import data",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadTemplate = () => {
    const templateData = [
      ['SchemaName', 'TableName', 'ColumnName', 'DataType', 'MaxLength', 'IsNullable', 'ColumnOrder', 'LastSeek', 'LastScan', 'LastLookup', 'LastUpdate'],
      ['dbo', 'Users', 'ID', 'int', 4, false, 1, '', '', '', ''],
      ['dbo', 'Users', 'Name', 'varchar', 255, false, 2, '', '', '', ''],
      ['dbo', 'Orders', 'OrderID', 'int', 4, false, 1, '', '', '', ''],
    ];

    const ws = XLSX.utils.aoa_to_sheet(templateData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Template');
    XLSX.writeFile(wb, 'system_data_template.xlsx');
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className={`max-w-6xl max-h-[90vh] overflow-hidden ${isRTL ? "rtl" : "ltr"}`}>
        <DialogHeader className="border-b border-gray-100 pb-6">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <div className="w-10 h-10 bg-[var(--brand-blue)]/10 rounded-xl flex items-center justify-center">
                  <FileSpreadsheet className="w-5 h-5 text-[var(--brand-blue)]" />
                </div>
                {isRTL ? "استيراد بيانات Excel" : "Excel Data Import"}
              </DialogTitle>
              <DialogDescription className="text-gray-600 mt-2">
                {isRTL ? "قم بتحميل ملف Excel لاستيراد بيانات النظام" : "Upload an Excel file to import system data"}
              </DialogDescription>
            </div>
            <Button
              onClick={downloadTemplate}
              variant="outline"
              className="border-[var(--brand-blue)] text-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/10"
            >
              <Download className="w-4 h-4 mr-2" />
              {isRTL ? "تحميل القالب" : "Download Template"}
            </Button>
          </div>
        </DialogHeader>

        <div className="overflow-y-auto max-h-[calc(90vh-200px)]">
          <AnimatePresence mode="wait">
            {step === 'upload' && (
              <motion.div
                key="upload"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="p-6"
              >
                <div
                  className={`relative border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-300 ${
                    dragActive 
                      ? 'border-[var(--brand-blue)] bg-[var(--brand-blue)]/5' 
                      : 'border-gray-300 hover:border-[var(--brand-blue)]/50'
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={handleFileSelect}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    disabled={isProcessing}
                  />
                  
                  <div className="space-y-6">
                    <div className="w-24 h-24 bg-[var(--brand-blue)]/10 rounded-3xl flex items-center justify-center mx-auto">
                      <Upload className="w-12 h-12 text-[var(--brand-blue)]" />
                    </div>
                    
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {isRTL ? "اسحب ملف Excel هنا أو انقر للاختيار" : "Drag Excel file here or click to select"}
                      </h3>
                      <p className="text-gray-600">
                        {isRTL ? "الحقول الإجبارية: اسم الجدول، اسم العمود، نوع البيانات" : "Required fields: Table Name, Column Name, Data Type"}
                      </p>
                    </div>

                    {isProcessing && (
                      <div className="flex items-center justify-center gap-2 text-[var(--brand-blue)]">
                        <div className="w-4 h-4 border-2 border-[var(--brand-blue)] border-t-transparent rounded-full animate-spin"></div>
                        <span>{isRTL ? "جاري المعالجة..." : "Processing..."}</span>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            )}

            {step === 'preview' && (
              <motion.div
                key="preview"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="p-6 space-y-6"
              >
                {/* File Info */}
                <div className="bg-gray-50 rounded-xl p-4 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                      <FileSpreadsheet className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">{file?.name}</p>
                      <p className="text-sm text-gray-600">
                        {isRTL ? `${parsedData.length} صف صالح` : `${parsedData.length} valid rows`}
                        {validationErrors.length > 0 && (
                          <span className="text-red-600 ml-2">
                            {isRTL ? `، ${validationErrors.length} خطأ` : `, ${validationErrors.length} errors`}
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                  <Button
                    onClick={() => setStep('upload')}
                    variant="outline"
                    size="sm"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    {isRTL ? "إزالة" : "Remove"}
                  </Button>
                </div>

                {/* Validation Errors */}
                {validationErrors.length > 0 && (
                  <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <AlertCircle className="w-5 h-5 text-red-600" />
                      <h4 className="font-semibold text-red-900">
                        {isRTL ? "أخطاء التحقق" : "Validation Errors"}
                      </h4>
                    </div>
                    <ul className="space-y-1 text-sm text-red-700 max-h-32 overflow-y-auto">
                      {validationErrors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Data Preview */}
                {parsedData.length > 0 && (
                  <div className="bg-white border border-gray-200 rounded-xl overflow-hidden">
                    <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                      <h4 className="font-semibold text-gray-900">
                        {isRTL ? "معاينة البيانات" : "Data Preview"}
                      </h4>
                    </div>
                    <div className="overflow-x-auto max-h-64 overflow-y-auto">
                      <table className="w-full text-sm">
                        <thead className="bg-gray-50 sticky top-0">
                          <tr>
                            <th className="px-3 py-2 text-left font-medium text-gray-700 border-b border-gray-200">
                              {isRTL ? "اسم الجدول" : "Table Name"}
                            </th>
                            <th className="px-3 py-2 text-left font-medium text-gray-700 border-b border-gray-200">
                              {isRTL ? "اسم العمود" : "Column Name"}
                            </th>
                            <th className="px-3 py-2 text-left font-medium text-gray-700 border-b border-gray-200">
                              {isRTL ? "نوع البيانات" : "Data Type"}
                            </th>
                            <th className="px-3 py-2 text-left font-medium text-gray-700 border-b border-gray-200">
                              {isRTL ? "اسم المخطط" : "Schema Name"}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {parsedData.slice(0, 10).map((row, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="px-3 py-2 text-gray-900 border-b border-gray-100">{row.tableName}</td>
                              <td className="px-3 py-2 text-gray-900 border-b border-gray-100">{row.columnName}</td>
                              <td className="px-3 py-2 text-gray-900 border-b border-gray-100">{row.dataType}</td>
                              <td className="px-3 py-2 text-gray-600 border-b border-gray-100">{row.schemaName || '-'}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      {parsedData.length > 10 && (
                        <div className="p-3 text-center text-gray-600 bg-gray-50">
                          {isRTL ? `و ${parsedData.length - 10} صف آخر...` : `And ${parsedData.length - 10} more rows...`}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Import Mode Selection */}
                {parsedData.length > 0 && validationErrors.length === 0 && (
                  <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                    <h4 className="font-semibold text-blue-900 mb-3">
                      {isRTL ? "وضع الاستيراد" : "Import Mode"}
                    </h4>
                    <div className="space-y-3">
                      <label className="flex items-start gap-3 cursor-pointer">
                        <input
                          type="radio"
                          name="importMode"
                          value="add"
                          checked={importMode === 'add'}
                          onChange={(e) => setImportMode(e.target.value as 'add' | 'replace')}
                          className="mt-1 w-4 h-4 text-[var(--brand-blue)] border-gray-300 focus:ring-[var(--brand-blue)]"
                        />
                        <div>
                          <div className="font-medium text-blue-900">
                            {isRTL ? "إضافة إلى البيانات الموجودة" : "Add to existing data"}
                          </div>
                          <div className="text-sm text-blue-700">
                            {isRTL
                              ? "سيتم إضافة البيانات الجديدة إلى البيانات الموجودة حالياً"
                              : "New data will be added to the existing data"
                            }
                          </div>
                        </div>
                      </label>
                      <label className="flex items-start gap-3 cursor-pointer">
                        <input
                          type="radio"
                          name="importMode"
                          value="replace"
                          checked={importMode === 'replace'}
                          onChange={(e) => setImportMode(e.target.value as 'add' | 'replace')}
                          className="mt-1 w-4 h-4 text-[var(--brand-blue)] border-gray-300 focus:ring-[var(--brand-blue)]"
                        />
                        <div>
                          <div className="font-medium text-blue-900">
                            {isRTL ? "استبدال البيانات الموجودة" : "Replace existing data"}
                          </div>
                          <div className="text-sm text-blue-700">
                            {isRTL
                              ? "سيتم حذف جميع البيانات الموجودة واستبدالها بالبيانات الجديدة"
                              : "All existing data will be deleted and replaced with new data"
                            }
                          </div>
                        </div>
                      </label>
                    </div>
                  </div>
                )}
              </motion.div>
            )}

            {step === 'confirm' && (
              <motion.div
                key="confirm"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="p-6 text-center space-y-6"
              >
                <div className="w-24 h-24 bg-[var(--brand-blue)]/10 rounded-3xl flex items-center justify-center mx-auto">
                  <CheckCircle className="w-12 h-12 text-[var(--brand-blue)]" />
                </div>
                
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {isRTL ? "تأكيد الاستيراد" : "Confirm Import"}
                  </h3>
                  <p className="text-gray-600">
                    {isRTL 
                      ? `هل أنت متأكد من استيراد ${parsedData.length} صف إلى قاعدة البيانات؟` 
                      : `Are you sure you want to import ${parsedData.length} rows to the database?`
                    }
                  </p>
                </div>

                <div className="bg-gray-50 rounded-xl p-6 text-left max-w-md mx-auto">
                  <h4 className="font-semibold text-gray-900 mb-3">
                    {isRTL ? "ملخص الاستيراد" : "Import Summary"}
                  </h4>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex justify-between">
                      <span>{isRTL ? "إجمالي الصفوف:" : "Total rows:"}</span>
                      <span className="font-medium text-gray-900">{parsedData.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>{isRTL ? "اسم الملف:" : "File name:"}</span>
                      <span className="font-medium text-gray-900 truncate max-w-32">{file?.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>{isRTL ? "الحقول الإجبارية:" : "Required fields:"}</span>
                      <span className="font-medium text-green-600">{isRTL ? "مكتملة" : "Complete"}</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Footer Actions */}
        <div className="border-t border-gray-100 pt-6 flex justify-between">
          <Button onClick={handleClose} variant="outline">
            {isRTL ? "إلغاء" : "Cancel"}
          </Button>
          
          <div className="flex gap-3">
            {step === 'preview' && validationErrors.length === 0 && parsedData.length > 0 && (
              <Button 
                onClick={handleConfirmImport}
                className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
              >
                {isRTL ? "المتابعة" : "Continue"}
              </Button>
            )}
            
            {step === 'confirm' && (
              <>
                <Button 
                  onClick={() => setStep('preview')}
                  variant="outline"
                >
                  {isRTL ? "العودة" : "Back"}
                </Button>
                <Button 
                  onClick={handleFinalImport}
                  disabled={isProcessing}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  {isProcessing ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      {isRTL ? "جاري الاستيراد..." : "Importing..."}
                    </div>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      {isRTL ? "تأكيد الاستيراد" : "Confirm Import"}
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 