import { Locale } from "@/i18n-config";
import { getDictionary } from "@/dictionaries";
import UserManagementClient from "./UserManagementClient";

interface UserManagementPageProps {
  params: Promise<{
    lang: Locale;
  }>;
}

export default async function UserManagementPage({ params }: UserManagementPageProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return <UserManagementClient dict={dict} lang={lang} />;
}
