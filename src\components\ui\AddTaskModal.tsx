"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  X, 
  Plus, 
  User, 
  Calendar, 
  AlertCircle, 
  Tag, 
  FileText, 
  Users, 
  Clock,
  CheckSquare
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { TaskPriority, TaskAssignee } from "@/Firebase/firestore/services/TasksService";
import { getAllUsers, UserProfile, UserRole } from "@/Firebase/firestore/services/UserService";

interface AddTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (taskData: {
    title: string;
    description: string;
    priority: TaskPriority;
    assignees: TaskAssignee[];
    dueDate?: Date;
    tags?: string[];
  }) => Promise<void>;
  isRTL: boolean;
  isLoading?: boolean;
}

export function AddTaskModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  isRTL, 
  isLoading = false 
}: AddTaskModalProps) {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    priority: TaskPriority.MEDIUM,
    dueDate: "",
    tags: ""
  });
  
  const [errors, setErrors] = useState<{title?: string; description?: string}>({});
  const [selectedAssignees, setSelectedAssignees] = useState<TaskAssignee[]>([]);
  const [availableUsers, setAvailableUsers] = useState<UserProfile[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  
  const { toast } = useToast();

  const loadUsers = useCallback(async () => {
    try {
      setIsLoadingUsers(true);
      const users = await getAllUsers();
      setAvailableUsers(users);
    } catch (error) {
      console.error('Error loading users:', error);
      toast({
        title: isRTL ? "خطأ في تحميل المستخدمين" : "Error loading users",
        description: isRTL ? "فشل في تحميل قائمة المستخدمين" : "Failed to load users list",
        variant: "destructive",
      });
    } finally {
      setIsLoadingUsers(false);
    }
  }, [isRTL, toast]);

  useEffect(() => {
    if (isOpen) {
      loadUsers();
    }
  }, [isOpen, loadUsers]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleAddAssignee = (user: UserProfile) => {
    const assignee: TaskAssignee = {
      uid: user.uid,
      displayName: user.displayName || user.email || "Unknown",
      email: user.email || "",
      role: user.role
    };

    if (!selectedAssignees.find(a => a.uid === user.uid)) {
      setSelectedAssignees(prev => [...prev, assignee]);
    }
    setShowUserDropdown(false);
  };

  const handleRemoveAssignee = (uid: string) => {
    setSelectedAssignees(prev => prev.filter(a => a.uid !== uid));
  };

  const validateForm = (): boolean => {
    const newErrors: {title?: string; description?: string} = {};

    if (!formData.title.trim()) {
      newErrors.title = isRTL ? "عنوان المهمة مطلوب" : "Task title is required";
    } else if (formData.title.length < 3) {
      newErrors.title = isRTL ? "يجب أن يكون العنوان 3 أحرف على الأقل" : "Title must be at least 3 characters";
    }

    if (!formData.description.trim()) {
      newErrors.description = isRTL ? "وصف المهمة مطلوب" : "Task description is required";
    } else if (formData.description.length < 10) {
      newErrors.description = isRTL ? "يجب أن يكون الوصف 10 أحرف على الأقل" : "Description must be at least 10 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const taskData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        priority: formData.priority,
        assignees: selectedAssignees,
        dueDate: formData.dueDate ? new Date(formData.dueDate) : undefined,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean) : undefined
      };

      await onSubmit(taskData);
      handleClose();
    } catch (error) {
      console.error('Error creating task:', error);
      toast({
        title: isRTL ? "خطأ في إنشاء المهمة" : "Error creating task",
        description: isRTL ? "فشل في إنشاء المهمة" : "Failed to create task",
        variant: "destructive",
      });
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        title: "",
        description: "",
        priority: TaskPriority.MEDIUM,
        dueDate: "",
        tags: ""
      });
      setSelectedAssignees([]);
      setErrors({});
      setShowUserDropdown(false);
      onClose();
    }
  };

  const priorityOptions = [
    { value: TaskPriority.LOW, label: isRTL ? "منخفضة" : "Low", color: "text-green-600 bg-green-50" },
    { value: TaskPriority.MEDIUM, label: isRTL ? "متوسطة" : "Medium", color: "text-yellow-600 bg-yellow-50" },
    { value: TaskPriority.HIGH, label: isRTL ? "عالية" : "High", color: "text-orange-600 bg-orange-50" },
    { value: TaskPriority.URGENT, label: isRTL ? "عاجلة" : "Urgent", color: "text-red-600 bg-red-50" }
  ];

  const availableUsersForSelection = availableUsers.filter(
    user => !selectedAssignees.find(a => a.uid === user.uid)
  );

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-gray-900/20 backdrop-blur-sm"
            onClick={handleClose}
          />

          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className={`relative bg-white rounded-3xl shadow-2xl border border-gray-100 w-full max-w-2xl max-h-[90vh] overflow-hidden ${isRTL ? "rtl" : "ltr"}`}
          >
            <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 px-8 py-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center shadow-lg border border-white/20">
                    <Plus className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">
                      {isRTL ? "إضافة مهمة جديدة" : "Add New Task"}
                    </h2>
                    <p className="text-white/80 text-sm">
                      {isRTL ? "إنشاء وتخصيص مهمة جديدة" : "Create and assign a new task"}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleClose}
                  className="text-white hover:bg-white/20 rounded-xl"
                  disabled={isLoading}
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>
            </div>

            <div className="overflow-y-auto max-h-[calc(90vh-160px)]">
              <form onSubmit={handleSubmit} className="p-8 space-y-6">
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    <CheckSquare className="w-4 h-4 text-[var(--brand-blue)]" />
                    {isRTL ? "عنوان المهمة" : "Task Title"}
                  </Label>
                  <Input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange("title", e.target.value)}
                    className={`text-lg font-medium ${errors.title ? "border-red-500 focus:border-red-500" : "border-gray-200 focus:border-[var(--brand-blue)]"}`}
                    placeholder={isRTL ? "أدخل عنوان المهمة" : "Enter task title"}
                    disabled={isLoading}
                  />
                  {errors.title && (
                    <p className="text-sm text-red-600 flex items-center gap-2">
                      <AlertCircle className="w-4 h-4" />
                      {errors.title}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    <FileText className="w-4 h-4 text-[var(--brand-blue)]" />
                    {isRTL ? "وصف المهمة" : "Task Description"}
                  </Label>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    className={`min-h-32 resize-none ${errors.description ? "border-red-500 focus:border-red-500" : "border-gray-200 focus:border-[var(--brand-blue)]"}`}
                    placeholder={isRTL ? "اكتب وصفاً مفصلاً للمهمة" : "Write a detailed description of the task"}
                    disabled={isLoading}
                  />
                  {errors.description && (
                    <p className="text-sm text-red-600 flex items-center gap-2">
                      <AlertCircle className="w-4 h-4" />
                      {errors.description}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                      <AlertCircle className="w-4 h-4 text-[var(--brand-blue)]" />
                      {isRTL ? "الأولوية" : "Priority"}
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      {priorityOptions.map((option) => (
                        <button
                          key={option.value}
                          type="button"
                          onClick={() => handleInputChange("priority", option.value)}
                          className={`p-3 rounded-xl border-2 transition-all duration-200 text-sm font-medium ${
                            formData.priority === option.value
                              ? `${option.color} border-current`
                              : 'border-gray-200 text-gray-600 hover:border-gray-300 hover:bg-gray-50'
                          }`}
                          disabled={isLoading}
                        >
                          {option.label}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                      <Calendar className="w-4 h-4 text-[var(--brand-blue)]" />
                      {isRTL ? "تاريخ الاستحقاق" : "Due Date"}
                    </Label>
                    <Input
                      type="datetime-local"
                      value={formData.dueDate}
                      onChange={(e) => handleInputChange("dueDate", e.target.value)}
                      className="border-gray-200 focus:border-[var(--brand-blue)]"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <Label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    <Users className="w-4 h-4 text-[var(--brand-blue)]" />
                    {isRTL ? "المُكلفون" : "Assignees"}
                  </Label>
                  
                  {selectedAssignees.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-3">
                      {selectedAssignees.map((assignee) => (
                        <div
                          key={assignee.uid}
                          className="bg-[var(--brand-blue)]/10 border border-[var(--brand-blue)]/20 rounded-xl px-3 py-2 flex items-center gap-2"
                        >
                          <User className="w-4 h-4 text-[var(--brand-blue)]" />
                          <span className="text-sm font-medium text-gray-800">
                            {assignee.displayName || assignee.email || "Unknown"}
                          </span>
                          <span className="text-xs text-gray-500 bg-white rounded-md px-2 py-1">
                            {assignee.role === UserRole.CONSULTANT ? (isRTL ? "استشاري" : "Consultant") : assignee.role}
                          </span>
                          <button
                            type="button"
                            onClick={() => handleRemoveAssignee(assignee.uid)}
                            className="text-red-500 hover:text-red-700 ml-1"
                            disabled={isLoading}
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="relative">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowUserDropdown(!showUserDropdown)}
                      className="w-full justify-start border-gray-200 hover:border-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/5"
                      disabled={isLoading || isLoadingUsers}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      {isRTL ? "إضافة مُكلف" : "Add Assignee"}
                    </Button>

                    {showUserDropdown && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="absolute top-full left-0 right-0 z-10 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg max-h-48 overflow-y-auto"
                      >
                        {isLoadingUsers ? (
                          <div className="p-4 text-center text-gray-500">
                            <Clock className="w-4 h-4 animate-spin mx-auto mb-2" />
                            {isRTL ? "جاري التحميل..." : "Loading..."}
                          </div>
                        ) : availableUsersForSelection.length === 0 ? (
                          <div className="p-4 text-center text-gray-500">
                            {isRTL ? "لا توجد مستخدمون متاحون" : "No available users"}
                          </div>
                        ) : (
                          availableUsersForSelection.map((user) => (
                            <button
                              key={user.uid}
                              type="button"
                              onClick={() => handleAddAssignee(user)}
                              className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center gap-3 border-b border-gray-100 last:border-b-0"
                            >
                              <div className="w-8 h-8 bg-[var(--brand-blue)]/10 rounded-full flex items-center justify-center">
                                <User className="w-4 h-4 text-[var(--brand-blue)]" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="font-medium text-gray-900 truncate">
                                  {user.displayName || user.email}
                                </div>
                                <div className="text-xs text-gray-500 flex items-center gap-2">
                                  <span className={`px-2 py-1 rounded-md ${
                                    user.role === UserRole.CONSULTANT 
                                      ? 'bg-blue-100 text-blue-700' 
                                      : 'bg-gray-100 text-gray-600'
                                  }`}>
                                    {user.role === UserRole.CONSULTANT ? (isRTL ? "استشاري" : "Consultant") : user.role}
                                  </span>
                                </div>
                              </div>
                            </button>
                          ))
                        )}
                      </motion.div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    <Tag className="w-4 h-4 text-[var(--brand-blue)]" />
                    {isRTL ? "العلامات" : "Tags"}
                  </Label>
                  <Input
                    type="text"
                    value={formData.tags}
                    onChange={(e) => handleInputChange("tags", e.target.value)}
                    className="border-gray-200 focus:border-[var(--brand-blue)]"
                    placeholder={isRTL ? "أدخل العلامات مفصولة بفواصل" : "Enter tags separated by commas"}
                    disabled={isLoading}
                  />
                  <p className="text-xs text-gray-500">
                    {isRTL ? "مثال: تطوير، مراجعة، تحليل" : "Example: development, review, analysis"}
                  </p>
                </div>

                <div className="flex gap-4 pt-6 border-t border-gray-100">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    className="flex-1 border-gray-200 hover:bg-gray-50"
                    disabled={isLoading}
                  >
                    {isRTL ? "إلغاء" : "Cancel"}
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1 bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 hover:from-[var(--brand-blue)]/90 hover:to-[var(--brand-blue)]/70 text-white shadow-lg"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        {isRTL ? "جاري الإنشاء..." : "Creating..."}
                      </div>
                    ) : (
                      <>
                        <CheckSquare className="w-4 h-4 mr-2" />
                        {isRTL ? "إنشاء المهمة" : "Create Task"}
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
} 