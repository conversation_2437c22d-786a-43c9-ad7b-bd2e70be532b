"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { X, CalendarDays, Save } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Meeting, MeetingsService } from "@/Firebase/firestore/services/MeetingsService";
import { useToast } from "@/components/ui/use-toast";
import { Timestamp } from "firebase/firestore";

interface EditMeetingModalProps {
  isOpen: boolean;
  onClose: () => void;
  meeting: Meeting;
  onUpdate: () => void;
  isRTL: boolean;
}

export function EditMeetingModal({ isOpen, onClose, meeting, onUpdate, isRTL }: EditMeetingModalProps) {
  const [formData, setFormData] = useState({
    title: meeting.title,
    description: meeting.description || "",
    meetingDate: meeting.meetingDate.toDate().toISOString().split('T')[0],
    meetingTime: meeting.meetingDate.toDate().toTimeString().split(' ')[0].substring(0, 5),
    duration: meeting.duration,
    location: meeting.location || "",
    meetingType: meeting.meetingType,
    status: meeting.status,
    agenda: meeting.agenda || [],
    notes: meeting.notes || ""
  });
  

  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        title: meeting.title,
        description: meeting.description || "",
        meetingDate: meeting.meetingDate.toDate().toISOString().split('T')[0],
        meetingTime: meeting.meetingDate.toDate().toTimeString().split(' ')[0].substring(0, 5),
        duration: meeting.duration,
        location: meeting.location || "",
        meetingType: meeting.meetingType,
        status: meeting.status,
        agenda: meeting.agenda || [],
        notes: meeting.notes || ""
      });
    }
  }, [isOpen, meeting]);

  const handleInputChange = (field: string, value: string | number | Date) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.meetingDate || !formData.meetingTime) {
      return;
    }

    setIsLoading(true);
    
    try {
      // Combine date and time
      const meetingDateTime = new Date(`${formData.meetingDate}T${formData.meetingTime}`);
      
      const updateData = {
        title: formData.title,
        description: formData.description,
        meetingDate: Timestamp.fromDate(meetingDateTime),
        duration: formData.duration,
        location: formData.location,
        meetingType: formData.meetingType,
        status: formData.status,
        attendees: meeting.attendees,
        agenda: formData.agenda,
        notes: formData.notes
      };

      await MeetingsService.updateMeeting(meeting.id!, updateData);
      onUpdate();
      onClose();
      
      toast({
        title: isRTL ? "تم تحديث الاجتماع" : "Meeting Updated",
        description: isRTL ? "تم تحديث الاجتماع بنجاح" : "Meeting has been updated successfully",
      });
    } catch (error) {
      console.error("Error updating meeting:", error);
      toast({
        title: isRTL ? "خطأ في التحديث" : "Error Updating",
        description: isRTL ? "حدث خطأ أثناء تحديث الاجتماع" : "An error occurred while updating the meeting",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-gray-900/20 backdrop-blur-sm"
        onClick={onClose}
      />
      
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="relative bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
            <div className="w-10 h-10 bg-[var(--brand-blue)]/10 rounded-xl flex items-center justify-center">
              <CalendarDays className="w-5 h-5 text-[var(--brand-blue)]" />
            </div>
            {isRTL ? "تحرير الاجتماع" : "Edit Meeting"}
          </h2>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "عنوان الاجتماع" : "Meeting Title"} *
                </label>
                <Input
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder={isRTL ? "أدخل عنوان الاجتماع" : "Enter meeting title"}
                  required
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "الوصف" : "Description"}
                </label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder={isRTL ? "وصف الاجتماع" : "Meeting description"}
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "التاريخ" : "Date"} *
                </label>
                <Input
                  type="date"
                  value={formData.meetingDate}
                  onChange={(e) => handleInputChange('meetingDate', e.target.value)}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "الوقت" : "Time"} *
                </label>
                <Input
                  type="time"
                  value={formData.meetingTime}
                  onChange={(e) => handleInputChange('meetingTime', e.target.value)}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "المدة (بالدقائق)" : "Duration (minutes)"}
                </label>
                <Input
                  type="number"
                  value={formData.duration}
                  onChange={(e) => handleInputChange('duration', parseInt(e.target.value))}
                  min="15"
                  step="15"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "الموقع" : "Location"}
                </label>
                <Input
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  placeholder={isRTL ? "موقع الاجتماع" : "Meeting location"}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "نوع الاجتماع" : "Meeting Type"}
                </label>
                <Select value={formData.meetingType} onValueChange={(value) => handleInputChange('meetingType', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="internal">{isRTL ? "داخلي" : "Internal"}</SelectItem>
                    <SelectItem value="client">{isRTL ? "عميل" : "Client"}</SelectItem>
                    <SelectItem value="vendor">{isRTL ? "مورد" : "Vendor"}</SelectItem>
                    <SelectItem value="stakeholder">{isRTL ? "أصحاب مصلحة" : "Stakeholder"}</SelectItem>
                    <SelectItem value="review">{isRTL ? "مراجعة" : "Review"}</SelectItem>
                    <SelectItem value="other">{isRTL ? "أخرى" : "Other"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "الحالة" : "Status"}
                </label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="scheduled">{isRTL ? "مجدولة" : "Scheduled"}</SelectItem>
                    <SelectItem value="in_progress">{isRTL ? "جارية" : "In Progress"}</SelectItem>
                    <SelectItem value="completed">{isRTL ? "مكتملة" : "Completed"}</SelectItem>
                    <SelectItem value="cancelled">{isRTL ? "ملغية" : "Cancelled"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
          <Button variant="outline" onClick={onClose}>
            {isRTL ? "إلغاء" : "Cancel"}
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={isLoading || !formData.title || !formData.meetingDate || !formData.meetingTime}
            className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {isRTL ? "حفظ التغييرات" : "Save Changes"}
          </Button>
        </div>
      </motion.div>
    </div>
  );
}
