"use client";

import React, { useState } from "react";
import { Plus, Edit, Trash2, MessageSquare, Calendar } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { SystemsService, SystemContextPoint } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";
import { AddContextPointModal } from "./AddContextPointModal";

interface SystemContextPointsProps {
  systemId: string;
  lang: string;
  contextPoints: SystemContextPoint[];
  onContextUpdate: () => void;
}

export function SystemContextPoints({ systemId, lang, contextPoints, onContextUpdate }: SystemContextPointsProps) {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingPoint, setEditingPoint] = useState<SystemContextPoint | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [deleteConfirmPoint, setDeleteConfirmPoint] = useState<SystemContextPoint | null>(null);

  const { toast } = useToast();
  const isRTL = lang === "ar";

  const handleDeleteClick = (point: SystemContextPoint) => {
    setDeleteConfirmPoint(point);
  };

  const handleConfirmDelete = async () => {
    if (!deleteConfirmPoint?.id) return;

    try {
      setIsDeleting(deleteConfirmPoint.id);
      await SystemsService.deleteSystemContextPoint(systemId, deleteConfirmPoint.id);

      toast({
        title: isRTL ? "تم الحذف" : "Deleted",
        description: isRTL ? "تم حذف نقطة السياق بنجاح" : "Context point deleted successfully",
      });

      onContextUpdate();
      setDeleteConfirmPoint(null);
    } catch (error) {
      console.error('Error deleting context point:', error);
      toast({
        title: isRTL ? "خطأ في الحذف" : "Delete Error",
        description: isRTL ? "فشل في حذف نقطة السياق" : "Failed to delete context point",
        variant: "destructive"
      });
    } finally {
      setIsDeleting(null);
    }
  };

  const handleEditPoint = (point: SystemContextPoint) => {
    setEditingPoint(point);
    setIsAddModalOpen(true);
  };

  const handleModalClose = () => {
    setIsAddModalOpen(false);
    setEditingPoint(null);
  };

  const getTagColor = (tag: string) => {
    const colors = {
      'System Description': 'bg-blue-100 text-blue-800',
      'System Personas': 'bg-green-100 text-green-800',
      'System Service Brief': 'bg-purple-100 text-purple-800'
    };
    return colors[tag as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">
            {isRTL ? "نقاط السياق" : "Context Points"}
          </h3>
          <p className="text-gray-600">
            {isRTL 
              ? "إدارة النقاط المهمة في سياق النظام" 
              : "Manage important points in system context"
            }
          </p>
        </div>
        <Button
          onClick={() => setIsAddModalOpen(true)}
          className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          {isRTL ? "إضافة نقطة" : "Add Point"}
        </Button>
      </div>

      {/* Context Points Grid */}
      {contextPoints.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {contextPoints.map((point) => (
            <Card key={point.id} className="hover:shadow-lg transition-shadow duration-300">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="w-4 h-4 text-[var(--brand-blue)]" />
                    <Badge className={getTagColor(point.tag)}>
                      {point.tag}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditPoint(point)}
                      className="h-8 w-8 p-0 hover:bg-gray-100"
                    >
                      <Edit className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteClick(point)}
                      disabled={isDeleting === point.id}
                      className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 text-sm leading-relaxed mb-3">
                  {point.content}
                </p>
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <Calendar className="w-3 h-3" />
                  {point.createdAt?.toDate?.()?.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') || 'N/A'}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <MessageSquare className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {isRTL ? "لا توجد نقاط سياق" : "No Context Points"}
          </h3>
          <p className="text-gray-600 mb-4">
            {isRTL 
              ? "ابدأ بإضافة نقاط مهمة حول سياق النظام" 
              : "Start by adding important points about the system context"
            }
          </p>
          <Button
            onClick={() => setIsAddModalOpen(true)}
            className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            {isRTL ? "إضافة نقطة سياق" : "Add Context Point"}
          </Button>
        </div>
      )}

      {/* Add/Edit Context Point Modal */}
      <AddContextPointModal
        isOpen={isAddModalOpen}
        onClose={handleModalClose}
        systemId={systemId}
        lang={lang}
        editingPoint={editingPoint}
        onSuccess={() => {
          onContextUpdate();
          handleModalClose();
        }}
      />

      {/* Delete Confirmation Modal */}
      {deleteConfirmPoint && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 rounded-lg bg-red-100">
                  <Trash2 className="w-5 h-5 text-red-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {isRTL ? "تأكيد الحذف" : "Confirm Deletion"}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {isRTL ? "هذا الإجراء لا يمكن التراجع عنه" : "This action cannot be undone"}
                  </p>
                </div>
              </div>

              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Badge className={getTagColor(deleteConfirmPoint.tag)}>
                    {deleteConfirmPoint.tag}
                  </Badge>
                </div>
                <p className="text-sm text-gray-700">
                  {deleteConfirmPoint.content}
                </p>
              </div>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => setDeleteConfirmPoint(null)}
                  className="flex-1"
                  disabled={isDeleting === deleteConfirmPoint.id}
                >
                  {isRTL ? "إلغاء" : "Cancel"}
                </Button>
                <Button
                  onClick={handleConfirmDelete}
                  disabled={isDeleting === deleteConfirmPoint.id}
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white"
                >
                  {isDeleting === deleteConfirmPoint.id
                    ? (isRTL ? "جاري الحذف..." : "Deleting...")
                    : (isRTL ? "حذف" : "Delete")
                  }
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
