"use client";

import React, { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { auth, logoutUser } from "@/Firebase/Authentication/authConfig";
import { getUserProfile, UserRole } from "@/Firebase/firestore/services/UserService";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { i18n, type Locale } from '@/i18n-config';
import { UserProfileModal } from "@/components/ui/UserProfileModal";
import {
  ChevronLeft,
  ChevronRight,
  Home,
  User,
  Users,
  LogOut,
  Menu,
  Globe,
  Bell,
  Shield,
  ClipboardList,
  FileSearch,
  Calendar,
  CalendarDays,
  BookOpen
} from "lucide-react";

interface ThiqahLayoutClientProps {
  children: React.ReactNode;
  lang: Locale;
}

export default function ThiqahLayoutClient({ children, lang }: ThiqahLayoutClientProps) {
  const router = useRouter();
  const { toast } = useToast();
  const pathname = usePathname();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isLanguageMenuOpen, setIsLanguageMenuOpen] = useState(false);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [userName, setUserName] = useState<string | null>(null);
  const [userDisplayName, setUserDisplayName] = useState<string | null>(null);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [userPhotoURL, setUserPhotoURL] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  
  // Get user info
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (user) => {
      if (user) {
        setUserName(user.email?.split('@')[0] || 'User');
        setUserEmail(user.email);
        setUserPhotoURL(user.photoURL);

        // Get user role and display name from Firestore
        try {
          const userProfile = await getUserProfile(user.uid);
          setUserRole(userProfile?.role || null);
          setUserDisplayName(userProfile?.displayName || null);
        } catch (error) {
          console.error("Error fetching user profile:", error);
        }
      } else {
        // If no user is logged in, redirect to sign in
        router.push(`/${lang}/auth/signin`);
      }
    });

    return () => unsubscribe();
  }, [router, lang]);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const toggleLanguageMenu = () => {
    setIsLanguageMenuOpen(!isLanguageMenuOpen);
  };

  const getPathWithLocale = (locale: Locale) => {
    if (!pathname) return '/';
    const segments = pathname.split('/');
    segments[1] = locale;
    return segments.join('/');
  };

  const handleLogout = async () => {
    try {
      await logoutUser();
      toast({
        title: lang === 'ar' ? "تم تسجيل الخروج بنجاح" : "Logged out successfully",
        variant: "default",
      });
      router.push(`/${lang}/auth/signin`);
    } catch {
      toast({
        title: lang === 'ar' ? "خطأ في تسجيل الخروج" : "Error signing out",
        description: lang === 'ar' ? "حدث خطأ أثناء تسجيل الخروج" : "An error occurred while signing out",
        variant: "destructive",
      });
    }
  };

  const handleNameUpdate = (newName: string | null) => {
    // Update the display name that shows in the clickable area
    setUserDisplayName(newName);
    // Keep userName as email prefix for other uses
    setUserName(auth.currentUser?.email?.split('@')[0] || 'User');
  };

  const menuItems = [
    {
      name: lang === 'ar' ? "الرئيسية" : "Dashboard",
      icon: <Home size={20} />,
      path: `/${lang}/Thiqah/Home`
    },
    {
      name: lang === 'ar' ? "تصنيف البيانات" : "Data Classification",
      icon: <Shield size={20} />,
      path: `/${lang}/Thiqah/DataClassification`
    },
    {
      name: lang === 'ar' ? "المهام" : "Tasks",
      icon: <ClipboardList size={20} />,
      path: `/${lang}/Thiqah/Tasks`
    },
    {
      name: lang === 'ar' ? "التقدم" : "Progress",
      icon: <Calendar size={20} />,
      path: `/${lang}/Thiqah/Progress`
    },
    {
      name: lang === 'ar' ? "تقويم الاجتماعات" : "Meetings Calendar",
      icon: <CalendarDays size={20} />,
      path: `/${lang}/Thiqah/Meetings`
    },
    {
      name: lang === 'ar' ? "النتائج الرقابية" : "Audit Findings",
      icon: <FileSearch size={20} />,
      path: `/${lang}/Thiqah/AuditFindings`
    },
    {
      name: lang === 'ar' ? "المراجع" : "References",
      icon: <BookOpen size={20} />,
      path: `/${lang}/Thiqah/References`
    },
    ...(userRole === UserRole.CONSULTANT ? [{
      name: lang === 'ar' ? "إدارة المستخدمين" : "User Management",
      icon: <Users size={20} />,
      path: `/${lang}/Thiqah/UserManagement`
    }] : []),
  ];

  const isRtl = lang === "ar";
  const sidebarWidth = isSidebarOpen ? "w-72" : "w-20";
  const mainContentMargin = isSidebarOpen ? "md:ml-72" : "md:ml-20";
  const mainContentMarginRtl = isSidebarOpen ? "md:mr-72" : "md:mr-20";

  // Check if current path is Home, DataClassification, Tasks, Progress, Meetings, AuditFindings, References, or UserManagement route to apply special full-screen treatment
  const isHomePage = pathname === `/${lang}/Thiqah/Home` ||
                     pathname === `/${lang}/Thiqah/DataClassification` ||
                     pathname.startsWith(`/${lang}/Thiqah/DataClassification/`) ||
                     pathname === `/${lang}/Thiqah/Tasks` ||
                     pathname.startsWith(`/${lang}/Thiqah/Tasks/`) ||
                     pathname === `/${lang}/Thiqah/Progress` ||
                     pathname.startsWith(`/${lang}/Thiqah/Progress/`) ||
                     pathname === `/${lang}/Thiqah/Meetings` ||
                     pathname.startsWith(`/${lang}/Thiqah/Meetings/`) ||
                     pathname === `/${lang}/Thiqah/AuditFindings` ||
                     pathname.startsWith(`/${lang}/Thiqah/AuditFindings/`) ||
                     pathname === `/${lang}/Thiqah/References` ||
                     pathname.startsWith(`/${lang}/Thiqah/References/`) ||
                     pathname === `/${lang}/Thiqah/UserManagement`;
  const sidebarWidthPx = isSidebarOpen ? '18rem' : '5rem';

  return (
    <div className={`min-h-screen overflow-x-hidden ${isHomePage ? '' : 'bg-[var(--brand-white)]'} ${isRtl ? "rtl" : "ltr"}`}>
      {/* Mobile sidebar toggle - higher z-index on Home page */}
      <div className={`md:hidden fixed top-4 ${isRtl ? 'right-4' : 'left-4'} ${isHomePage ? 'z-[60]' : 'z-50'}`}>
        <Button 
          variant="outline" 
          size="icon" 
          onClick={toggleSidebar}
          className="bg-white shadow-md"
        >
          <Menu size={20} />
        </Button>
      </div>

      {/* Sidebar Overlay for mobile */}
      {isSidebarOpen && (
        <div 
          className="md:hidden fixed inset-0 bg-black/50 z-40"
          onClick={() => setIsSidebarOpen(false)}
        ></div>
      )}

      {/* Sidebar */}
      <aside 
        className={`fixed top-0 ${isRtl ? 'right-0' : 'left-0'} h-full bg-[var(--brand-dark-gray)] text-white transition-all duration-300 ease-in-out z-50 ${sidebarWidth} ${isSidebarOpen ? 'translate-x-0' : `${isRtl ? 'translate-x-full' : '-translate-x-full'}`} md:translate-x-0 shadow-xl`}
      >
        <div className="flex flex-col h-full">
          {/* Sidebar header with logo and collapse button */}
          <div className="flex items-center justify-between p-5 border-b border-[var(--brand-blue)]/30">
            <div className={`flex items-center ${isSidebarOpen ? 'flex-1 justify-center' : ''}`}>
              <Image 
                src="/image.png" 
                alt="THIqah Logo" 
                width={56} 
                height={56} 
                className={`transition-all duration-300 ${!isSidebarOpen ? 'mx-auto' : ''}`}
              />
            </div>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={toggleSidebar}
              className="text-white hover:bg-[var(--brand-blue)]/50 hidden md:flex"
            >
              {isRtl ? (
                isSidebarOpen ? <ChevronRight size={18} /> : <ChevronLeft size={18} />
              ) : (
                isSidebarOpen ? <ChevronLeft size={18} /> : <ChevronRight size={18} />
              )}
            </Button>
          </div>

          {/* User profile section */}
          <div className={`p-5 border-b border-[var(--brand-blue)]/30 ${!isSidebarOpen && 'text-center'}`}>
            <div className="flex flex-col items-center mb-3">
              <button
                onClick={() => setIsProfileModalOpen(true)}
                className="group relative transition-all duration-300 hover:scale-105"
              >
                {userPhotoURL ? (
                  <Image 
                    src={userPhotoURL} 
                    alt="User Profile" 
                    width={48} 
                    height={48} 
                    className="rounded-full border-2 border-[var(--brand-blue)]/70 group-hover:border-white/80 transition-all duration-300"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-full bg-[var(--brand-blue)] flex items-center justify-center shadow-md group-hover:bg-[var(--brand-blue)]/80 transition-all duration-300">
                    <User size={24} />
                  </div>
                )}
                {/* Hover indicator */}
                <div className="absolute inset-0 rounded-full bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <User size={16} className="text-white" />
                </div>
              </button>
            </div>
            {isSidebarOpen && (
              <div className="text-center">
                <button 
                  onClick={() => setIsProfileModalOpen(true)}
                  className="group transition-all duration-300 hover:bg-white/10 rounded-lg p-2 -m-2"
                >
                  <h3 className="font-medium text-white group-hover:text-white/90">{userDisplayName || userName}</h3>
                  <p className="text-xs text-blue-200/80 group-hover:text-blue-200/90">{userEmail}</p>
                </button>
              </div>
            )}
          </div>

          {/* Navigation menu */}
          <nav className="flex-1 overflow-y-auto py-5 px-3">
            <div className={`mb-2 px-3 ${isSidebarOpen ? 'block' : 'hidden'}`}>
              <h3 className="text-xs font-semibold text-blue-200/70 uppercase tracking-wider">
                {lang === 'ar' ? "القائمة الرئيسية" : "Main Menu"}
              </h3>
            </div>
            <ul className="space-y-1.5">
              {menuItems.map((item) => {
                const isActive = pathname === item.path || pathname.startsWith(item.path + '/');
                return (
                  <li key={item.name}>
                    <Link href={item.path}>
                      <div
                        className={`flex items-center p-3 rounded-lg transition-all duration-200 ${isRtl ? 'text-right' : 'text-left'} ${
                          isActive 
                            ? 'bg-[var(--brand-blue)] bg-opacity-50 font-semibold shadow-md' 
                            : 'hover:bg-[var(--brand-blue)] hover:bg-opacity-30'
                        } ${!isSidebarOpen && "justify-center"}`}
                      >
                        <span className={`flex-shrink-0 ${isActive ? 'text-white' : 'text-blue-200'}`}>{item.icon}</span>
                        {isSidebarOpen && (
                          <span className={`${isRtl ? 'mr-3' : 'ml-3'} font-medium`}>{item.name}</span>
                        )}
                        {isActive && isSidebarOpen && (
                          <span className={`absolute ${isRtl ? 'left-3' : 'right-3'} w-1.5 h-8 bg-white rounded-full`}></span>
                        )}
                      </div>
                    </Link>
                  </li>
                );
              })}
            </ul>


          </nav>

          {/* Bottom actions: Language and Logout */}
          <div className="p-4 border-t border-[#004080]/30">
            <div className="space-y-2">
              {/* Language switcher */}
              <div className="relative">
                <button
                  onClick={toggleLanguageMenu}
                  className={`flex items-center p-3 w-full rounded-lg text-white hover:bg-[var(--brand-blue)]/30 transition-all duration-200 ${
                    !isSidebarOpen && "justify-center"
                  }`}
                >
                  <Globe size={20} className="text-blue-200" />
                  {isSidebarOpen && (
                    <span className={`${isRtl ? 'mr-3' : 'ml-3'} font-medium`}>
                      {lang === 'ar' ? "اللغة" : "Language"}
                    </span>
                  )}
                </button>

                {isLanguageMenuOpen && isSidebarOpen && (
                  <div className="absolute bottom-full left-0 mb-1 bg-white rounded-lg shadow-lg w-full overflow-hidden z-10">
                    {i18n.locales.map((locale) => (
                      <Link 
                        key={locale} 
                        href={getPathWithLocale(locale)}
                        className={`block px-4 py-3 text-[#003366] hover:bg-gray-100 ${locale === lang ? 'bg-gray-100 font-medium' : ''}`}
                        onClick={() => setIsLanguageMenuOpen(false)}
                      >
                        {locale === 'en' ? 'English' : 'العربية'}
                      </Link>
                    ))}
                  </div>
                )}

                {isLanguageMenuOpen && !isSidebarOpen && (
                  <div className={`absolute bottom-full ${isRtl ? 'right-full mr-2' : 'left-full ml-2'} mb-1 bg-white rounded-lg shadow-lg overflow-hidden z-10`}>
                    {i18n.locales.map((locale) => (
                      <Link 
                        key={locale} 
                        href={getPathWithLocale(locale)}
                        className={`block px-4 py-3 text-[#003366] hover:bg-gray-100 whitespace-nowrap ${locale === lang ? 'bg-gray-100 font-medium' : ''}`}
                        onClick={() => setIsLanguageMenuOpen(false)}
                      >
                        {locale === 'en' ? 'English' : 'العربية'}
                      </Link>
                    ))}
                  </div>
                )}
              </div>

              {/* Logout button */}
              <button
                onClick={handleLogout}
                className={`flex items-center p-3 w-full rounded-lg text-white hover:bg-[var(--brand-blue)]/30 transition-all duration-200 ${
                  !isSidebarOpen && "justify-center"
                }`}
              >
                <LogOut size={20} className="text-blue-200" />
                {isSidebarOpen && (
                  <span className={`${isRtl ? 'mr-3' : 'ml-3'} font-medium`}>
                    {lang === 'ar' ? "تسجيل الخروج" : "Logout"}
                  </span>
                )}
              </button>
            </div>
          </div>
        </div>
      </aside>

      {/* Header with notifications - hidden on Home page */}
      {!isHomePage && (
        <header className={`fixed top-0 ${isRtl ? 'right-0 pl-4' : 'left-0 pr-4'} ${isRtl ? mainContentMarginRtl : mainContentMargin} h-16 bg-white shadow-sm z-30 w-full transition-all duration-300 ease-in-out flex items-center justify-end`}>
        <div className="flex items-center space-x-4">
          <button className="relative p-2 rounded-full hover:bg-gray-100 transition-colors">
            <Bell size={20} className="text-gray-600" />
            <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
        </div>
      </header>
      )}

      {/* Main content */}
      {isHomePage ? (
        // For Home and DataClassification pages, fit content exactly from sidebar to screen edge
        <main
          className={`h-full overflow-hidden transition-all duration-300 ease-in-out`}
          style={{
            width: `calc(100% - ${sidebarWidthPx})`,
            [isRtl ? 'marginRight' : 'marginLeft']: sidebarWidthPx
          }}
        >
          {children}
        </main>
      ) : (
        // For all other pages, use the standard layout
        <main 
          className={`transition-all duration-300 ease-in-out pt-20 px-4 md:px-8 pb-8 ${
            isRtl ? mainContentMarginRtl : mainContentMargin
          }`}
        >
          {children}
        </main>
      )}

      {/* User Profile Modal */}
      <UserProfileModal
        isOpen={isProfileModalOpen}
        onClose={() => setIsProfileModalOpen(false)}
        isRTL={isRtl}
        onNameUpdate={handleNameUpdate}
      />
    </div>
  );
}
