"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  ArrowRight,
  CheckSquare,
  Calendar,
  Users,
  Clock,
  Flag,
  User,
  Plus,
  MessageSquare,
  TrendingUp,
  Edit3,
  Save,
  X,
  Activity,
  FileText,
  Trash2,
  Mail,
  ExternalLink,
  Shield,
  UserPlus,
  Link,
  AlertCircle
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { TasksService, Task, TaskStatus, TaskPriority, TimelineEntry, TaskRelatedPerson } from "@/Firebase/firestore/services/TasksService";
import { auth } from "@/Firebase/Authentication/authConfig";
import { Timestamp } from "firebase/firestore";
import { getUserProfile } from "@/Firebase/firestore/services/UserService";
import { TaskFeedbackSidebar } from "@/components/ui/TaskFeedbackSidebar";

interface TaskDetailsPageProps {
  params: Promise<{ lang: Locale; taskId: string }>;
}

export default function TaskDetailsPage({ params }: TaskDetailsPageProps) {
  const [lang, setLang] = useState<string>('');
  const [taskId, setTaskId] = useState<string>('');
  const [task, setTask] = useState<Task | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeline, setTimeline] = useState<TimelineEntry[]>([]);
  const [currentProgress, setCurrentProgress] = useState(0);
  const [isEditingProgress, setIsEditingProgress] = useState(false);
  const [newProgress, setNewProgress] = useState(0);
  const [isAddingUpdate, setIsAddingUpdate] = useState(false);
  const [newUpdate, setNewUpdate] = useState({ title: '', description: '', includeProgress: false, progressValue: 0 });
  const [isCurrentUserAssigned, setIsCurrentUserAssigned] = useState(false);
  const [creatorDisplayName, setCreatorDisplayName] = useState<string>('');

  // New fields state
  const [isAddingPerson, setIsAddingPerson] = useState(false);
  const [newPerson, setNewPerson] = useState({ name: '', email: '', role: '' });
  const [isAddingRisk, setIsAddingRisk] = useState(false);
  const [newRisk, setNewRisk] = useState({ title: '', description: '', severity: 'medium' as 'low' | 'medium' | 'high' | 'critical', probability: 'medium' as 'low' | 'medium' | 'high', mitigation: '' });
  const [isEditingOutputLink, setIsEditingOutputLink] = useState(false);
  const [outputLink, setOutputLink] = useState('');

  // Feedback state
  const [isFeedbackSidebarOpen, setIsFeedbackSidebarOpen] = useState(false);
  
  const router = useRouter();
  const { toast } = useToast();

  // Initialize params
  useEffect(() => {
    params.then(({ lang, taskId }) => {
      setLang(lang);
      setTaskId(taskId);
    });
  }, [params]);

  // Load creator's actual display name
  const loadCreatorInfo = useCallback(async (createdBy: string, createdByName: string) => {
    try {
      const userProfile = await getUserProfile(createdBy);
      const displayName = userProfile?.displayName || userProfile?.email || createdByName || "Unknown";
      setCreatorDisplayName(displayName);
    } catch (error) {
      console.error('Error loading creator info:', error);
      setCreatorDisplayName(createdByName || "Unknown");
    }
  }, []);

  const loadTaskDetails = useCallback(async () => {
    if (!taskId) return;
    
    try {
      setIsLoading(true);
      const taskData = await TasksService.getTask(taskId);
      
      if (taskData) {
        setTask(taskData);
        setCurrentProgress(taskData.progress || 0);
        setNewProgress(taskData.progress || 0);
        setOutputLink(taskData.outputLink || '');
        
        // Load timeline from database
        setTimeline(taskData.timeline || []);
        
        // Load creator's actual display name
        await loadCreatorInfo(taskData.createdBy, taskData.createdByName);
        
        const currentUser = auth.currentUser;
        if (currentUser) {
          const isAssigned = taskData.assignees.some(assignee => assignee.uid === currentUser.uid);
          setIsCurrentUserAssigned(isAssigned);
        }
        
      } else {
        const isRTL = lang === "ar";
        toast({
          title: isRTL ? "المهمة غير موجودة" : "Task not found",
          description: isRTL ? "لم يتم العثور على المهمة المطلوبة" : "The requested task was not found",
          variant: "destructive",
        });
        router.push(`/${lang}/Thiqah/Tasks`);
      }
    } catch (error) {
      console.error('Error loading task:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في تحميل المهمة" : "Error loading task",
        description: isRTL ? "فشل في تحميل تفاصيل المهمة" : "Failed to load task details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [taskId, lang, toast, router, loadCreatorInfo]);

  useEffect(() => {
    if (taskId && lang) {
      loadTaskDetails();
    }
  }, [taskId, lang, loadTaskDetails]);

  const handleGoBack = () => {
    router.push(`/${lang}/Thiqah/Tasks`);
  };

  const handleUpdateProgress = async () => {
    if (!task || !isCurrentUserAssigned) return;
    
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) return;

      const currentUserProfile = await getUserProfile(currentUser.uid);
      const currentUserDisplayName = currentUserProfile?.displayName || currentUser?.displayName || currentUser?.email || 'Unknown';
      
      // Use the new TasksService method to update progress and save to database
      await TasksService.updateTaskProgress(task.id!, newProgress, currentUser.uid, currentUserDisplayName);
      
      // Reload task data to get updated timeline
      await loadTaskDetails();
      
      setCurrentProgress(newProgress);
      setIsEditingProgress(false);
      
      const isRTL = lang === "ar";

      // Check if status changed automatically
      const updatedTask = await TasksService.getTask(task.id!);
      const statusChanged = updatedTask && updatedTask.status !== task.status;

      if (statusChanged && newProgress >= 100) {
        toast({
          title: isRTL ? "🎉 تم إكمال المهمة!" : "🎉 Task Completed!",
          description: isRTL ? `تم تحديث التقدم إلى ${newProgress}% وتغيير الحالة إلى "مكتملة" تلقائياً` : `Progress updated to ${newProgress}% and status automatically changed to "Completed"`,
        });
      } else if (statusChanged && newProgress > 0 && newProgress < 100) {
        toast({
          title: isRTL ? "تم تحديث التقدم والحالة" : "Progress and status updated",
          description: isRTL ? `تم تحديث التقدم إلى ${newProgress}% وتغيير الحالة إلى "قيد التنفيذ"` : `Progress updated to ${newProgress}% and status changed to "In Progress"`,
        });
      } else {
        toast({
          title: isRTL ? "تم تحديث التقدم" : "Progress updated",
          description: isRTL ? `تم تحديث التقدم إلى ${newProgress}%` : `Progress updated to ${newProgress}%`,
        });
      }
    } catch (error) {
      console.error('Error updating progress:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في التحديث" : "Update error",
        description: isRTL ? "فشل في تحديث التقدم" : "Failed to update progress",
        variant: "destructive",
      });
    }
  };

  const handleAddUpdate = async () => {
    if (!task || !isCurrentUserAssigned || !newUpdate.title.trim()) return;

    try {
      const currentUser = auth.currentUser;
      if (!currentUser) return;

      const currentUserProfile = await getUserProfile(currentUser.uid);
      const currentUserDisplayName = currentUserProfile?.displayName || currentUser?.displayName || currentUser?.email || 'Unknown';
      
      const updateData = {
        title: newUpdate.title.trim(),
        description: newUpdate.description.trim(),
        createdBy: currentUser.uid,
        createdByName: currentUserDisplayName,
        progress: newUpdate.includeProgress ? newUpdate.progressValue : undefined
      };

      // Update the task progress if included
      if (newUpdate.includeProgress && newUpdate.progressValue !== currentProgress) {
        await TasksService.updateTaskProgress(task.id!, newUpdate.progressValue, currentUser.uid, currentUserDisplayName);
        setCurrentProgress(newUpdate.progressValue);
      }

      // Store old status to check for changes
      const oldStatus = task.status;

      // Use the new TasksService method to add update and save to database
      await TasksService.addTaskUpdate(task.id!, updateData);

      // Reload task data to get updated timeline and updates
      await loadTaskDetails();

      setIsAddingUpdate(false);
      setNewUpdate({ title: '', description: '', includeProgress: false, progressValue: 0 });

      const isRTL = lang === "ar";

      // Check if status changed automatically due to progress
      if (newUpdate.includeProgress) {
        const updatedTask = await TasksService.getTask(task.id!);
        const statusChanged = updatedTask && updatedTask.status !== oldStatus;

        if (statusChanged && newUpdate.progressValue >= 100) {
          toast({
            title: isRTL ? "🎉 تم إكمال المهمة!" : "🎉 Task Completed!",
            description: isRTL ? `تم إضافة التحديث وتغيير الحالة إلى "مكتملة" تلقائياً` : `Update added and status automatically changed to "Completed"`,
          });
        } else if (statusChanged) {
          toast({
            title: isRTL ? "تم إضافة التحديث وتغيير الحالة" : "Update added and status changed",
            description: isRTL ? `تم إضافة التحديث وتغيير الحالة تلقائياً` : `Update added and status automatically changed`,
          });
        } else {
          toast({
            title: isRTL ? "تم إضافة التحديث" : "Update added",
            description: isRTL ? "تم إضافة التحديث بنجاح" : "Update added successfully",
          });
        }
      } else {
        toast({
          title: isRTL ? "تم إضافة التحديث" : "Update added",
          description: isRTL ? "تم إضافة التحديث بنجاح" : "Update added successfully",
        });
      }
    } catch (error) {
      console.error('Error adding update:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في الإضافة" : "Add error",
        description: isRTL ? "فشل في إضافة التحديث" : "Failed to add update",
        variant: "destructive",
      });
    }
  };

  const formatDate = (timestamp: Timestamp) => {
    const date = timestamp.toDate();
    return date.toLocaleDateString(lang === "ar" ? "ar-SA" : "en-US", {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPriorityColor = (priority: TaskPriority) => {
    switch (priority) {
      case TaskPriority.LOW: return 'text-green-600 bg-green-50';
      case TaskPriority.MEDIUM: return 'text-yellow-600 bg-yellow-50';
      case TaskPriority.HIGH: return 'text-orange-600 bg-orange-50';
      case TaskPriority.URGENT: return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING: return 'text-yellow-700 bg-yellow-100';
      case TaskStatus.IN_PROGRESS: return 'text-blue-700 bg-blue-100';
      case TaskStatus.COMPLETED: return 'text-green-700 bg-green-100';
      case TaskStatus.CANCELLED: return 'text-gray-700 bg-gray-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  // New handler functions
  const handleDeleteUpdate = async (updateId: string) => {
    if (!task || !isCurrentUserAssigned) return;

    try {
      await TasksService.deleteTaskUpdate(task.id!, updateId);
      await loadTaskDetails();
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم حذف التحديث" : "Update deleted",
        description: isRTL ? "تم حذف التحديث بنجاح" : "Update deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting update:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في الحذف" : "Delete error",
        description: isRTL ? "فشل في حذف التحديث" : "Failed to delete update",
        variant: "destructive",
      });
    }
  };

  const handleDeleteTimelineEntry = async (timelineId: string) => {
    if (!task || !isCurrentUserAssigned) return;

    try {
      await TasksService.deleteProgressUpdate(task.id!, timelineId);
      await loadTaskDetails();
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم حذف الحدث" : "Timeline entry deleted",
        description: isRTL ? "تم حذف الحدث من التسلسل الزمني" : "Timeline entry deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting timeline entry:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في الحذف" : "Delete error",
        description: isRTL ? "فشل في حذف الحدث" : "Failed to delete timeline entry",
        variant: "destructive",
      });
    }
  };

  const handleAddPerson = async () => {
    if (!task || !newPerson.name.trim() || !newPerson.email.trim()) return;

    try {
      const person: TaskRelatedPerson = {
        name: newPerson.name.trim(),
        email: newPerson.email.trim(),
        role: newPerson.role.trim() || undefined
      };

      await TasksService.addRelatedPerson(task.id!, person);
      await loadTaskDetails();
      
      setIsAddingPerson(false);
      setNewPerson({ name: '', email: '', role: '' });
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم إضافة الشخص" : "Person added",
        description: isRTL ? "تم إضافة الشخص ذي الصلة بنجاح" : "Related person added successfully",
      });
    } catch (error) {
      console.error('Error adding person:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في الإضافة" : "Add error",
        description: isRTL ? "فشل في إضافة الشخص" : "Failed to add person",
        variant: "destructive",
      });
    }
  };

  const handleRemovePerson = async (email: string) => {
    if (!task) return;

    try {
      await TasksService.removeRelatedPerson(task.id!, email);
      await loadTaskDetails();
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم حذف الشخص" : "Person removed",
        description: isRTL ? "تم حذف الشخص ذي الصلة" : "Related person removed",
      });
    } catch (error) {
      console.error('Error removing person:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في الحذف" : "Remove error",
        description: isRTL ? "فشل في حذف الشخص" : "Failed to remove person",
        variant: "destructive",
      });
    }
  };

  const handleAddRisk = async () => {
    if (!task || !newRisk.title.trim()) return;

    try {
      const currentUser = auth.currentUser;
      if (!currentUser) return;

      const currentUserProfile = await getUserProfile(currentUser.uid);
      const currentUserDisplayName = currentUserProfile?.displayName || currentUser?.displayName || currentUser?.email || 'Unknown';

      const riskData = {
        title: newRisk.title.trim(),
        description: newRisk.description.trim(),
        severity: newRisk.severity,
        probability: newRisk.probability,
        mitigation: newRisk.mitigation.trim() || undefined,
        createdBy: currentUser.uid,
        createdByName: currentUserDisplayName
      };

      await TasksService.addTaskRisk(task.id!, riskData);
      await loadTaskDetails();
      
      setIsAddingRisk(false);
      setNewRisk({ title: '', description: '', severity: 'medium', probability: 'medium', mitigation: '' });
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم إضافة المخاطرة" : "Risk added",
        description: isRTL ? "تم إضافة المخاطرة بنجاح" : "Risk added successfully",
      });
    } catch (error) {
      console.error('Error adding risk:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في الإضافة" : "Add error",
        description: isRTL ? "فشل في إضافة المخاطرة" : "Failed to add risk",
        variant: "destructive",
      });
    }
  };

  const handleRemoveRisk = async (riskId: string) => {
    if (!task) return;

    try {
      await TasksService.removeTaskRisk(task.id!, riskId);
      await loadTaskDetails();
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم حذف المخاطرة" : "Risk removed",
        description: isRTL ? "تم حذف المخاطرة" : "Risk removed",
      });
    } catch (error) {
      console.error('Error removing risk:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في الحذف" : "Remove error",
        description: isRTL ? "فشل في حذف المخاطرة" : "Failed to remove risk",
        variant: "destructive",
      });
    }
  };

  const handleUpdateOutputLink = async () => {
    if (!task) return;

    try {
      await TasksService.updateOutputLink(task.id!, outputLink);
      await loadTaskDetails();
      
      setIsEditingOutputLink(false);
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم تحديث الرابط" : "Link updated",
        description: isRTL ? "تم تحديث رابط المخرجات" : "Output link updated successfully",
      });
    } catch (error) {
      console.error('Error updating output link:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في التحديث" : "Update error",
        description: isRTL ? "فشل في تحديث الرابط" : "Failed to update output link",
        variant: "destructive",
      });
    }
  };

  const getRiskSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-green-700 bg-green-100';
      case 'medium': return 'text-yellow-700 bg-yellow-100';
      case 'high': return 'text-orange-700 bg-orange-100';
      case 'critical': return 'text-red-700 bg-red-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  if (!lang || !taskId) {
    return <div>Loading...</div>;
  }

  const isRTL = lang === "ar";

  if (isLoading) {
    return (
      <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
        <div className="relative min-h-screen bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
          <div className="relative z-10 flex flex-col justify-center items-center min-h-screen px-8 py-16">
            <div className="animate-pulse text-center">
              <div className="w-24 h-24 bg-white/20 rounded-3xl mx-auto mb-8"></div>
              <div className="h-12 bg-white/20 rounded-lg w-96 mx-auto mb-4"></div>
              <div className="h-6 bg-white/20 rounded-lg w-64 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!task) {
    return null;
  }

  return (
    <>
      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: white;
          cursor: pointer;
          border: 3px solid rgba(255, 255, 255, 0.9);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
          transition: all 0.2s ease;
        }
        
        .slider::-webkit-slider-thumb:hover {
          transform: scale(1.1);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2), 0 3px 6px rgba(0, 0, 0, 0.15);
        }
        
        .slider::-webkit-slider-thumb:active {
          transform: scale(1.05);
        }
        
        .slider::-moz-range-thumb {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: white;
          cursor: pointer;
          border: 3px solid rgba(255, 255, 255, 0.9);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .slider:focus {
          outline: none;
        }
        
        .slider:focus::-webkit-slider-thumb {
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2), 0 3px 6px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(255, 255, 255, 0.3);
        }
      `}</style>
      
      <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
        {/* Hero Section */}
        <div className="relative bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80">
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
            <div className="absolute bottom-0 right-0 w-32 h-32 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
          </div>

          <div className="relative z-10 px-8 py-8">
            <div className="flex items-center justify-between mb-6">
              <Button
                onClick={handleGoBack}
                className="bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 hover:text-white shadow-lg"
              >
                {isRTL ? <ArrowRight className="w-4 h-4 mr-2" /> : <ArrowLeft className="w-4 h-4 mr-2" />}
                {isRTL ? "العودة للمهام" : "Back to Tasks"}
              </Button>
            </div>

            <div className="flex items-start gap-6 mb-6">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center shadow-lg border border-white/20">
                <CheckSquare className="w-8 h-8 text-white" />
              </div>
              <div className="flex-1">
                <h1 className="text-3xl md:text-4xl font-bold text-white mb-2 tracking-tight">
                  {task.title}
                </h1>
                <p className="text-white/90 text-lg mb-4">
                  {task.description}
                </p>
                
                <div className="flex items-center gap-3 mb-4">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(task.status)}`}>
                    {task.status === TaskStatus.PENDING ? (isRTL ? "معلقة" : "Pending") :
                     task.status === TaskStatus.IN_PROGRESS ? (isRTL ? "قيد التنفيذ" : "In Progress") :
                     task.status === TaskStatus.COMPLETED ? (isRTL ? "مكتملة" : "Completed") :
                     (isRTL ? "ملغية" : "Cancelled")}
                  </span>
                  
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(task.priority)}`}>
                    <Flag className="w-3 h-3 inline mr-1" />
                    {task.priority === TaskPriority.LOW ? (isRTL ? "منخفضة" : "Low") :
                     task.priority === TaskPriority.MEDIUM ? (isRTL ? "متوسطة" : "Medium") :
                     task.priority === TaskPriority.HIGH ? (isRTL ? "عالية" : "High") :
                     (isRTL ? "عاجلة" : "Urgent")}
                  </span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {[
                { 
                  icon: User, 
                  label: isRTL ? "منشئ المهمة" : "Created by", 
                  value: creatorDisplayName || task.createdByName || "Unknown"
                },
                { 
                  icon: Users, 
                  label: isRTL ? "المُكلفون" : "Assignees", 
                  value: task.assignees.length.toString(),
                  subValue: isRTL ? "شخص" : "people"
                },
                { 
                  icon: Calendar, 
                  label: isRTL ? "تاريخ الإنشاء" : "Created", 
                  value: formatDate(task.createdAt).split(',')[0]
                },
                { 
                  icon: TrendingUp, 
                  label: isRTL ? "التقدم" : "Progress", 
                  value: `${currentProgress}%`,
                  subValue: isRTL ? "مكتمل" : "complete"
                }
              ].map((item, index) => (
                <div
                  key={index}
                  className="bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-300"
                >
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-white/20 text-white">
                      <item.icon className="w-4 h-4" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="text-xs font-semibold text-white/80 uppercase tracking-wider">{item.label}</div>
                      <div className="text-sm font-bold text-white truncate">{item.value}</div>
                      {item.subValue && <div className="text-xs text-white/70">{item.subValue}</div>}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white font-medium">{isRTL ? "نسبة الإنجاز" : "Completion Progress"}</span>
                {isCurrentUserAssigned && (
                  <Button
                    onClick={() => {
                      setIsEditingProgress(!isEditingProgress);
                      setNewProgress(currentProgress);
                    }}
                    variant="ghost"
                    size="sm"
                    className="text-white hover:bg-white/20"
                  >
                    <Edit3 className="w-4 h-4" />
                  </Button>
                )}
              </div>
              <div className="w-full bg-white/20 rounded-full h-3 mb-2">
                <div 
                  className="bg-white h-3 rounded-full transition-all duration-500 ease-out shadow-lg"
                  style={{ width: `${currentProgress}%` }}
                ></div>
              </div>
              <div className="text-white/80 text-sm text-center">{currentProgress}% {isRTL ? "مكتمل" : "Complete"}</div>
              
              {isEditingProgress && isCurrentUserAssigned && (
                <motion.div 
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-6 space-y-4 bg-white/10 backdrop-blur-xl rounded-xl p-6 border border-white/20"
                >
                  <div className="text-center mb-4">
                    <h4 className="text-white font-semibold text-lg mb-2">
                      {isRTL ? "تحديث نسبة الإنجاز" : "Update Progress"}
                    </h4>
                    <div className="text-white/80 text-sm">
                      {isRTL ? "اسحب المؤشر لتعديل نسبة الإنجاز" : "Drag the slider to adjust progress"}
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="relative">
                      <input
                        type="range"
                        min="0"
                        max="100"
                        step="5"
                        value={newProgress}
                        onChange={(e) => setNewProgress(parseInt(e.target.value))}
                        className="w-full h-3 bg-white/20 rounded-xl appearance-none cursor-pointer slider focus:outline-none focus:ring-2 focus:ring-white/50"
                        style={{
                          background: `linear-gradient(to right, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.9) ${newProgress}%, rgba(255,255,255,0.2) ${newProgress}%, rgba(255,255,255,0.2) 100%)`
                        }}
                      />
                      <div className="flex justify-between mt-2 text-white/70 text-xs">
                        <span>0%</span>
                        <span>25%</span>
                        <span>50%</span>
                        <span>75%</span>
                        <span>100%</span>
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <div className="text-white font-bold text-3xl mb-1">{newProgress}%</div>
                      <div className="text-white/80 text-sm">
                        {newProgress === 0 ? (isRTL ? "لم يبدأ" : "Not Started") :
                         newProgress < 25 ? (isRTL ? "بداية العمل" : "Getting Started") :
                         newProgress < 50 ? (isRTL ? "في التقدم" : "Making Progress") :
                         newProgress < 75 ? (isRTL ? "أكثر من النصف" : "Halfway There") :
                         newProgress < 100 ? (isRTL ? "تقريباً منتهي" : "Almost Done") :
                         (isRTL ? "مكتمل" : "Complete")}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-center gap-3 pt-2">
                    <Button
                      onClick={handleUpdateProgress}
                      className="bg-white/20 hover:bg-white/30 text-white border border-white/30 px-6 py-2 rounded-xl font-medium transition-all duration-200"
                    >
                      <Save className="w-4 h-4 mr-2" />
                      {isRTL ? "حفظ التحديث" : "Save Update"}
                    </Button>
                    <Button
                      onClick={() => setIsEditingProgress(false)}
                      variant="ghost"
                      className="text-white hover:bg-white/20 px-6 py-2 rounded-xl font-medium transition-all duration-200"
                    >
                      <X className="w-4 h-4 mr-2" />
                      {isRTL ? "إلغاء" : "Cancel"}
                    </Button>
                  </div>
                </motion.div>
              )}
            </div>
          </div>
        </div>

        {/* Feedback Button Section */}
        <div className="px-8 py-6 bg-gradient-to-r from-gray-50 to-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                onClick={() => setIsFeedbackSidebarOpen(true)}
                className="bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 px-6 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105"
              >
                <MessageSquare className="w-5 h-5 mr-2" />
                {isRTL ? "فتح التعليقات" : "Open Feedback"}
                {task.feedback && task.feedback.length > 0 && (
                  <span className="ml-2 bg-white/20 text-white text-xs px-2 py-1 rounded-full font-semibold">
                    {task.feedback.length}
                  </span>
                )}
              </Button>

              {task.feedback && task.feedback.length > 0 && (
                <div className="text-sm text-gray-600">
                  {task.feedback.filter(f => !f.isResolved).length > 0 && (
                    <span className="inline-flex items-center gap-1 bg-orange-100 text-orange-700 px-3 py-1 rounded-full">
                      <AlertCircle className="w-3 h-3" />
                      {task.feedback.filter(f => !f.isResolved).length} {isRTL ? "غير محلول" : "unresolved"}
                    </span>
                  )}
                </div>
              )}
            </div>

            <div className="text-sm text-gray-500">
              {isRTL ? "شارك ملاحظاتك وتعليقاتك" : "Share your feedback and comments"}
            </div>
          </div>
        </div>

        {/* Timeline and Assignees sections */}
        <div className="px-8 py-8">
          <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-6">
              {isCurrentUserAssigned && (
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 rounded-xl flex items-center justify-center">
                        <MessageSquare className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">
                          {isRTL ? "إضافة تحديث" : "Add Update"}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {isRTL ? "شارك التقدم مع الفريق" : "Share progress with your team"}
                        </p>
                      </div>
                    </div>
                    <Button
                      onClick={() => {
                        setIsAddingUpdate(!isAddingUpdate);
                        if (!isAddingUpdate) {
                          setNewUpdate({ title: '', description: '', includeProgress: false, progressValue: currentProgress });
                        }
                      }}
                      className="bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 px-6 py-2 rounded-xl font-medium transition-all duration-200"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      {isRTL ? "تحديث جديد" : "New Update"}
                    </Button>
                  </div>

                  {isAddingUpdate && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      className="space-y-6 bg-gray-50 rounded-xl p-6 border border-gray-100"
                    >
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {isRTL ? "عنوان التحديث" : "Update Title"}
                          </label>
                          <Input
                            placeholder={isRTL ? "مثال: تم إكمال المرحلة الأولى" : "e.g., Completed first phase"}
                            value={newUpdate.title}
                            onChange={(e) => setNewUpdate(prev => ({ ...prev, title: e.target.value }))}
                            className="border-gray-200 focus:border-[var(--brand-blue)] focus:ring-2 focus:ring-[var(--brand-blue)]/20 rounded-xl"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {isRTL ? "تفاصيل التحديث" : "Update Details"}
                          </label>
                          <Textarea
                            placeholder={isRTL ? "اكتب تفاصيل التقدم والإنجازات..." : "Describe your progress and accomplishments..."}
                            value={newUpdate.description}
                            onChange={(e) => setNewUpdate(prev => ({ ...prev, description: e.target.value }))}
                            className="border-gray-200 focus:border-[var(--brand-blue)] focus:ring-2 focus:ring-[var(--brand-blue)]/20 rounded-xl min-h-32"
                          />
                        </div>

                        <div className="bg-white rounded-xl p-4 border border-gray-200">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-[var(--brand-blue)]/10 rounded-lg flex items-center justify-center">
                                <TrendingUp className="w-4 h-4 text-[var(--brand-blue)]" />
                              </div>
                              <div>
                                <h4 className="font-medium text-gray-900">
                                  {isRTL ? "تحديث نسبة الإنجاز" : "Progress Update"}
                                </h4>
                                <p className="text-xs text-gray-500">
                                  {isRTL ? "هل تريد تحديث نسبة الإنجاز؟" : "Would you like to update the progress?"}
                                </p>
                              </div>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={newUpdate.includeProgress}
                                onChange={(e) => setNewUpdate(prev => ({ ...prev, includeProgress: e.target.checked }))}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[var(--brand-blue)]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[var(--brand-blue)]"></div>
                            </label>
                          </div>
                          
                          {newUpdate.includeProgress && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: "auto" }}
                              className="space-y-3"
                            >
                              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                                <span>{isRTL ? "نسبة الإنجاز الجديدة" : "New Progress"}</span>
                                <span className="font-medium text-[var(--brand-blue)]">{newUpdate.progressValue}%</span>
                              </div>
                              <input
                                type="range"
                                min="0"
                                max="100"
                                step="5"
                                value={newUpdate.progressValue}
                                onChange={(e) => setNewUpdate(prev => ({ ...prev, progressValue: parseInt(e.target.value) }))}
                                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-[var(--brand-blue)]/20"
                                style={{
                                  background: `linear-gradient(to right, var(--brand-blue) 0%, var(--brand-blue) ${newUpdate.progressValue}%, #e5e7eb ${newUpdate.progressValue}%, #e5e7eb 100%)`
                                }}
                              />
                              <div className="flex justify-between text-xs text-gray-400">
                                <span>0%</span>
                                <span>25%</span>
                                <span>50%</span>
                                <span>75%</span>
                                <span>100%</span>
                              </div>
                            </motion.div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div className="text-sm text-gray-500">
                          {newUpdate.title.trim() ? 
                            `${newUpdate.title.length}/100 ${isRTL ? "حرف" : "characters"}` : 
                            (isRTL ? "العنوان مطلوب" : "Title required")
                          }
                        </div>
                        <div className="flex gap-3">
                          <Button
                            onClick={() => {
                              setIsAddingUpdate(false);
                              setNewUpdate({ title: '', description: '', includeProgress: false, progressValue: 0 });
                            }}
                            variant="outline"
                            className="px-6 py-2 rounded-xl"
                          >
                            {isRTL ? "إلغاء" : "Cancel"}
                          </Button>
                          <Button
                            onClick={handleAddUpdate}
                            disabled={!newUpdate.title.trim()}
                            className="bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 px-6 py-2 rounded-xl font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <Activity className="w-4 h-4 mr-2" />
                            {isRTL ? "إضافة التحديث" : "Add Update"}
                          </Button>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </div>
              )}

              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 rounded-xl flex items-center justify-center">
                      <Clock className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">
                        {isRTL ? "التسلسل الزمني" : "Timeline"}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {timeline.length > 0 ? 
                          `${timeline.length} ${isRTL ? "حدث" : "events"}` : 
                          (isRTL ? "لا توجد أحداث بعد" : "No events yet")
                        }
                      </p>
                    </div>
                  </div>
                  <div className="text-sm text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
                    {isRTL ? "آخر تحديث" : "Latest Activity"}
                  </div>
                </div>

                {timeline.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FileText className="w-12 h-12 text-gray-400" />
                    </div>
                    <h4 className="text-lg font-medium text-gray-900 mb-2">
                      {isRTL ? "لا توجد أحداث في التسلسل الزمني" : "No Timeline Events"}
                    </h4>
                    <p className="text-gray-500 mb-4 max-w-md mx-auto">
                      {isRTL ? 
                        "سيظهر هنا سجل بجميع التحديثات والتقدم في هذه المهمة" : 
                        "All updates and progress for this task will appear here"
                      }
                    </p>
                    {isCurrentUserAssigned && (
                      <div className="text-sm text-[var(--brand-blue)] font-medium">
                        {isRTL ? "أضف أول تحديث لبدء التسلسل الزمني" : "Add your first update to start the timeline"}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="relative">
                    <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gradient-to-b from-[var(--brand-blue)] via-blue-300 to-gray-200"></div>

                    <div className="space-y-6">
                      {timeline.map((entry, index) => (
                        <motion.div
                          key={entry.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="relative flex items-start gap-4"
                        >
                          <div className={`relative z-10 w-4 h-4 rounded-full border-3 border-white shadow-lg flex items-center justify-center ${
                            entry.type === 'progress' ? 'bg-gradient-to-r from-blue-500 to-blue-600' :
                            entry.type === 'update' ? 'bg-gradient-to-r from-green-500 to-green-600' :
                            entry.type === 'status_change' ? 'bg-gradient-to-r from-orange-500 to-orange-600' :
                            'bg-gradient-to-r from-gray-500 to-gray-600'
                          }`}>
                            {entry.type === 'progress' && <TrendingUp className="w-2 h-2 text-white" />}
                            {entry.type === 'update' && <MessageSquare className="w-2 h-2 text-white" />}
                            {entry.type === 'status_change' && <Flag className="w-2 h-2 text-white" />}
                          </div>

                          <div className="flex-1 bg-gradient-to-r from-gray-50 to-white rounded-xl p-5 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-200">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1">
                                <h4 className="font-semibold text-gray-900 text-lg mb-1">{entry.title}</h4>
                                <div className="flex items-center gap-2 text-sm text-gray-500">
                                  <User className="w-3 h-3" />
                                  <span>{entry.createdByName}</span>
                                  <span>•</span>
                                  <span>{formatDate(entry.createdAt)}</span>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  entry.type === 'progress' ? 'bg-blue-100 text-blue-700' :
                                  entry.type === 'update' ? 'bg-green-100 text-green-700' :
                                  entry.type === 'status_change' ? 'bg-orange-100 text-orange-700' :
                                  'bg-gray-100 text-gray-700'
                                }`}>
                                  {entry.type === 'progress' ? (isRTL ? "تحديث التقدم" : "Progress") :
                                   entry.type === 'update' ? (isRTL ? "تحديث" : "Update") :
                                   entry.type === 'status_change' ? (isRTL ? "تغيير الحالة" : "Status Change") :
                                   (isRTL ? "حدث" : "Event")}
                                </div>
                                {isCurrentUserAssigned && (
                                  <Button
                                    onClick={() => entry.type === 'update' ? handleDeleteUpdate(entry.id) : handleDeleteTimelineEntry(entry.id)}
                                    size="sm"
                                    variant="ghost"
                                    className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 h-6 w-6"
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </Button>
                                )}
                              </div>
                            </div>
                            
                            {entry.description && (
                              <p className="text-gray-600 text-sm mb-3 leading-relaxed">{entry.description}</p>
                            )}
                            
                            {entry.progress !== undefined && (
                              <div className="bg-white rounded-lg p-3 border border-gray-100">
                                <div className="flex items-center justify-between text-sm font-medium text-gray-700 mb-2">
                                  <span>{isRTL ? "نسبة الإنجاز" : "Progress"}</span>
                                  <span className="text-[var(--brand-blue)]">{entry.progress}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2.5">
                                  <div 
                                    className="bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 h-2.5 rounded-full transition-all duration-500 ease-out"
                                    style={{ width: `${entry.progress}%` }}
                                  ></div>
                                </div>
                              </div>
                            )}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-6">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Users className="w-5 h-5 text-[var(--brand-blue)]" />
                  {isRTL ? "المُكلفون" : "Assignees"}
                </h3>
                
                <div className="space-y-3">
                  {task.assignees.map((assignee) => (
                    <div key={assignee.uid} className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                      <div className="w-10 h-10 bg-[var(--brand-blue)]/10 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-[var(--brand-blue)]" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{assignee.displayName || assignee.email || "Unknown"}</div>
                        <div className="text-sm text-gray-500">{assignee.email}</div>
                        <div className="text-xs text-[var(--brand-blue)] bg-[var(--brand-blue)]/10 rounded-md px-2 py-1 inline-block mt-1">
                          {assignee.role}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <CheckSquare className="w-5 h-5 text-[var(--brand-blue)]" />
                  {isRTL ? "تفاصيل المهمة" : "Task Details"}
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">{isRTL ? "تاريخ الإنشاء" : "Created"}</label>
                    <p className="text-gray-900">{formatDate(task.createdAt)}</p>
                  </div>
                  
                  {task.dueDate && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">{isRTL ? "تاريخ الاستحقاق" : "Due Date"}</label>
                      <p className="text-gray-900">{formatDate(task.dueDate)}</p>
                    </div>
                  )}
                  
                  {task.tags && task.tags.length > 0 && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">{isRTL ? "العلامات" : "Tags"}</label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {task.tags.map((tag, index) => (
                          <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Related People Section */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center">
                      <UserPlus className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">
                        {isRTL ? "الأشخاص ذوو الصلة" : "Related People"}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {task.relatedPeople?.length || 0} {isRTL ? "شخص" : "people"}
                      </p>
                    </div>
                  </div>
                  {isCurrentUserAssigned && (
                    <Button
                      onClick={() => setIsAddingPerson(!isAddingPerson)}
                      className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700 px-4 py-2 rounded-xl text-sm"
                    >
                      <Plus className="w-4 h-4 mr-1" />
                      {isRTL ? "إضافة" : "Add"}
                    </Button>
                  )}
                </div>

                {isAddingPerson && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="mb-4 p-4 bg-gray-50 rounded-xl space-y-3"
                  >
                    <Input
                      placeholder={isRTL ? "الاسم" : "Name"}
                      value={newPerson.name}
                      onChange={(e) => setNewPerson(prev => ({ ...prev, name: e.target.value }))}
                      className="border-gray-200 focus:border-emerald-500 rounded-lg"
                    />
                    <Input
                      placeholder={isRTL ? "البريد الإلكتروني" : "Email"}
                      type="email"
                      value={newPerson.email}
                      onChange={(e) => setNewPerson(prev => ({ ...prev, email: e.target.value }))}
                      className="border-gray-200 focus:border-emerald-500 rounded-lg"
                    />
                    <Input
                      placeholder={isRTL ? "الدور (اختياري)" : "Role (optional)"}
                      value={newPerson.role}
                      onChange={(e) => setNewPerson(prev => ({ ...prev, role: e.target.value }))}
                      className="border-gray-200 focus:border-emerald-500 rounded-lg"
                    />
                    <div className="flex gap-2">
                      <Button
                        onClick={() => {
                          setIsAddingPerson(false);
                          setNewPerson({ name: '', email: '', role: '' });
                        }}
                        variant="outline"
                        size="sm"
                      >
                        {isRTL ? "إلغاء" : "Cancel"}
                      </Button>
                      <Button
                        onClick={handleAddPerson}
                        disabled={!newPerson.name.trim() || !newPerson.email.trim()}
                        size="sm"
                        className="bg-emerald-500 hover:bg-emerald-600"
                      >
                        {isRTL ? "إضافة" : "Add"}
                      </Button>
                    </div>
                  </motion.div>
                )}

                <div className="space-y-3">
                  {task.relatedPeople && task.relatedPeople.length > 0 ? (
                    task.relatedPeople.map((person, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                            <Mail className="w-4 h-4 text-emerald-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{person.name}</div>
                            <div className="text-sm text-gray-500">{person.email}</div>
                            {person.role && (
                              <div className="text-xs text-emerald-600 bg-emerald-100 rounded-md px-2 py-1 inline-block mt-1">
                                {person.role}
                              </div>
                            )}
                          </div>
                        </div>
                        {isCurrentUserAssigned && (
                          <Button
                            onClick={() => handleRemovePerson(person.email)}
                            size="sm"
                            variant="ghost"
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-6 text-gray-500">
                      <UserPlus className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                      <p>{isRTL ? "لا توجد أشخاص مرتبطة بالمهمة" : "No related people added yet"}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Risks Section */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-red-600 rounded-xl flex items-center justify-center">
                      <Shield className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">
                        {isRTL ? "المخاطر المحتملة" : "Possible Risks"}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {task.risks?.length || 0} {isRTL ? "مخاطرة" : "risks"}
                      </p>
                    </div>
                  </div>
                  {isCurrentUserAssigned && (
                    <Button
                      onClick={() => setIsAddingRisk(!isAddingRisk)}
                      className="bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 px-4 py-2 rounded-xl text-sm"
                    >
                      <Plus className="w-4 h-4 mr-1" />
                      {isRTL ? "إضافة" : "Add"}
                    </Button>
                  )}
                </div>

                {isAddingRisk && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="mb-4 p-4 bg-gray-50 rounded-xl space-y-3"
                  >
                    <Input
                      placeholder={isRTL ? "عنوان المخاطرة" : "Risk title"}
                      value={newRisk.title}
                      onChange={(e) => setNewRisk(prev => ({ ...prev, title: e.target.value }))}
                      className="border-gray-200 focus:border-red-500 rounded-lg"
                    />
                    <Textarea
                      placeholder={isRTL ? "وصف المخاطرة" : "Risk description"}
                      value={newRisk.description}
                      onChange={(e) => setNewRisk(prev => ({ ...prev, description: e.target.value }))}
                      className="border-gray-200 focus:border-red-500 rounded-lg min-h-20"
                    />
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {isRTL ? "الشدة" : "Severity"}
                        </label>
                        <select
                          value={newRisk.severity}
                          onChange={(e) => setNewRisk(prev => ({ ...prev, severity: e.target.value as 'low' | 'medium' | 'high' | 'critical' }))}
                          className="w-full border border-gray-200 rounded-lg px-3 py-2 focus:border-red-500 focus:outline-none"
                        >
                          <option value="low">{isRTL ? "منخفضة" : "Low"}</option>
                          <option value="medium">{isRTL ? "متوسطة" : "Medium"}</option>
                          <option value="high">{isRTL ? "عالية" : "High"}</option>
                          <option value="critical">{isRTL ? "حرجة" : "Critical"}</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {isRTL ? "الاحتمالية" : "Probability"}
                        </label>
                        <select
                          value={newRisk.probability}
                          onChange={(e) => setNewRisk(prev => ({ ...prev, probability: e.target.value as 'low' | 'medium' | 'high' }))}
                          className="w-full border border-gray-200 rounded-lg px-3 py-2 focus:border-red-500 focus:outline-none"
                        >
                          <option value="low">{isRTL ? "منخفضة" : "Low"}</option>
                          <option value="medium">{isRTL ? "متوسطة" : "Medium"}</option>
                          <option value="high">{isRTL ? "عالية" : "High"}</option>
                        </select>
                      </div>
                    </div>
                    <Textarea
                      placeholder={isRTL ? "التخفيف (اختياري)" : "Mitigation (optional)"}
                      value={newRisk.mitigation}
                      onChange={(e) => setNewRisk(prev => ({ ...prev, mitigation: e.target.value }))}
                      className="border-gray-200 focus:border-red-500 rounded-lg min-h-16"
                    />
                    <div className="flex gap-2">
                      <Button
                        onClick={() => {
                          setIsAddingRisk(false);
                          setNewRisk({ title: '', description: '', severity: 'medium', probability: 'medium', mitigation: '' });
                        }}
                        variant="outline"
                        size="sm"
                      >
                        {isRTL ? "إلغاء" : "Cancel"}
                      </Button>
                      <Button
                        onClick={handleAddRisk}
                        disabled={!newRisk.title.trim()}
                        size="sm"
                        className="bg-red-500 hover:bg-red-600"
                      >
                        {isRTL ? "إضافة" : "Add"}
                      </Button>
                    </div>
                  </motion.div>
                )}

                <div className="space-y-3">
                  {task.risks && task.risks.length > 0 ? (
                    task.risks.map((risk) => (
                      <div key={risk.id} className="p-4 bg-gray-50 rounded-xl border border-gray-100">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-medium text-gray-900">{risk.title}</h4>
                          <div className="flex items-center gap-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskSeverityColor(risk.severity)}`}>
                              {risk.severity === 'low' ? (isRTL ? "منخفضة" : "Low") :
                               risk.severity === 'medium' ? (isRTL ? "متوسطة" : "Medium") :
                               risk.severity === 'high' ? (isRTL ? "عالية" : "High") :
                               (isRTL ? "حرجة" : "Critical")}
                            </span>
                            {isCurrentUserAssigned && (
                              <Button
                                onClick={() => handleRemoveRisk(risk.id)}
                                size="sm"
                                variant="ghost"
                                className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 h-6 w-6"
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            )}
                          </div>
                        </div>
                        {risk.description && (
                          <p className="text-sm text-gray-600 mb-2">{risk.description}</p>
                        )}
                        <div className="flex items-center gap-4 text-xs text-gray-500 mb-2">
                          <span>{isRTL ? "الاحتمالية:" : "Probability:"} {
                            risk.probability === 'low' ? (isRTL ? "منخفضة" : "Low") :
                            risk.probability === 'medium' ? (isRTL ? "متوسطة" : "Medium") :
                            (isRTL ? "عالية" : "High")
                          }</span>
                          <span>•</span>
                          <span>{risk.createdByName}</span>
                        </div>
                        {risk.mitigation && (
                          <div className="mt-2 p-2 bg-green-50 rounded-lg border border-green-200">
                            <div className="text-xs font-medium text-green-700 mb-1">
                              {isRTL ? "التخفيف:" : "Mitigation:"}
                            </div>
                            <p className="text-sm text-green-600">{risk.mitigation}</p>
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-6 text-gray-500">
                      <Shield className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                      <p>{isRTL ? "لم يتم تحديد مخاطر بعد" : "No risks identified yet"}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Output Link Section */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 rounded-xl flex items-center justify-center">
                      <Link className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">
                        {isRTL ? "رابط المخرجات" : "Output Link"}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {isRTL ? "رابط المجلد المشترك في ثقة" : "Thiqah Shared Folder Link"}
                      </p>
                    </div>
                  </div>
                  {isCurrentUserAssigned && (
                    <Button
                      onClick={() => setIsEditingOutputLink(!isEditingOutputLink)}
                      variant="outline"
                      className="border-[var(--brand-blue)] text-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/10 px-4 py-2 rounded-xl text-sm"
                    >
                      <Edit3 className="w-4 h-4 mr-1" />
                      {isRTL ? "تعديل" : "Edit"}
                    </Button>
                  )}
                </div>

                {isEditingOutputLink ? (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-3"
                  >
                    <Input
                      placeholder={isRTL ? "رابط المجلد المشترك" : "Shared folder link"}
                      value={outputLink}
                      onChange={(e) => setOutputLink(e.target.value)}
                      className="border-gray-200 focus:border-[var(--brand-blue)] rounded-lg"
                    />
                    <div className="flex gap-2">
                      <Button
                        onClick={() => {
                          setIsEditingOutputLink(false);
                          setOutputLink(task.outputLink || '');
                        }}
                        variant="outline"
                        size="sm"
                      >
                        {isRTL ? "إلغاء" : "Cancel"}
                      </Button>
                      <Button
                        onClick={handleUpdateOutputLink}
                        size="sm"
                        className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
                      >
                        {isRTL ? "حفظ" : "Save"}
                      </Button>
                    </div>
                  </motion.div>
                ) : (
                  <div>
                    {task.outputLink ? (
                      <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                        <ExternalLink className="w-5 h-5 text-[var(--brand-blue)]" />
                        <a
                          href={task.outputLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex-1 text-[var(--brand-blue)] hover:underline font-medium truncate"
                        >
                          {task.outputLink}
                        </a>
                      </div>
                    ) : (
                      <div className="text-center py-6 text-gray-500">
                        <Link className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                        <p>{isRTL ? "لم يتم إضافة رابط المخرجات بعد" : "No output link added yet"}</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Feedback Sidebar */}
      <TaskFeedbackSidebar
        isOpen={isFeedbackSidebarOpen}
        onClose={() => setIsFeedbackSidebarOpen(false)}
        taskId={taskId}
        feedback={task.feedback || []}
        onFeedbackUpdate={loadTaskDetails}
        isRTL={isRTL}
      />
    </>
  );
}