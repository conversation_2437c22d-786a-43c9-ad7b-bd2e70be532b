"use client";

import React from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";

interface ControlData {
  id: string;
  name: string;
  domainId: string;
  totalSpecs: number;
}

interface ControlCardProps {
  control: ControlData;
  onClick: () => void;
  isActive: boolean;
  index: number;
  lang: Locale;
}

export function ControlCard({ control, onClick, isActive, index, lang }: ControlCardProps) {
  const isRTL = lang === "ar";
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      onClick={onClick}
      className={`
        cursor-pointer rounded-xl overflow-hidden transition-all duration-300
        border ${isActive ? 'border-purple-500' : 'border-white/10'}
        ${isActive ? 'bg-purple-500/10' : 'bg-white/5'}
        backdrop-blur-sm
      `}
    >
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="h-8 w-8 rounded-full bg-purple-500/20 flex items-center justify-center">
            <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
            </svg>
          </div>
          <div className="bg-white/10 rounded-lg px-2 py-1">
            <span className="text-white/70 text-xs">{isRTL ? "رمز" : "ID"}: {control.id}</span>
          </div>
        </div>
        
        <h3 className="text-lg font-bold text-white mb-2 line-clamp-2" title={control.name}>
          {control.name}
        </h3>
        
        <div className="flex justify-between items-center mb-3">
          <span className="text-white/70 text-sm">{isRTL ? "المواصفات" : "Specifications"}</span>
          <span className="text-white font-medium">{control.totalSpecs}</span>
        </div>
        
        <div className="mt-3">
          <div className={`
            flex items-center justify-center py-1.5 px-3 rounded-lg text-xs
            ${isActive 
              ? 'bg-purple-500 text-white' 
              : 'bg-white/10 text-white/70'}
            transition-all duration-300
          `}>
            <span className="font-medium">
              {isActive 
                ? (isRTL ? "عرض المواصفات" : "Viewing Specs") 
                : (isRTL ? "عرض المواصفات" : "View Specs")}
            </span>
            <svg className={`w-3 h-3 ${isRTL ? 'mr-1.5' : 'ml-1.5'} transition-transform duration-300 ${isActive ? 'rotate-90' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </div>
      </div>
    </motion.div>
  );
} 