"use client";

import React from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>hart3,
  CheckCircle,
  XCircle,
  TrendingUp,
  FileText,
  Shield
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

interface AnalysisSummaryCardProps {
  overallScore: number;
  complianceLevel: 'Excellent' | 'Good' | 'Needs Improvement' | 'Poor';
  totalSentences: number;
  compliantSentences: number;
  missingDomainsCount: number;
  isRTL: boolean;
}

export function AnalysisSummaryCard({ 
  overallScore, 
  complianceLevel, 
  totalSentences,
  compliantSentences,
  missingDomainsCount,
  isRTL 
}: AnalysisSummaryCardProps) {
  
  const getComplianceColor = (level: string) => {
    switch (level) {
      case 'Excellent':
        return 'bg-green-100 text-green-800 border-green-300';
      case 'Good':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'Needs Improvement':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'Poor':
        return 'bg-red-100 text-red-800 border-red-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProgressColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const compliancePercentage = totalSentences > 0 ? Math.round((compliantSentences / totalSentences) * 100) : 0;

  const stats = [
    {
      label: isRTL ? "إجمالي الجمل" : "Total Sentences",
      value: totalSentences,
      icon: FileText,
      color: "text-blue-600"
    },
    {
      label: isRTL ? "الجمل المتوافقة" : "Compliant Sentences",
      value: compliantSentences,
      icon: CheckCircle,
      color: "text-green-600"
    },
    {
      label: isRTL ? "المجالات المفقودة" : "Missing Domains",
      value: missingDomainsCount,
      icon: XCircle,
      color: "text-red-600"
    },
    {
      label: isRTL ? "نسبة التوافق" : "Compliance Rate",
      value: `${compliancePercentage}%`,
      icon: TrendingUp,
      color: compliancePercentage >= 80 ? "text-green-600" : compliancePercentage >= 60 ? "text-yellow-600" : "text-red-600"
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="border-l-4 border-l-[var(--brand-blue)] bg-gradient-to-r from-blue-50/50 to-white">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-[var(--brand-blue)]">
            <BarChart3 className="w-6 h-6" />
            {isRTL ? "ملخص التحليل" : "Analysis Summary"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Overall Score */}
          <div className="text-center">
            <div className="relative inline-flex items-center justify-center">
              <div className="w-32 h-32 rounded-full border-8 border-gray-200 flex items-center justify-center relative">
                <div 
                  className={`absolute inset-0 rounded-full border-8 border-transparent`}
                  style={{
                    background: `conic-gradient(${getProgressColor(overallScore)} ${overallScore * 3.6}deg, transparent 0deg)`,
                    borderRadius: '50%'
                  }}
                />
                <div className="w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-inner">
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${getScoreColor(overallScore)}`}>
                      {overallScore}%
                    </div>
                    <div className="text-xs text-gray-500 font-medium">
                      {isRTL ? "النتيجة الإجمالية" : "Overall Score"}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-4">
              <Badge 
                variant="outline" 
                className={`text-sm px-4 py-2 ${getComplianceColor(complianceLevel)}`}
              >
                <Shield className="w-4 h-4 mr-2" />
                {isRTL ? 
                  (complianceLevel === 'Excellent' ? 'ممتاز' :
                   complianceLevel === 'Good' ? 'جيد' :
                   complianceLevel === 'Needs Improvement' ? 'يحتاج تحسين' : 'ضعيف')
                  : complianceLevel
                }
              </Badge>
            </div>
          </div>

          {/* Statistics Grid */}
          <div className="grid grid-cols-2 gap-4">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm"
              >
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg bg-gray-50 ${stat.color}`}>
                    <stat.icon className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <div className="text-2xl font-bold text-gray-900">
                      {stat.value}
                    </div>
                    <div className="text-sm text-gray-600">
                      {stat.label}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">
                {isRTL ? "مستوى التوافق مع قانون حماية البيانات الشخصية" : "PDPL Compliance Level"}
              </span>
              <span className={`text-sm font-bold ${getScoreColor(overallScore)}`}>
                {overallScore}%
              </span>
            </div>
            <div className="relative">
              <Progress 
                value={overallScore} 
                className="h-3"
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-xs font-medium text-white drop-shadow-sm">
                  {overallScore >= 10 && `${overallScore}%`}
                </div>
              </div>
            </div>
            <div className="flex justify-between text-xs text-gray-500">
              <span>{isRTL ? "ضعيف" : "Poor"}</span>
              <span>{isRTL ? "ممتاز" : "Excellent"}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
