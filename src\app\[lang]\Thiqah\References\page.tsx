import { Locale } from '@/i18n-config';
import { ReferencesPageClient } from './ReferencesPageClient';

interface ReferencesPageProps {
  params: Promise<{
    lang: Locale;
  }>;
}

export default async function ReferencesPage({ params }: ReferencesPageProps) {
  const { lang } = await params;
  return <ReferencesPageClient lang={lang} />;
}

export async function generateStaticParams() {
  return [
    { lang: 'en' },
    { lang: 'ar' }
  ];
}
