"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";
import { useRouter } from "next/navigation";
import { ArrowLeft, ArrowRight, FileSearch, Shield, User, Calendar, AlertTriangle, Plus, Edit, Trash2, Save, X, CheckCircle, MessageSquare, FileText, Target, TrendingUp, BarChart3, Activity, DollarSign, Timer, Percent, Hash } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { AuditService, Observation, ObservationRisk, ObservationRating, ManagementResponse, ResolutionKPI, KPIUpdate } from "@/Firebase/firestore/services/AuditService";
import { Timestamp } from 'firebase/firestore';
import { auth } from "@/Firebase/Authentication/authConfig";
import { getUserProfile } from "@/Firebase/firestore/services/UserService";

interface ObservationDetailPageProps {
  params: Promise<{ lang: Locale; observationId: string }>;
}

interface AddRiskModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (risk: Omit<ObservationRisk, 'id' | 'createdAt'>) => Promise<void>;
  isRTL: boolean;
  isLoading?: boolean;
}

interface AddManagementResponseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (response: Omit<ManagementResponse, 'id' | 'createdAt' | 'status'>) => Promise<void>;
  isRTL: boolean;
  isLoading?: boolean;
}

interface AddKPIModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (kpi: Omit<ResolutionKPI, 'id' | 'createdAt' | 'updates' | 'currentValue' | 'lastUpdated' | 'status'>) => Promise<void>;
  isRTL: boolean;
  isLoading?: boolean;
}

interface UpdateKPIModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (update: Omit<KPIUpdate, 'id' | 'createdAt'>) => Promise<void>;
  kpi: ResolutionKPI | null;
  isRTL: boolean;
  isLoading?: boolean;
}



function AddRiskModal({ isOpen, onClose, onSubmit, isRTL, isLoading }: AddRiskModalProps) {
  const [formData, setFormData] = useState({
    description: "",
    impact: "",
    probability: "",
    mitigation: ""
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.description.trim()) return;

    try {
      await onSubmit({
        description: formData.description.trim(),
        impact: formData.impact.trim() || undefined,
        probability: formData.probability.trim() || undefined,
        mitigation: formData.mitigation.trim() || undefined
      });
      setFormData({ description: "", impact: "", probability: "", mitigation: "" });
      onClose();
    } catch (error) {
      console.error('Error adding risk:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className={`bg-white rounded-2xl shadow-2xl border border-gray-200 w-full max-w-2xl ${isRTL ? "rtl" : "ltr"}`}
      >
        <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 px-6 py-4">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold text-white">
              {isRTL ? "إضافة مخاطرة جديدة" : "Add New Risk"}
            </h3>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-white hover:bg-white/20 rounded-xl"
              disabled={isLoading}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {isRTL ? "وصف المخاطرة" : "Risk Description"} *
            </label>
            <Textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder={isRTL ? "اكتب وصف المخاطرة..." : "Describe the risk..."}
              required
              className="min-h-20"
              disabled={isLoading}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? "التأثير" : "Impact"}
              </label>
              <Input
                value={formData.impact}
                onChange={(e) => setFormData(prev => ({ ...prev, impact: e.target.value }))}
                placeholder={isRTL ? "تأثير المخاطرة" : "Risk impact"}
                disabled={isLoading}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? "الاحتمالية" : "Probability"}
              </label>
              <Input
                value={formData.probability}
                onChange={(e) => setFormData(prev => ({ ...prev, probability: e.target.value }))}
                placeholder={isRTL ? "احتمالية حدوث المخاطرة" : "Risk probability"}
                disabled={isLoading}
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {isRTL ? "التخفيف" : "Mitigation"}
            </label>
            <Textarea
              value={formData.mitigation}
              onChange={(e) => setFormData(prev => ({ ...prev, mitigation: e.target.value }))}
              placeholder={isRTL ? "إجراءات التخفيف..." : "Mitigation actions..."}
              className="min-h-20"
              disabled={isLoading}
            />
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isLoading}
            >
              {isRTL ? "إلغاء" : "Cancel"}
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80"
              disabled={isLoading || !formData.description.trim()}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  {isRTL ? "جاري الحفظ..." : "Saving..."}
                </div>
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-2" />
                  {isRTL ? "إضافة المخاطرة" : "Add Risk"}
                </>
              )}
            </Button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}

function AddManagementResponseModal({ isOpen, onClose, onSubmit, isRTL, isLoading }: AddManagementResponseModalProps) {
  const [formData, setFormData] = useState({
    response: "",
    responsiblePerson: "",
    dateType: "monthly" as 'monthly' | 'quarterly',
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    quarter: Math.ceil((new Date().getMonth() + 1) / 3) as 1 | 2 | 3 | 4,
    createdBy: "",
    createdByName: ""
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.response.trim() || !formData.responsiblePerson.trim()) return;

    try {
      const targetDate = new Date();
      
      if (formData.dateType === 'quarterly') {
        // Set to the last day of the quarter
        const quarterEndMonth = formData.quarter * 3;
        targetDate.setFullYear(formData.year, quarterEndMonth - 1, 0);
        targetDate.setDate(new Date(formData.year, quarterEndMonth, 0).getDate());
      } else {
        // Set to the last day of the month
        targetDate.setFullYear(formData.year, formData.month - 1, 0);
        targetDate.setDate(new Date(formData.year, formData.month, 0).getDate());
      }

      const responseData = {
        response: formData.response.trim(),
        responsiblePerson: formData.responsiblePerson.trim(),
        targetCompletionDate: Timestamp.fromDate(targetDate),
        dateType: formData.dateType,
        year: formData.year,
        ...(formData.dateType === 'quarterly' ? { quarter: formData.quarter } : { month: formData.month }),
        createdBy: formData.createdBy,
        createdByName: formData.createdByName
      };

      await onSubmit(responseData);
      setFormData({
        response: "",
        responsiblePerson: "",
        dateType: "monthly",
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        quarter: Math.ceil((new Date().getMonth() + 1) / 3) as 1 | 2 | 3 | 4,
        createdBy: "",
        createdByName: ""
      });
      onClose();
    } catch (error) {
      console.error('Error adding management response:', error);
    }
  };

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear + i);
  const months = [
    { value: 1, label: isRTL ? "يناير" : "January" },
    { value: 2, label: isRTL ? "فبراير" : "February" },
    { value: 3, label: isRTL ? "مارس" : "March" },
    { value: 4, label: isRTL ? "أبريل" : "April" },
    { value: 5, label: isRTL ? "مايو" : "May" },
    { value: 6, label: isRTL ? "يونيو" : "June" },
    { value: 7, label: isRTL ? "يوليو" : "July" },
    { value: 8, label: isRTL ? "أغسطس" : "August" },
    { value: 9, label: isRTL ? "سبتمبر" : "September" },
    { value: 10, label: isRTL ? "أكتوبر" : "October" },
    { value: 11, label: isRTL ? "نوفمبر" : "November" },
    { value: 12, label: isRTL ? "ديسمبر" : "December" }
  ];

  const quarters = [
    { value: 1, label: isRTL ? "الربع الأول" : "Q1" },
    { value: 2, label: isRTL ? "الربع الثاني" : "Q2" },
    { value: 3, label: isRTL ? "الربع الثالث" : "Q3" },
    { value: 4, label: isRTL ? "الربع الرابع" : "Q4" }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className={`bg-white rounded-2xl shadow-2xl border border-gray-200 w-full max-w-3xl max-h-[90vh] overflow-hidden ${isRTL ? "rtl" : "ltr"}`}
      >
        <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                <MessageSquare className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">
                  {isRTL ? "إضافة رد إدارة جديد" : "Add New Management Response"}
                </h3>
                <p className="text-white/80 text-sm">
                  {isRTL ? "تسجيل رد الإدارة مع المسؤول والموعد المستهدف" : "Record management response with responsible person and target date"}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-white hover:bg-white/20 rounded-xl"
              disabled={isLoading}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Response Text */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? "رد الإدارة" : "Management Response"} *
              </label>
              <Textarea
                value={formData.response}
                onChange={(e) => setFormData(prev => ({ ...prev, response: e.target.value }))}
                placeholder={isRTL ? "اكتب رد الإدارة على الملاحظة..." : "Enter management response to the observation..."}
                required
                className="min-h-24"
                disabled={isLoading}
              />
            </div>

            {/* Responsible Person */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? "الشخص المسؤول" : "Responsible Person"} *
              </label>
              <Input
                value={formData.responsiblePerson}
                onChange={(e) => setFormData(prev => ({ ...prev, responsiblePerson: e.target.value }))}
                placeholder={isRTL ? "اسم الشخص المسؤول عن التنفيذ" : "Name of person responsible for implementation"}
                required
                disabled={isLoading}
              />
            </div>

            {/* Date Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                {isRTL ? "نوع التاريخ المستهدف" : "Target Date Type"}
              </label>
              <div className="grid grid-cols-2 gap-3">
                <button
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, dateType: 'monthly' }))}
                  className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                    formData.dateType === 'monthly'
                      ? 'border-[var(--brand-blue)] bg-[var(--brand-blue)]/10 text-[var(--brand-blue)]'
                      : 'border-gray-200 text-gray-600 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                  disabled={isLoading}
                >
                  <Calendar className="w-5 h-5 mx-auto mb-2" />
                  <div className="font-semibold text-sm">{isRTL ? "شهري" : "Monthly"}</div>
                  <div className="text-xs opacity-80">{isRTL ? "تحديد شهر معين" : "Specific month"}</div>
                </button>
                <button
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, dateType: 'quarterly' }))}
                  className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                    formData.dateType === 'quarterly'
                      ? 'border-[var(--brand-blue)] bg-[var(--brand-blue)]/10 text-[var(--brand-blue)]'
                      : 'border-gray-200 text-gray-600 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                  disabled={isLoading}
                >
                  <TrendingUp className="w-5 h-5 mx-auto mb-2" />
                  <div className="font-semibold text-sm">{isRTL ? "ربع سنوي" : "Quarterly"}</div>
                  <div className="text-xs opacity-80">{isRTL ? "تحديد ربع سنة" : "Specific quarter"}</div>
                </button>
              </div>
            </div>

            {/* Date Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "السنة" : "Year"}
                </label>
                <select
                  value={formData.year}
                  onChange={(e) => setFormData(prev => ({ ...prev, year: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:border-[var(--brand-blue)] focus:outline-none"
                  disabled={isLoading}
                >
                  {years.map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {formData.dateType === 'monthly' ? (isRTL ? "الشهر" : "Month") : (isRTL ? "الربع" : "Quarter")}
                </label>
                {formData.dateType === 'monthly' ? (
                  <select
                    value={formData.month}
                    onChange={(e) => setFormData(prev => ({ ...prev, month: parseInt(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:border-[var(--brand-blue)] focus:outline-none"
                    disabled={isLoading}
                  >
                    {months.map(month => (
                      <option key={month.value} value={month.value}>{month.label}</option>
                    ))}
                  </select>
                ) : (
                  <select
                    value={formData.quarter}
                    onChange={(e) => setFormData(prev => ({ ...prev, quarter: parseInt(e.target.value) as 1 | 2 | 3 | 4 }))}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:border-[var(--brand-blue)] focus:outline-none"
                    disabled={isLoading}
                  >
                    {quarters.map(quarter => (
                      <option key={quarter.value} value={quarter.value}>{quarter.label}</option>
                    ))}
                  </select>
                )}
              </div>
            </div>

            {/* Preview */}
            <div className="bg-gray-50 rounded-xl p-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? "معاينة التاريخ المستهدف" : "Target Date Preview"}
              </label>
              <p className="text-sm text-gray-600">
                {formData.dateType === 'monthly' 
                  ? `${months.find(m => m.value === formData.month)?.label} ${formData.year}`
                  : `${quarters.find(q => q.value === formData.quarter)?.label} ${formData.year}`
                }
              </p>
            </div>

            {/* Form Actions */}
            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="flex-1"
                disabled={isLoading}
              >
                {isRTL ? "إلغاء" : "Cancel"}
              </Button>
              <Button
                type="submit"
                className="flex-1 bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80"
                disabled={isLoading || !formData.response.trim() || !formData.responsiblePerson.trim()}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    {isRTL ? "جاري الحفظ..." : "Saving..."}
                  </div>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    {isRTL ? "إضافة الرد" : "Add Response"}
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
}

function AddKPIModal({ isOpen, onClose, onSubmit, isRTL, isLoading }: AddKPIModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    unit: "",
    targetValue: 0,
    measurementFrequency: "monthly" as 'weekly' | 'monthly' | 'quarterly',
    kpiType: "number" as 'percentage' | 'number' | 'currency' | 'time',
    dateType: "monthly" as 'monthly' | 'quarterly',
    quarter: 1 as 1 | 2 | 3 | 4,
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    createdBy: "",
    createdByName: ""
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim() || !formData.unit.trim() || formData.targetValue <= 0) return;

    try {
      // Calculate target date based on selected type
      let targetDate: Date;
      if (formData.dateType === 'monthly') {
        targetDate = new Date(formData.year, formData.month - 1, 1);
        // Set to last day of the month
        targetDate = new Date(formData.year, formData.month, 0);
      } else {
        // Quarterly
        const quarterStartMonth = (formData.quarter - 1) * 3;
        const quarterEndMonth = quarterStartMonth + 2;
        targetDate = new Date(formData.year, quarterEndMonth + 1, 0); // Last day of quarter
      }

      // Create clean KPI data without undefined values
      const kpiData: Omit<ResolutionKPI, 'id' | 'createdAt' | 'updates' | 'currentValue' | 'lastUpdated' | 'status'> = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        unit: formData.unit.trim(),
        targetValue: formData.targetValue,
        measurementFrequency: formData.measurementFrequency,
        kpiType: formData.kpiType,
        targetDate: Timestamp.fromDate(targetDate),
        dateType: formData.dateType,
        year: formData.year,
        createdBy: formData.createdBy,
        createdByName: formData.createdByName
      };

      // Only add optional fields if they have values
      if (formData.dateType === 'quarterly' && formData.quarter !== undefined) {
        kpiData.quarter = formData.quarter;
      }
      if (formData.dateType === 'monthly' && formData.month !== undefined) {
        kpiData.month = formData.month;
      }

      await onSubmit(kpiData);
      
      setFormData({
        name: "",
        description: "",
        unit: "",
        targetValue: 0,
        measurementFrequency: "monthly",
        kpiType: "number",
        dateType: "monthly",
        quarter: 1,
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        createdBy: "",
        createdByName: ""
      });
      onClose();
    } catch (error) {
      console.error('Error adding KPI:', error);
    }
  };

  const kpiTypes = [
    { value: 'number', label: isRTL ? "رقم" : "Number", icon: Hash },
    { value: 'percentage', label: isRTL ? "نسبة مئوية" : "Percentage", icon: Percent },
    { value: 'currency', label: isRTL ? "عملة" : "Currency", icon: DollarSign },
    { value: 'time', label: isRTL ? "وقت" : "Time", icon: Timer }
  ];

  const frequencies = [
    { value: 'weekly', label: isRTL ? "أسبوعي" : "Weekly" },
    { value: 'monthly', label: isRTL ? "شهري" : "Monthly" },
    { value: 'quarterly', label: isRTL ? "ربع سنوي" : "Quarterly" }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className={`bg-white rounded-2xl shadow-2xl border border-gray-200 w-full max-w-3xl max-h-[90vh] overflow-hidden ${isRTL ? "rtl" : "ltr"}`}
      >
        <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">
                  {isRTL ? "إضافة مؤشر أداء جديد" : "Add New KPI"}
                </h3>
                <p className="text-white/80 text-sm">
                  {isRTL ? "تعريف مؤشر أداء لمتابعة تقدم الحل" : "Define a KPI to track resolution progress"}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-white hover:bg-white/20 rounded-xl"
              disabled={isLoading}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* KPI Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? "اسم المؤشر" : "KPI Name"} *
              </label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder={isRTL ? "مثال: معدل الإنجاز" : "e.g., Completion Rate"}
                required
                disabled={isLoading}
              />
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? "وصف المؤشر" : "KPI Description"}
              </label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder={isRTL ? "اكتب وصفاً مفصلاً للمؤشر..." : "Enter a detailed description of the KPI..."}
                className="min-h-20"
                disabled={isLoading}
              />
            </div>

            {/* KPI Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                {isRTL ? "نوع المؤشر" : "KPI Type"}
              </label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {kpiTypes.map((type) => (
                  <button
                    key={type.value}
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, kpiType: type.value as 'percentage' | 'number' | 'currency' | 'time' }))}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                      formData.kpiType === type.value
                        ? 'border-[var(--brand-blue)] bg-[var(--brand-blue)]/10 text-[var(--brand-blue)]'
                        : 'border-gray-200 text-gray-600 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                    disabled={isLoading}
                  >
                    <type.icon className="w-5 h-5 mx-auto mb-2" />
                    <div className="font-semibold text-xs">{type.label}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Target Value and Unit */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "القيمة المستهدفة" : "Target Value"} *
                </label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.targetValue}
                  onChange={(e) => setFormData(prev => ({ ...prev, targetValue: parseFloat(e.target.value) || 0 }))}
                  placeholder="100"
                  required
                  disabled={isLoading}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "وحدة القياس" : "Unit of Measurement"} *
                </label>
                <Input
                  value={formData.unit}
                  onChange={(e) => setFormData(prev => ({ ...prev, unit: e.target.value }))}
                  placeholder={isRTL ? "مثال: %, عدد, ريال" : "e.g., %, count, SAR"}
                  required
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Measurement Frequency */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? "تكرار القياس" : "Measurement Frequency"}
              </label>
              <select
                value={formData.measurementFrequency}
                onChange={(e) => setFormData(prev => ({ ...prev, measurementFrequency: e.target.value as 'weekly' | 'monthly' | 'quarterly' }))}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:border-[var(--brand-blue)] focus:outline-none"
                disabled={isLoading}
              >
                {frequencies.map(freq => (
                  <option key={freq.value} value={freq.value}>{freq.label}</option>
                ))}
              </select>
            </div>

            {/* Target Date Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                {isRTL ? "تاريخ الهدف" : "Target Date"} *
              </label>
              
              {/* Date Type Selection */}
              <div className="grid grid-cols-2 gap-3 mb-4">
                <button
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, dateType: "monthly" }))}
                  className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                    formData.dateType === "monthly"
                      ? 'border-[var(--brand-blue)] bg-[var(--brand-blue)]/10 text-[var(--brand-blue)]'
                      : 'border-gray-200 text-gray-600 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                  disabled={isLoading}
                >
                  <Calendar className="w-5 h-5 mx-auto mb-2" />
                  <div className="font-semibold text-xs">{isRTL ? "شهري" : "Monthly"}</div>
                </button>
                <button
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, dateType: "quarterly" }))}
                  className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                    formData.dateType === "quarterly"
                      ? 'border-[var(--brand-blue)] bg-[var(--brand-blue)]/10 text-[var(--brand-blue)]'
                      : 'border-gray-200 text-gray-600 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                  disabled={isLoading}
                >
                  <Calendar className="w-5 h-5 mx-auto mb-2" />
                  <div className="font-semibold text-xs">{isRTL ? "ربع سنوي" : "Quarterly"}</div>
                </button>
              </div>

              {/* Date Selection Inputs */}
              <div className="grid grid-cols-2 gap-4">
                {formData.dateType === 'monthly' ? (
                  <>
                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">
                        {isRTL ? "الشهر" : "Month"}
                      </label>
                      <select
                        value={formData.month}
                        onChange={(e) => setFormData(prev => ({ ...prev, month: parseInt(e.target.value) }))}
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:border-[var(--brand-blue)] focus:outline-none text-sm"
                        disabled={isLoading}
                      >
                        {[
                          { value: 1, label: isRTL ? "يناير" : "January" },
                          { value: 2, label: isRTL ? "فبراير" : "February" },
                          { value: 3, label: isRTL ? "مارس" : "March" },
                          { value: 4, label: isRTL ? "أبريل" : "April" },
                          { value: 5, label: isRTL ? "مايو" : "May" },
                          { value: 6, label: isRTL ? "يونيو" : "June" },
                          { value: 7, label: isRTL ? "يوليو" : "July" },
                          { value: 8, label: isRTL ? "أغسطس" : "August" },
                          { value: 9, label: isRTL ? "سبتمبر" : "September" },
                          { value: 10, label: isRTL ? "أكتوبر" : "October" },
                          { value: 11, label: isRTL ? "نوفمبر" : "November" },
                          { value: 12, label: isRTL ? "ديسمبر" : "December" }
                        ].map(month => (
                          <option key={month.value} value={month.value}>{month.label}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">
                        {isRTL ? "السنة" : "Year"}
                      </label>
                      <Input
                        type="number"
                        min={new Date().getFullYear()}
                        max={new Date().getFullYear() + 10}
                        value={formData.year}
                        onChange={(e) => setFormData(prev => ({ ...prev, year: parseInt(e.target.value) || new Date().getFullYear() }))}
                        disabled={isLoading}
                        className="text-sm"
                      />
                    </div>
                  </>
                ) : (
                  <>
                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">
                        {isRTL ? "الربع" : "Quarter"}
                      </label>
                      <select
                        value={formData.quarter}
                        onChange={(e) => setFormData(prev => ({ ...prev, quarter: parseInt(e.target.value) as 1 | 2 | 3 | 4 }))}
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:border-[var(--brand-blue)] focus:outline-none text-sm"
                        disabled={isLoading}
                      >
                        <option value={1}>{isRTL ? "الربع الأول" : "Q1"}</option>
                        <option value={2}>{isRTL ? "الربع الثاني" : "Q2"}</option>
                        <option value={3}>{isRTL ? "الربع الثالث" : "Q3"}</option>
                        <option value={4}>{isRTL ? "الربع الرابع" : "Q4"}</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">
                        {isRTL ? "السنة" : "Year"}
                      </label>
                      <Input
                        type="number"
                        min={new Date().getFullYear()}
                        max={new Date().getFullYear() + 10}
                        value={formData.year}
                        onChange={(e) => setFormData(prev => ({ ...prev, year: parseInt(e.target.value) || new Date().getFullYear() }))}
                        disabled={isLoading}
                        className="text-sm"
                      />
                    </div>
                  </>
                )}
              </div>

              {/* Date Preview */}
              <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                <div className="text-xs font-medium text-gray-500 mb-1">
                  {isRTL ? "تاريخ الهدف:" : "Target Date:"}
                </div>
                <div className="text-sm font-semibold text-gray-900">
                  {formData.dateType === 'monthly' 
                    ? `${[
                        isRTL ? "يناير" : "January", isRTL ? "فبراير" : "February", isRTL ? "مارس" : "March",
                        isRTL ? "أبريل" : "April", isRTL ? "مايو" : "May", isRTL ? "يونيو" : "June",
                        isRTL ? "يوليو" : "July", isRTL ? "أغسطس" : "August", isRTL ? "سبتمبر" : "September",
                        isRTL ? "أكتوبر" : "October", isRTL ? "نوفمبر" : "November", isRTL ? "ديسمبر" : "December"
                      ][formData.month - 1]} ${formData.year}`
                    : `${isRTL ? "الربع" : "Q"}${formData.quarter} ${formData.year}`
                  }
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="flex-1"
                disabled={isLoading}
              >
                {isRTL ? "إلغاء" : "Cancel"}
              </Button>
              <Button
                type="submit"
                className="flex-1 bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80"
                disabled={isLoading || !formData.name.trim() || !formData.unit.trim() || formData.targetValue <= 0}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    {isRTL ? "جاري الحفظ..." : "Saving..."}
                  </div>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    {isRTL ? "إضافة المؤشر" : "Add KPI"}
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
}

function UpdateKPIModal({ isOpen, onClose, onSubmit, kpi, isRTL, isLoading }: UpdateKPIModalProps) {
  const [formData, setFormData] = useState({
    value: 0,
    notes: "",
    updatedBy: "",
    updatedByName: ""
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!kpi || formData.value < 0) return;

    try {
      // Create clean update data without undefined values
      const updateData: Omit<KPIUpdate, 'id' | 'createdAt'> = {
        value: formData.value,
        targetValue: kpi.targetValue,
        unit: kpi.unit,
        updateDate: Timestamp.now(),
        updatedBy: formData.updatedBy,
        updatedByName: formData.updatedByName
      };

      // Only add notes if they have content
      if (formData.notes.trim()) {
        updateData.notes = formData.notes.trim();
      }

      await onSubmit(updateData);
      
      setFormData({
        value: 0,
        notes: "",
        updatedBy: "",
        updatedByName: ""
      });
      onClose();
    } catch (error) {
      console.error('Error updating KPI:', error);
    }
  };

  if (!isOpen || !kpi) return null;

  const progress = (formData.value / kpi.targetValue) * 100;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className={`bg-white rounded-2xl shadow-2xl border border-gray-200 w-full max-w-2xl ${isRTL ? "rtl" : "ltr"}`}
      >
        <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                <Activity className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">
                  {isRTL ? "تحديث المؤشر" : "Update KPI"}
                </h3>
                <p className="text-white/80 text-sm">{kpi.name}</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-white hover:bg-white/20 rounded-xl"
              disabled={isLoading}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Current Status */}
          <div className="bg-gray-50 rounded-xl p-4">
            <h4 className="font-semibold text-gray-900 mb-3">
              {isRTL ? "الحالة الحالية" : "Current Status"}
            </h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <label className="text-gray-600">{isRTL ? "القيمة الحالية" : "Current Value"}</label>
                <p className="font-bold text-gray-900">{kpi.currentValue} {kpi.unit}</p>
              </div>
              <div>
                <label className="text-gray-600">{isRTL ? "الهدف" : "Target"}</label>
                <p className="font-bold text-gray-900">{kpi.targetValue} {kpi.unit}</p>
              </div>
            </div>
          </div>

          {/* New Value */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {isRTL ? "القيمة الجديدة" : "New Value"} *
            </label>
            <div className="relative">
              <Input
                type="number"
                min="0"
                step="0.01"
                value={formData.value}
                onChange={(e) => setFormData(prev => ({ ...prev, value: parseFloat(e.target.value) || 0 }))}
                placeholder="0"
                required
                disabled={isLoading}
                className="pr-16"
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                {kpi.unit}
              </span>
            </div>
          </div>

          {/* Progress Preview */}
          {formData.value > 0 && (
            <div className="bg-gray-50 rounded-xl p-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? "معاينة التقدم" : "Progress Preview"}
              </label>
              <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
                <div 
                  className={`h-3 rounded-full transition-all duration-300 ${
                    progress >= 100 ? 'bg-green-500' :
                    progress >= 80 ? 'bg-blue-500' :
                    progress >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${Math.min(progress, 100)}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-600">
                {progress.toFixed(1)}% {isRTL ? "من الهدف" : "of target"}
              </p>
            </div>
          )}

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {isRTL ? "ملاحظات (اختياري)" : "Notes (Optional)"}
            </label>
            <Textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              placeholder={isRTL ? "أضف ملاحظات حول هذا التحديث..." : "Add notes about this update..."}
              className="min-h-20"
              disabled={isLoading}
            />
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isLoading}
            >
              {isRTL ? "إلغاء" : "Cancel"}
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80"
              disabled={isLoading || formData.value < 0}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  {isRTL ? "جاري التحديث..." : "Updating..."}
                </div>
              ) : (
                <>
                  <Activity className="w-4 h-4 mr-2" />
                  {isRTL ? "تحديث المؤشر" : "Update KPI"}
                </>
              )}
            </Button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}



export default function ObservationDetailPage({ params }: ObservationDetailPageProps) {
  const [lang, setLang] = useState<string>('');
  const [observationId, setObservationId] = useState<string>('');
  const [observation, setObservation] = useState<Observation | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'details' | 'resolution'>('details');
  const [isAddRiskModalOpen, setIsAddRiskModalOpen] = useState(false);
  const [isAddingRisk, setIsAddingRisk] = useState(false);
  const [isAddManagementResponseModalOpen, setIsAddManagementResponseModalOpen] = useState(false);
  const [isAddingManagementResponse, setIsAddingManagementResponse] = useState(false);
  const [isAddKPIModalOpen, setIsAddKPIModalOpen] = useState(false);
  const [isUpdateKPIModalOpen, setIsUpdateKPIModalOpen] = useState(false);
  const [selectedKPI, setSelectedKPI] = useState<ResolutionKPI | null>(null);
  const [isLoadingKPI, setIsLoadingKPI] = useState(false);
  const [focusedKPIId, setFocusedKPIId] = useState<string | null>(null);
  const [editingFields, setEditingFields] = useState<{[key: string]: boolean}>({});
  const [formData, setFormData] = useState({
    backgroundInformation: "",
    recommendations: ""
  });

  const router = useRouter();
  const { toast } = useToast();

  // Initialize params
  useEffect(() => {
    params.then(({ lang, observationId }) => {
      setLang(lang);
      setObservationId(observationId);
    });
  }, [params]);

  // Load observation details
  const loadObservation = useCallback(async () => {
    if (!observationId) return;
    
    try {
      setIsLoading(true);
      const observationData = await AuditService.getObservation(observationId);
      
      if (observationData) {
        setObservation(observationData);
        setFormData({
          backgroundInformation: observationData.backgroundInformation || "",
          recommendations: observationData.recommendations || ""
        });
      } else {
        const isRTL = lang === "ar";
        toast({
          title: isRTL ? "الملاحظة غير موجودة" : "Observation not found",
          description: isRTL ? "لم يتم العثور على الملاحظة المطلوبة" : "The requested observation was not found",
          variant: "destructive",
        });
        router.push(`/${lang}/Thiqah/AuditFindings`);
      }
    } catch (error) {
      console.error('Error loading observation:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في تحميل الملاحظة" : "Error loading observation",
        description: isRTL ? "فشل في تحميل تفاصيل الملاحظة" : "Failed to load observation details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [observationId, lang, toast, router]);

  useEffect(() => {
    if (observationId) {
      loadObservation();
    }
  }, [observationId, loadObservation]);

  const handleAddRisk = async (risk: Omit<ObservationRisk, 'id' | 'createdAt'>) => {
    if (!observation?.id) return;

    try {
      setIsAddingRisk(true);
      await AuditService.addObservationRisk(observation.id, risk);
      await loadObservation(); // Refresh observation data
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم إضافة المخاطرة بنجاح" : "Risk added successfully",
        description: isRTL ? "تم إضافة المخاطرة إلى الملاحظة" : "Risk has been added to the observation",
      });
    } catch (error) {
      console.error('Error adding risk:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في إضافة المخاطرة" : "Error adding risk",
        description: isRTL ? "فشل في إضافة المخاطرة" : "Failed to add risk",
        variant: "destructive",
      });
    } finally {
      setIsAddingRisk(false);
    }
  };

  const handleRemoveRisk = async (riskId: string) => {
    if (!observation?.id) return;

    try {
      await AuditService.removeObservationRisk(observation.id, riskId);
      await loadObservation(); // Refresh observation data
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم حذف المخاطرة بنجاح" : "Risk removed successfully",
        description: isRTL ? "تم حذف المخاطرة من الملاحظة" : "Risk has been removed from the observation",
      });
    } catch (error) {
      console.error('Error removing risk:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في حذف المخاطرة" : "Error removing risk",
        description: isRTL ? "فشل في حذف المخاطرة" : "Failed to remove risk",
        variant: "destructive",
      });
    }
  };

  const handleAddManagementResponse = async (response: Omit<ManagementResponse, 'id' | 'createdAt' | 'status'>) => {
    if (!observation?.id) return;

    const currentUser = auth.currentUser;
    if (!currentUser) {
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في المصادقة" : "Authentication Error",
        description: isRTL ? "يجب تسجيل الدخول أولاً" : "Please log in first",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsAddingManagementResponse(true);
      
      const userProfile = await getUserProfile(currentUser.uid);
      const createdByName = userProfile?.displayName || currentUser.displayName || currentUser.email || 'Unknown User';
      
      const responseWithUser = {
        ...response,
        createdBy: currentUser.uid,
        createdByName: createdByName
      };

      await AuditService.addManagementResponse(observation.id, responseWithUser);
      await loadObservation(); // Refresh observation data
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم إضافة رد الإدارة بنجاح" : "Management response added successfully",
        description: isRTL ? "تم إضافة رد الإدارة إلى الملاحظة" : "Management response has been added to the observation",
      });
    } catch (error) {
      console.error('Error adding management response:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في إضافة رد الإدارة" : "Error adding management response",
        description: isRTL ? "فشل في إضافة رد الإدارة" : "Failed to add management response",
        variant: "destructive",
      });
    } finally {
      setIsAddingManagementResponse(false);
    }
  };

  const handleRemoveManagementResponse = async (responseId: string) => {
    if (!observation?.id) return;

    try {
      await AuditService.removeManagementResponse(observation.id, responseId);
      await loadObservation(); // Refresh observation data
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم حذف رد الإدارة بنجاح" : "Management response removed successfully",
        description: isRTL ? "تم حذف رد الإدارة من الملاحظة" : "Management response has been removed from the observation",
      });
    } catch (error) {
      console.error('Error removing management response:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في حذف رد الإدارة" : "Error removing management response",
        description: isRTL ? "فشل في حذف رد الإدارة" : "Failed to remove management response",
        variant: "destructive",
      });
    }
  };

  const handleAddKPI = async (kpi: Omit<ResolutionKPI, 'id' | 'createdAt' | 'updates' | 'currentValue' | 'lastUpdated' | 'status'>) => {
    if (!observation?.id) return;

    const currentUser = auth.currentUser;
    if (!currentUser) {
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في المصادقة" : "Authentication Error",
        description: isRTL ? "يجب تسجيل الدخول أولاً" : "Please log in first",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoadingKPI(true);
      
      const userProfile = await getUserProfile(currentUser.uid);
      const createdByName = userProfile?.displayName || currentUser.displayName || currentUser.email || 'Unknown User';
      
      const kpiWithUser = {
        ...kpi,
        createdBy: currentUser.uid,
        createdByName: createdByName
      };

      await AuditService.addResolutionKPI(observation.id, kpiWithUser);
      await loadObservation(); // Refresh observation data
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم إضافة المؤشر بنجاح" : "KPI added successfully",
        description: isRTL ? "تم إضافة مؤشر الأداء إلى الملاحظة" : "KPI has been added to the observation",
      });
    } catch (error) {
      console.error('Error adding KPI:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في إضافة المؤشر" : "Error adding KPI",
        description: isRTL ? "فشل في إضافة مؤشر الأداء" : "Failed to add KPI",
        variant: "destructive",
      });
    } finally {
      setIsLoadingKPI(false);
    }
  };

  const handleUpdateKPI = async (kpiId: string, update: Omit<KPIUpdate, 'id' | 'createdAt'>) => {
    if (!observation?.id) return;

    const currentUser = auth.currentUser;
    if (!currentUser) {
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في المصادقة" : "Authentication Error",
        description: isRTL ? "يجب تسجيل الدخول أولاً" : "Please log in first",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoadingKPI(true);
      
      const userProfile = await getUserProfile(currentUser.uid);
      const updatedByName = userProfile?.displayName || currentUser.displayName || currentUser.email || 'Unknown User';
      
      const updateWithUser = {
        ...update,
        updatedBy: currentUser.uid,
        updatedByName: updatedByName
      };

      await AuditService.updateKPIValue(observation.id, kpiId, updateWithUser);
      await loadObservation(); // Refresh observation data
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم تحديث المؤشر بنجاح" : "KPI updated successfully",
        description: isRTL ? "تم تحديث مؤشر الأداء" : "KPI has been updated",
      });
    } catch (error) {
      console.error('Error updating KPI:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في تحديث المؤشر" : "Error updating KPI",
        description: isRTL ? "فشل في تحديث مؤشر الأداء" : "Failed to update KPI",
        variant: "destructive",
      });
    } finally {
      setIsLoadingKPI(false);
    }
  };

  const handleRemoveKPI = async (kpiId: string) => {
    if (!observation?.id) return;

    try {
      await AuditService.removeResolutionKPI(observation.id, kpiId);
      await loadObservation(); // Refresh observation data
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم حذف المؤشر بنجاح" : "KPI removed successfully",
        description: isRTL ? "تم حذف مؤشر الأداء من الملاحظة" : "KPI has been removed from the observation",
      });
    } catch (error) {
      console.error('Error removing KPI:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في حذف المؤشر" : "Error removing KPI",
        description: isRTL ? "فشل في حذف مؤشر الأداء" : "Failed to remove KPI",
        variant: "destructive",
      });
    }
  };

  const handleOpenUpdateKPI = (kpi: ResolutionKPI) => {
    setSelectedKPI(kpi);
    setIsUpdateKPIModalOpen(true);
  };

  const handleFocusKPI = (kpiId: string) => {
    setFocusedKPIId(focusedKPIId === kpiId ? null : kpiId);
  };



  const handleSaveField = async (field: string, value: string) => {
    if (!observation?.id) return;

    try {
      await AuditService.updateObservation(observation.id, { [field]: value });
      setObservation(prev => prev ? { ...prev, [field]: value } : null);
      setEditingFields(prev => ({ ...prev, [field]: false }));
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم الحفظ بنجاح" : "Saved successfully",
        description: isRTL ? "تم حفظ التغييرات" : "Changes have been saved",
      });
    } catch (error) {
      console.error('Error saving field:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Error saving",
        description: isRTL ? "فشل في حفظ التغييرات" : "Failed to save changes",
        variant: "destructive",
      });
    }
  };

  const formatDate = (timestamp: Timestamp) => {
    const date = timestamp.toDate();
    return date.toLocaleDateString(lang === "ar" ? "ar-SA" : "en-US", {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRatingColor = (rating: ObservationRating) => {
    switch (rating) {
      case ObservationRating.LOW: return 'text-green-600 bg-green-50 border-green-200';
      case ObservationRating.MEDIUM: return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case ObservationRating.HIGH: return 'text-orange-600 bg-orange-50 border-orange-200';
      case ObservationRating.CRITICAL: return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const handleGoBack = () => {
    router.push(`/${lang}/Thiqah/AuditFindings`);
  };

  // Don't render until params are loaded
  if (!lang || !observationId) {
    return <div>Loading...</div>;
  }

  const isRTL = lang === "ar";

  if (isLoading) {
    return (
      <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
        <div className="relative min-h-screen bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
          <div className="relative z-10 flex flex-col justify-center items-center min-h-screen px-8 py-16">
            <div className="animate-pulse text-center">
              <div className="w-24 h-24 bg-white/20 rounded-3xl mx-auto mb-8"></div>
              <div className="h-12 bg-white/20 rounded-lg w-96 mx-auto mb-4"></div>
              <div className="h-6 bg-white/20 rounded-lg w-64 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!observation) {
    return null;
  }

  const tabs = [
    {
      id: 'details' as const,
      label: isRTL ? "تفاصيل الملاحظة" : "Observation Details",
      subtitle: isRTL ? "المعلومات والمخاطر" : "Information & Risks",
      icon: FileSearch
    },
    {
      id: 'resolution' as const,
      label: isRTL ? "تفاصيل الحل" : "Resolution Details",
      subtitle: isRTL ? "الحلول والمتابعة" : "Solutions & Follow-up",
      icon: CheckCircle
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Header */}
      <div className="relative bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
        </div>

        <div className="relative z-10 px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            {/* Back Button */}
            <Button
              onClick={handleGoBack}
              className="bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 hover:text-white shadow-lg"
            >
              {isRTL ? <ArrowRight className="w-4 h-4 mr-2" /> : <ArrowLeft className="w-4 h-4 mr-2" />}
              {isRTL ? "العودة للملاحظات" : "Back to Observations"}
            </Button>
          </div>

          {/* Observation Header */}
          <div className="flex items-center gap-6 mb-6">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center shadow-lg border border-white/20">
              <FileSearch className="w-8 h-8 text-white" />
            </div>
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-1 tracking-tight">
                {observation.name}
              </h1>
              <p className="text-white/90 text-lg">
                #{observation.observationNumber}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <span className={`px-4 py-2 rounded-xl text-sm font-semibold border ${getRatingColor(observation.rating)}`}>
                {observation.rating === ObservationRating.LOW ? (isRTL ? "منخفضة" : "Low") :
                 observation.rating === ObservationRating.MEDIUM ? (isRTL ? "متوسطة" : "Medium") :
                 observation.rating === ObservationRating.HIGH ? (isRTL ? "عالية" : "High") :
                 (isRTL ? "حرجة" : "Critical")}
              </span>
            </div>
          </div>

          {/* Quick Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              { 
                icon: User, 
                label: isRTL ? "أنشأ بواسطة" : "Created by", 
                value: observation.createdByName 
              },
              { 
                icon: Calendar, 
                label: isRTL ? "تاريخ الإنشاء" : "Created", 
                value: formatDate(observation.createdAt) 
              },
              { 
                icon: AlertTriangle, 
                label: isRTL ? "المخاطر" : "Risks", 
                value: `${observation.risks?.length || 0} ${isRTL ? "مخاطرة" : "risks"}` 
              }
            ].map((item, index) => (
              <div
                key={index}
                className="bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-300"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-white/20 text-white">
                    <item.icon className="w-4 h-4" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="text-xs font-semibold text-white/80 uppercase tracking-wider">{item.label}</div>
                    <div className="text-sm font-bold text-white truncate" title={item.value}>{item.value}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-8 py-8">
        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-2xl p-1 shadow-lg border border-gray-200">
            <div className="flex gap-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-6 py-3 rounded-xl transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-[var(--brand-blue)] text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  <span className="font-medium">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="max-w-7xl mx-auto">
          {/* Observation Details Tab */}
          {activeTab === 'details' && (
            <motion.div
              key="details"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              {/* Basic Information */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                  <FileText className="w-6 h-6 text-[var(--brand-blue)]" />
                  {isRTL ? "المعلومات الأساسية" : "Basic Information"}
                </h3>
                
                {observation.description && (
                  <div className="bg-gray-50 rounded-xl p-6 mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {isRTL ? "وصف الملاحظة" : "Observation Description"}
                    </label>
                    <p className="text-gray-800 leading-relaxed">{observation.description}</p>
                  </div>
                )}
              </div>

              {/* Background Information */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                    <Shield className="w-6 h-6 text-[var(--brand-blue)]" />
                    {isRTL ? "المعلومات الخلفية" : "Background Information"}
                  </h3>
                  <Button
                    onClick={() => setEditingFields(prev => ({ ...prev, backgroundInformation: !prev.backgroundInformation }))}
                    variant="outline"
                    size="sm"
                    className="text-[var(--brand-blue)] border-[var(--brand-blue)] hover:bg-[var(--brand-blue)] hover:text-white"
                  >
                    {editingFields.backgroundInformation ? <X className="w-4 h-4" /> : <Edit className="w-4 h-4" />}
                  </Button>
                </div>

                {editingFields.backgroundInformation ? (
                  <div className="space-y-4">
                    <Textarea
                      value={formData.backgroundInformation}
                      onChange={(e) => setFormData(prev => ({ ...prev, backgroundInformation: e.target.value }))}
                      placeholder={isRTL ? "اكتب المعلومات الخلفية للملاحظة..." : "Enter background information for the observation..."}
                      className="min-h-32"
                    />
                    <div className="flex gap-3">
                      <Button
                        onClick={() => handleSaveField('backgroundInformation', formData.backgroundInformation)}
                        className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
                      >
                        <Save className="w-4 h-4 mr-2" />
                        {isRTL ? "حفظ" : "Save"}
                      </Button>
                      <Button
                        onClick={() => {
                          setFormData(prev => ({ ...prev, backgroundInformation: observation.backgroundInformation || "" }));
                          setEditingFields(prev => ({ ...prev, backgroundInformation: false }));
                        }}
                        variant="outline"
                      >
                        {isRTL ? "إلغاء" : "Cancel"}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="bg-gray-50 rounded-xl p-6">
                    {observation.backgroundInformation ? (
                      <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">{observation.backgroundInformation}</p>
                    ) : (
                      <p className="text-gray-500 italic">
                        {isRTL ? "لم يتم إضافة معلومات خلفية بعد" : "No background information added yet"}
                      </p>
                    )}
                  </div>
                )}
              </div>

              {/* Risks Management */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                    <AlertTriangle className="w-6 h-6 text-[var(--brand-blue)]" />
                    {isRTL ? "إدارة المخاطر" : "Risks Management"}
                    {observation.risks && observation.risks.length > 0 && (
                      <span className="bg-[var(--brand-blue)]/10 text-[var(--brand-blue)] px-3 py-1 rounded-full text-sm">
                        {observation.risks.length}
                      </span>
                    )}
                  </h3>
                  <Button
                    onClick={() => setIsAddRiskModalOpen(true)}
                    className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 text-white"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {isRTL ? "إضافة مخاطرة" : "Add Risk"}
                  </Button>
                </div>

                {observation.risks && observation.risks.length > 0 ? (
                  <div className="space-y-4">
                    {observation.risks.map((risk, index) => (
                      <div key={risk.id || index} className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 mb-2">
                              {isRTL ? `المخاطرة ${index + 1}` : `Risk ${index + 1}`}
                            </h4>
                            <p className="text-gray-700 leading-relaxed mb-3">{risk.description}</p>
                            
                            {(risk.impact || risk.probability || risk.mitigation) && (
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                                {risk.impact && (
                                  <div>
                                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      {isRTL ? "التأثير" : "Impact"}
                                    </label>
                                    <p className="text-sm text-gray-700">{risk.impact}</p>
                                  </div>
                                )}
                                {risk.probability && (
                                  <div>
                                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      {isRTL ? "الاحتمالية" : "Probability"}
                                    </label>
                                    <p className="text-sm text-gray-700">{risk.probability}</p>
                                  </div>
                                )}
                                {risk.mitigation && (
                                  <div className="md:col-span-3">
                                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      {isRTL ? "التخفيف" : "Mitigation"}
                                    </label>
                                    <p className="text-sm text-gray-700">{risk.mitigation}</p>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                          <Button
                            onClick={() => risk.id && handleRemoveRisk(risk.id)}
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <AlertTriangle className="w-8 h-8 text-gray-400" />
                    </div>
                    <p className="text-gray-500 mb-4">
                      {isRTL ? "لم يتم إضافة مخاطر بعد" : "No risks added yet"}
                    </p>
                    <Button
                      onClick={() => setIsAddRiskModalOpen(true)}
                      variant="outline"
                      className="text-[var(--brand-blue)] border-[var(--brand-blue)] hover:bg-[var(--brand-blue)] hover:text-white"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      {isRTL ? "إضافة أول مخاطرة" : "Add First Risk"}
                    </Button>
                  </div>
                )}
              </div>

              {/* Recommendations */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                    <Target className="w-6 h-6 text-[var(--brand-blue)]" />
                    {isRTL ? "التوصيات" : "Recommendations"}
                  </h3>
                  <Button
                    onClick={() => setEditingFields(prev => ({ ...prev, recommendations: !prev.recommendations }))}
                    variant="outline"
                    size="sm"
                    className="text-[var(--brand-blue)] border-[var(--brand-blue)] hover:bg-[var(--brand-blue)] hover:text-white"
                  >
                    {editingFields.recommendations ? <X className="w-4 h-4" /> : <Edit className="w-4 h-4" />}
                  </Button>
                </div>

                {editingFields.recommendations ? (
                  <div className="space-y-4">
                    <Textarea
                      value={formData.recommendations}
                      onChange={(e) => setFormData(prev => ({ ...prev, recommendations: e.target.value }))}
                      placeholder={isRTL ? "اكتب التوصيات..." : "Enter recommendations..."}
                      className="min-h-32"
                    />
                    <div className="flex gap-3">
                      <Button
                        onClick={() => handleSaveField('recommendations', formData.recommendations)}
                        className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
                      >
                        <Save className="w-4 h-4 mr-2" />
                        {isRTL ? "حفظ" : "Save"}
                      </Button>
                      <Button
                        onClick={() => {
                          setFormData(prev => ({ ...prev, recommendations: observation.recommendations || "" }));
                          setEditingFields(prev => ({ ...prev, recommendations: false }));
                        }}
                        variant="outline"
                      >
                        {isRTL ? "إلغاء" : "Cancel"}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="bg-gray-50 rounded-xl p-6">
                    {observation.recommendations ? (
                      <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">{observation.recommendations}</p>
                    ) : (
                      <p className="text-gray-500 italic">
                        {isRTL ? "لم يتم إضافة توصيات بعد" : "No recommendations added yet"}
                      </p>
                    )}
                  </div>
                )}
              </div>

              {/* Management Responses */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                    <MessageSquare className="w-6 h-6 text-[var(--brand-blue)]" />
                    {isRTL ? "ردود الإدارة" : "Management Responses"}
                    {observation.managementResponses && observation.managementResponses.length > 0 && (
                      <span className="bg-[var(--brand-blue)]/10 text-[var(--brand-blue)] px-3 py-1 rounded-full text-sm">
                        {observation.managementResponses.length}
                      </span>
                    )}
                  </h3>
                  <Button
                    onClick={() => setIsAddManagementResponseModalOpen(true)}
                    className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 text-white"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {isRTL ? "إضافة رد" : "Add Response"}
                  </Button>
                </div>

                {observation.managementResponses && observation.managementResponses.length > 0 ? (
                  <div className="space-y-6">
                    {observation.managementResponses.map((response, index) => (
                      <div key={response.id} className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-start gap-4 flex-1">
                            <div className="w-12 h-12 bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                              <MessageSquare className="w-6 h-6 text-white" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h4 className="font-semibold text-gray-900">
                                  {isRTL ? `رد الإدارة ${index + 1}` : `Management Response ${index + 1}`}
                                </h4>
                                <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
                                  response.status === 'completed' ? 'bg-green-100 text-green-700' :
                                  response.status === 'in_progress' ? 'bg-yellow-100 text-yellow-700' :
                                  response.status === 'overdue' ? 'bg-red-100 text-red-700' :
                                  'bg-gray-100 text-gray-700'
                                }`}>
                                  {response.status === 'completed' ? (isRTL ? "مكتمل" : "Completed") :
                                   response.status === 'in_progress' ? (isRTL ? "قيد التنفيذ" : "In Progress") :
                                   response.status === 'overdue' ? (isRTL ? "متأخر" : "Overdue") :
                                   (isRTL ? "معلق" : "Pending")}
                                </span>
                              </div>
                              <p className="text-gray-800 leading-relaxed mb-4 whitespace-pre-wrap">{response.response}</p>
                              
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div className="bg-white/70 rounded-lg p-3">
                                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {isRTL ? "الشخص المسؤول" : "Responsible Person"}
                                  </label>
                                  <p className="text-gray-700 font-medium">{response.responsiblePerson}</p>
                                </div>
                                <div className="bg-white/70 rounded-lg p-3">
                                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {isRTL ? "الموعد المستهدف" : "Target Completion"}
                                  </label>
                                  <p className="text-gray-700 font-medium">
                                    {response.dateType === 'monthly' 
                                      ? `${[
                                          isRTL ? "يناير" : "January", isRTL ? "فبراير" : "February", isRTL ? "مارس" : "March",
                                          isRTL ? "أبريل" : "April", isRTL ? "مايو" : "May", isRTL ? "يونيو" : "June",
                                          isRTL ? "يوليو" : "July", isRTL ? "أغسطس" : "August", isRTL ? "سبتمبر" : "September",
                                          isRTL ? "أكتوبر" : "October", isRTL ? "نوفمبر" : "November", isRTL ? "ديسمبر" : "December"
                                        ][response.month! - 1]} ${response.year}`
                                      : `${isRTL ? "الربع" : "Q"}${response.quarter} ${response.year}`
                                    }
                                  </p>
                                </div>
                              </div>
                              
                              <div className="flex items-center justify-between mt-4">
                                <div className="text-xs text-gray-500">
                                  {isRTL ? "أضيف في" : "Added on"} {formatDate(response.createdAt)}
                                  {response.createdByName && (
                                    <span className="mx-1">{isRTL ? "بواسطة" : "by"} {response.createdByName}</span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                          <Button
                            onClick={() => handleRemoveManagementResponse(response.id)}
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <MessageSquare className="w-8 h-8 text-gray-400" />
                    </div>
                    <p className="text-gray-500 mb-4">
                      {isRTL ? "لم يتم إضافة ردود إدارة بعد" : "No management responses added yet"}
                    </p>
                    <Button
                      onClick={() => setIsAddManagementResponseModalOpen(true)}
                      variant="outline"
                      className="text-[var(--brand-blue)] border-[var(--brand-blue)] hover:bg-[var(--brand-blue)] hover:text-white"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      {isRTL ? "إضافة أول رد" : "Add First Response"}
                    </Button>
                  </div>
                )}
              </div>
            </motion.div>
          )}

          {/* Resolution Details Tab */}
          {activeTab === 'resolution' && (
            <motion.div
              key="resolution"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-8"
            >
              {/* KPI Management Section */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200">
                <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 rounded-t-2xl px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                        <BarChart3 className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-white">
                          {isRTL ? "مؤشرات الأداء الرئيسية" : "Key Performance Indicators"}
                        </h3>
                        <p className="text-white/80 text-sm">
                          {isRTL ? "متابعة تقدم الحل من خلال مؤشرات الأداء" : "Track resolution progress through performance metrics"}
                        </p>
                      </div>
                    </div>
                    <Button
                      onClick={() => setIsAddKPIModalOpen(true)}
                      className="bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 hover:text-white shadow-lg"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      {isRTL ? "إضافة مؤشر" : "Add KPI"}
                    </Button>
                  </div>
                </div>

                <div className="p-6">
                  {!observation?.resolutionKPIs || observation.resolutionKPIs.length === 0 ? (
                    // Empty State
                    <div className="text-center py-12">
                      <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <BarChart3 className="w-8 h-8 text-gray-400" />
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">
                        {isRTL ? "لا توجد مؤشرات أداء" : "No KPIs Defined"}
                      </h4>
                      <p className="text-gray-600 mb-6 max-w-md mx-auto">
                        {isRTL 
                          ? "ابدأ بإضافة مؤشرات الأداء لمتابعة تقدم الحل وقياس فعاليته"
                          : "Start by adding KPIs to track resolution progress and measure effectiveness"
                        }
                      </p>
                      <Button
                        onClick={() => setIsAddKPIModalOpen(true)}
                        className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        {isRTL ? "إضافة أول مؤشر أداء" : "Add First KPI"}
                      </Button>
                    </div>
                  ) : focusedKPIId ? (
                                        // Focused KPI View - Expanded within current layout
                    <div className="-mx-6 -mt-6">
                      {/* Back Button */}
                      <div className="bg-white border-b border-gray-200 px-8 py-4">
                        <Button
                          onClick={() => setFocusedKPIId(null)}
                          variant="outline"
                          className="border-[var(--brand-blue)]/20 text-[var(--brand-blue)] hover:bg-[var(--brand-blue)] hover:text-white"
                        >
                          <ArrowLeft className="w-4 h-4 mr-2" />
                          {isRTL ? "العودة للقائمة" : "Back to List"}
                        </Button>
                      </div>
                      
                      <div className="flex">
                        {/* Main Focused Content */}
                        <div className="flex-1 p-8 bg-gradient-to-br from-[var(--brand-blue)]/5 to-[var(--brand-blue)]/10">
                         {(() => {
                           const focusedKPI = observation?.resolutionKPIs?.find(kpi => kpi.id === focusedKPIId);
                           if (!focusedKPI) return null;
                          
                          const progress = focusedKPI.targetValue > 0 ? (focusedKPI.currentValue / focusedKPI.targetValue) * 100 : 0;
                          const statusColors = {
                            achieved: 'from-green-500 to-green-600',
                            on_track: 'from-blue-500 to-blue-600', 
                            at_risk: 'from-yellow-500 to-yellow-600',
                            behind: 'from-red-500 to-red-600',
                            overdue: 'from-red-700 to-red-800'
                          };
                          
                          const kpiTypeIcons = {
                            number: Hash,
                            percentage: Percent,
                            currency: DollarSign,
                            time: Timer
                          };
                          
                          const KPIIcon = kpiTypeIcons[focusedKPI.kpiType] || Hash;
                          
                                                     return (
                             <div className="space-y-8">
                               {/* Action Buttons - Top Right */}
                               <div className="flex justify-end gap-2">
                                 <Button
                                   onClick={() => handleOpenUpdateKPI(focusedKPI)}
                                   className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80"
                                 >
                                   <Activity className="w-4 h-4 mr-2" />
                                   {isRTL ? "تحديث" : "Update"}
                                 </Button>
                                 <Button
                                   onClick={() => handleRemoveKPI(focusedKPI.id)}
                                   variant="outline"
                                   className="border-red-200 text-red-600 hover:bg-red-50"
                                 >
                                   <Trash2 className="w-4 h-4 mr-2" />
                                   {isRTL ? "حذف" : "Delete"}
                                 </Button>
                               </div>

                              {/* KPI Hero Section */}
                              <div className={`bg-gradient-to-r ${statusColors[focusedKPI.status]} rounded-3xl p-8 text-white relative overflow-hidden`}>
                                {/* Background Pattern */}
                                <div className="absolute inset-0 opacity-10">
                                  <div className="absolute top-0 right-0 w-64 h-64 bg-white rounded-full -translate-y-1/2 translate-x-1/2"></div>
                                  <div className="absolute bottom-0 left-0 w-48 h-48 bg-white rounded-full translate-y-1/2 -translate-x-1/2"></div>
                                </div>
                                
                                <div className="relative z-10 flex items-center gap-6">
                                  <div className="w-24 h-24 bg-white/20 rounded-3xl flex items-center justify-center backdrop-blur-sm">
                                    <KPIIcon className="w-12 h-12 text-white" />
                                  </div>
                                  <div className="flex-1">
                                    <h2 className="text-4xl font-bold mb-2">{focusedKPI.name}</h2>
                                    <p className="text-white/90 text-lg mb-4">
                                      {focusedKPI.description || (isRTL ? "لا يوجد وصف" : "No description")}
                                    </p>
                                    <div className="flex items-center gap-6">
                                      <div>
                                        <p className="text-white/80 text-sm">{isRTL ? "القيمة الحالية" : "Current Value"}</p>
                                        <p className="text-3xl font-bold">{focusedKPI.currentValue} {focusedKPI.unit}</p>
                                      </div>
                                      <div>
                                        <p className="text-white/80 text-sm">{isRTL ? "الهدف" : "Target"}</p>
                                        <p className="text-3xl font-bold">{focusedKPI.targetValue} {focusedKPI.unit}</p>
                                      </div>
                                      <div>
                                        <p className="text-white/80 text-sm">{isRTL ? "التقدم" : "Progress"}</p>
                                        <p className="text-3xl font-bold">{progress.toFixed(1)}%</p>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                {/* Progress Bar */}
                                <div className="mt-6 relative z-10">
                                  <div className="w-full bg-white/20 rounded-full h-4">
                                    <div 
                                      className="h-4 bg-white rounded-full transition-all duration-1000"
                                      style={{ width: `${Math.min(progress, 100)}%` }}
                                    ></div>
                                  </div>
                                </div>
                              </div>

                              {/* Stats Grid */}
                              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                                {[
                                  { 
                                    label: isRTL ? "التحديثات" : "Updates", 
                                    value: focusedKPI.updates.length, 
                                    icon: Activity, 
                                    color: "from-blue-500 to-blue-600" 
                                  },
                                  { 
                                    label: isRTL ? "النوع" : "Type", 
                                    value: focusedKPI.kpiType, 
                                    icon: Target, 
                                    color: "from-green-500 to-green-600" 
                                  },
                                  { 
                                    label: isRTL ? "التكرار" : "Frequency", 
                                    value: focusedKPI.measurementFrequency, 
                                    icon: Calendar, 
                                    color: "from-purple-500 to-purple-600" 
                                  },
                                  { 
                                    label: isRTL ? "الحالة" : "Status", 
                                    value: focusedKPI.status === 'achieved' ? (isRTL ? "مُحقق" : "Achieved") :
                                           focusedKPI.status === 'on_track' ? (isRTL ? "على المسار" : "On Track") :
                                           focusedKPI.status === 'at_risk' ? (isRTL ? "في خطر" : "At Risk") :
                                           focusedKPI.status === 'behind' ? (isRTL ? "متأخر" : "Behind") :
                                           (isRTL ? "متأخر جداً" : "Overdue"), 
                                    icon: TrendingUp, 
                                    color: statusColors[focusedKPI.status] 
                                  }
                                ].map((stat, index) => (
                                  <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: index * 0.1 }}
                                    className={`bg-gradient-to-r ${stat.color} rounded-2xl p-6 text-white`}
                                  >
                                    <div className="flex items-center justify-between mb-4">
                                      <stat.icon className="w-8 h-8 text-white/80" />
                                      <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                                        <stat.icon className="w-6 h-6" />
                                      </div>
                                    </div>
                                    <h3 className="text-sm font-medium text-white/80 mb-1">{stat.label}</h3>
                                    <p className="text-2xl font-bold capitalize">{stat.value}</p>
                                  </motion.div>
                                ))}
                              </div>

                              {/* Update History */}
                              <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200">
                                <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                                  <Activity className="w-8 h-8 text-[var(--brand-blue)]" />
                                  {isRTL ? "تاريخ التحديثات" : "Update History"}
                                  <span className="bg-[var(--brand-blue)]/10 text-[var(--brand-blue)] px-3 py-1 rounded-full text-sm font-medium">
                                    {focusedKPI.updates.length}
                                  </span>
                                </h3>

                                {focusedKPI.updates.length === 0 ? (
                                  <div className="text-center py-12">
                                    <div className="w-20 h-20 bg-gray-100 rounded-3xl flex items-center justify-center mx-auto mb-6">
                                      <Activity className="w-10 h-10 text-gray-400" />
                                    </div>
                                    <h4 className="text-xl font-semibold text-gray-900 mb-3">
                                      {isRTL ? "لا توجد تحديثات بعد" : "No Updates Yet"}
                                    </h4>
                                    <p className="text-gray-600 mb-6">
                                      {isRTL 
                                        ? "ابدأ بإضافة أول تحديث لهذا المؤشر"
                                        : "Start by adding the first update for this KPI"
                                      }
                                    </p>
                                    <Button
                                      onClick={() => handleOpenUpdateKPI(focusedKPI)}
                                      className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80"
                                    >
                                      <Plus className="w-4 h-4 mr-2" />
                                      {isRTL ? "إضافة أول تحديث" : "Add First Update"}
                                    </Button>
                                  </div>
                                ) : (
                                  <div className="space-y-4">
                                    {focusedKPI.updates
                                      .sort((a, b) => b.createdAt.toMillis() - a.createdAt.toMillis())
                                      .map((update, updateIndex) => (
                                        <motion.div
                                          key={update.id}
                                          initial={{ opacity: 0, x: -20 }}
                                          animate={{ opacity: 1, x: 0 }}
                                          transition={{ delay: updateIndex * 0.1 }}
                                          className="bg-gradient-to-r from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300"
                                        >
                                          <div className="flex items-start justify-between">
                                            <div className="flex items-center gap-4 flex-1">
                                              <div className="w-16 h-16 bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 rounded-2xl flex items-center justify-center text-white">
                                                <span className="text-sm font-bold">#{focusedKPI.updates.length - updateIndex}</span>
                                              </div>
                                              <div className="flex-1">
                                                <h4 className="text-lg font-semibold text-gray-900 mb-1">
                                                  {isRTL ? "تحديث" : "Update"} #{focusedKPI.updates.length - updateIndex}
                                                </h4>
                                                <p className="text-gray-600 text-sm mb-2">
                                                  {update.updatedByName} • {formatDate(update.createdAt)}
                                                </p>
                                                {update.notes && (
                                                  <div className="bg-blue-50 rounded-lg p-3 mt-3">
                                                    <p className="text-gray-800 text-sm">{update.notes}</p>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                            <div className="text-right">
                                              <p className="text-3xl font-bold text-[var(--brand-blue)] mb-1">
                                                {update.value} {update.unit}
                                              </p>
                                              <p className="text-sm text-gray-600 mb-2">
                                                {((update.value / update.targetValue) * 100).toFixed(1)}% {isRTL ? "من الهدف" : "of target"}
                                              </p>
                                              <div className="w-32 bg-gray-200 rounded-full h-2">
                                                <div 
                                                  className="h-2 bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 rounded-full"
                                                  style={{ width: `${Math.min((update.value / update.targetValue) * 100, 100)}%` }}
                                                ></div>
                                              </div>
                                            </div>
                                          </div>
                                        </motion.div>
                                      ))}
                                  </div>
                                )}
                              </div>
                            </div>
                          );
                        })()}
                      </div>

                                                                          {/* KPI Sidebar */}
                          <div className="w-80 bg-white shadow-lg border-l border-gray-200 p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                              <BarChart3 className="w-5 h-5 text-[var(--brand-blue)]" />
                              {isRTL ? "جميع المؤشرات" : "All KPIs"}
                            </h3>
                            <div className="space-y-3">
                              {observation?.resolutionKPIs?.map((kpi) => {
                            const progress = kpi.targetValue > 0 ? (kpi.currentValue / kpi.targetValue) * 100 : 0;
                            const isSelected = kpi.id === focusedKPIId;
                            
                            return (
                              <motion.button
                                key={kpi.id}
                                onClick={() => setFocusedKPIId(kpi.id)}
                                className={`w-full text-left p-4 rounded-xl border transition-all duration-200 ${
                                  isSelected 
                                    ? 'bg-gradient-to-r from-[var(--brand-blue)]/10 to-[var(--brand-blue)]/5 border-[var(--brand-blue)]/30 shadow-lg' 
                                    : 'bg-gray-50 border-gray-200 hover:bg-gray-100 hover:border-gray-300'
                                }`}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                              >
                                <div className="flex items-center gap-3 mb-2">
                                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                                    isSelected ? 'bg-[var(--brand-blue)]/20' : 'bg-gray-200'
                                  }`}>
                                    <Hash className={`w-4 h-4 ${isSelected ? 'text-[var(--brand-blue)]' : 'text-gray-500'}`} />
                                  </div>
                                  <h4 className={`font-semibold text-sm truncate ${
                                    isSelected ? 'text-[var(--brand-blue)]' : 'text-gray-900'
                                  }`}>
                                    {kpi.name}
                                  </h4>
                                </div>
                                <div className="space-y-2">
                                  <div className="flex justify-between text-xs">
                                    <span className="text-gray-600">{kpi.currentValue} / {kpi.targetValue} {kpi.unit}</span>
                                    <span className={`font-bold ${isSelected ? 'text-[var(--brand-blue)]' : 'text-gray-900'}`}>
                                      {progress.toFixed(1)}%
                                    </span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                                    <div 
                                      className={`h-1.5 rounded-full transition-all duration-300 ${
                                        isSelected 
                                          ? 'bg-gradient-to-r from-[var(--brand-blue)] to-blue-600' 
                                          : progress >= 100 ? 'bg-green-500' :
                                            progress >= 80 ? 'bg-blue-500' :
                                            progress >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                                      }`}
                                      style={{ width: `${Math.min(progress, 100)}%` }}
                                    ></div>
                                  </div>
                                </div>
                              </motion.button>
                            );
                                                        })}
                            </div>
                          </div>
                        </div>
                    </div>
                  ) : (
                    // KPI Cards
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {observation.resolutionKPIs.map((kpi, index) => {
                        const progress = kpi.targetValue > 0 ? (kpi.currentValue / kpi.targetValue) * 100 : 0;
                        const statusColors = {
                          achieved: 'from-green-500 to-green-600 text-white',
                          on_track: 'from-blue-500 to-blue-600 text-white',
                          at_risk: 'from-yellow-500 to-yellow-600 text-white',
                          behind: 'from-red-500 to-red-600 text-white',
                          overdue: 'from-red-700 to-red-800 text-white'
                        };
                        
                        const kpiTypeIcons = {
                          number: Hash,
                          percentage: Percent,
                          currency: DollarSign,
                          time: Timer
                        };
                        
                        const KPIIcon = kpiTypeIcons[kpi.kpiType] || Hash;

                        return (
                          <motion.div
                            key={kpi.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="bg-gradient-to-br from-gray-50 to-white rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300"
                          >
                            {/* KPI Header - Clickable */}
                            <div 
                                                            onClick={() => handleFocusKPI(kpi.id)}
                               className="p-6 cursor-pointer hover:bg-gray-50/50 transition-colors rounded-t-xl"
                            >
                              <div className="flex items-start justify-between mb-4">
                                <div className="flex items-start gap-3 flex-1 min-w-0">
                                  <div className="w-10 h-10 bg-[var(--brand-blue)]/10 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <KPIIcon className="w-5 h-5 text-[var(--brand-blue)]" />
                                  </div>
                                  <div className="min-w-0 flex-1">
                                    <div className="flex items-center gap-2">
                                      <h4 className="font-bold text-gray-900 text-sm truncate" title={kpi.name}>
                                        {kpi.name}
                                      </h4>
                                                                             {focusedKPIId === kpi.id ? (
                                        <motion.div
                                          initial={{ rotate: 0 }}
                                          animate={{ rotate: 180 }}
                                          transition={{ duration: 0.2 }}
                                        >
                                          <ArrowRight className="w-4 h-4 text-gray-400" />
                                        </motion.div>
                                      ) : (
                                        <ArrowRight className="w-4 h-4 text-gray-400" />
                                      )}
                                    </div>
                                    <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                      {kpi.description || (isRTL ? "لا يوجد وصف" : "No description")}
                                    </p>
                                  </div>
                                </div>
                                <div className="flex gap-1 flex-shrink-0 ml-2">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleOpenUpdateKPI(kpi);
                                    }}
                                    className="w-8 h-8 text-gray-600 hover:text-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/10"
                                  >
                                    <Activity className="w-4 h-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleRemoveKPI(kpi.id);
                                    }}
                                    className="w-8 h-8 text-gray-600 hover:text-red-600 hover:bg-red-50"
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </Button>
                                </div>
                              </div>

                              {/* Status Badge */}
                              <div className={`inline-block px-3 py-1 rounded-full text-xs font-semibold mb-4 bg-gradient-to-r ${statusColors[kpi.status]}`}>
                                {kpi.status === 'achieved' ? (isRTL ? "تم الإنجاز" : "Achieved") :
                                 kpi.status === 'on_track' ? (isRTL ? "على المسار الصحيح" : "On Track") :
                                 kpi.status === 'at_risk' ? (isRTL ? "في خطر" : "At Risk") :
                                 kpi.status === 'overdue' ? (isRTL ? "متأخر عن الموعد" : "Overdue") :
                                 (isRTL ? "متأخر" : "Behind")}
                              </div>

                              {/* Progress Bar */}
                              <div className="mb-4">
                                <div className="flex justify-between items-center mb-2">
                                  <span className="text-xs font-medium text-gray-600">
                                    {isRTL ? "التقدم" : "Progress"}
                                  </span>
                                  <span className="text-xs font-bold text-gray-900">
                                    {progress.toFixed(1)}%
                                  </span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div 
                                    className={`h-2 rounded-full transition-all duration-500 ${
                                      progress >= 100 ? 'bg-green-500' :
                                      progress >= 80 ? 'bg-blue-500' :
                                      progress >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                                    }`}
                                    style={{ width: `${Math.min(progress, 100)}%` }}
                                  ></div>
                                </div>
                              </div>

                              {/* Values */}
                              <div className="grid grid-cols-2 gap-4 mb-4">
                                <div>
                                  <label className="text-xs font-medium text-gray-500">
                                    {isRTL ? "القيمة الحالية" : "Current"}
                                  </label>
                                  <p className="text-lg font-bold text-gray-900">
                                    {kpi.currentValue} <span className="text-sm font-medium text-gray-600">{kpi.unit}</span>
                                  </p>
                                </div>
                                <div>
                                  <label className="text-xs font-medium text-gray-500">
                                    {isRTL ? "الهدف" : "Target"}
                                  </label>
                                  <p className="text-lg font-bold text-gray-900">
                                    {kpi.targetValue} <span className="text-sm font-medium text-gray-600">{kpi.unit}</span>
                                  </p>
                                </div>
                              </div>

                              {/* Quick Stats */}
                              <div className="grid grid-cols-3 gap-2 text-center">
                                <div className="bg-blue-50 rounded-lg p-2">
                                  <p className="text-xs text-blue-600 font-medium">{isRTL ? "التحديثات" : "Updates"}</p>
                                  <p className="text-sm font-bold text-blue-800">{kpi.updates.length}</p>
                                </div>
                                <div className="bg-green-50 rounded-lg p-2">
                                  <p className="text-xs text-green-600 font-medium">{isRTL ? "النوع" : "Type"}</p>
                                  <p className="text-sm font-bold text-green-800 capitalize">{kpi.kpiType}</p>
                                </div>
                                <div className="bg-purple-50 rounded-lg p-2">
                                  <p className="text-xs text-purple-600 font-medium">{isRTL ? "التكرار" : "Frequency"}</p>
                                  <p className="text-sm font-bold text-purple-800 capitalize">{kpi.measurementFrequency}</p>
                                </div>
                              </div>
                            </div>

                                                         
                          </motion.div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Add Risk Modal */}
      <AddRiskModal
        isOpen={isAddRiskModalOpen}
        onClose={() => setIsAddRiskModalOpen(false)}
        onSubmit={handleAddRisk}
        isRTL={isRTL}
        isLoading={isAddingRisk}
      />

      {/* Add Management Response Modal */}
      <AddManagementResponseModal
        isOpen={isAddManagementResponseModalOpen}
        onClose={() => setIsAddManagementResponseModalOpen(false)}
        onSubmit={handleAddManagementResponse}
        isRTL={isRTL}
        isLoading={isAddingManagementResponse}
      />

      {/* Add KPI Modal */}
      <AddKPIModal
        isOpen={isAddKPIModalOpen}
        onClose={() => setIsAddKPIModalOpen(false)}
        onSubmit={handleAddKPI}
        isRTL={isRTL}
        isLoading={isLoadingKPI}
      />

      {/* Update KPI Modal */}
      <UpdateKPIModal
        isOpen={isUpdateKPIModalOpen}
        onClose={() => {
          setIsUpdateKPIModalOpen(false);
          setSelectedKPI(null);
        }}
        onSubmit={(update) => selectedKPI ? handleUpdateKPI(selectedKPI.id, update) : Promise.resolve()}
        kpi={selectedKPI}
        isRTL={isRTL}
        isLoading={isLoadingKPI}
      />


    </div>
  );
} 