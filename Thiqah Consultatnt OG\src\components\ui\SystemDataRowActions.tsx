"use client";

import React, { useState } from "react";
import { Trash2, <PERSON><PERSON>2, <PERSON><PERSON><PERSON>cle, XCircle, Eye, User } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { SystemData, SystemsService } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";
import { auth } from "@/Firebase/Authentication/authConfig";
import { getUserProfile } from "@/Firebase/firestore/services/UserService";

// Extended SystemData interface for review functionality
interface ExtendedSystemData extends SystemData {
  isReviewed?: boolean;
  reviewedBy?: string;
  reviewedAt?: string;
  needsReview?: boolean;
  pushToClient?: string;
}

interface SystemDataRowActionsProps {
  row: ExtendedSystemData;
  systemId: string;
  isRTL: boolean;
  onDataUpdate?: () => void;
}

export function SystemDataRowActions({ 
  row, 
  systemId, 
  isRTL, 
  onDataUpdate 
}: SystemDataRowActionsProps) {
  const { toast } = useToast();
  const [isDeleting, setIsDeleting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Delete individual attribute
  const handleDelete = async () => {
    if (!row.id) return;

    setIsDeleting(true);
    try {
      await SystemsService.deleteSystemDataDocument(systemId, row.id);

      toast({
        title: isRTL ? "تم الحذف" : "Deleted Successfully",
        description: isRTL ? 
          "تم حذف العنصر بنجاح" :
          "Item deleted successfully",
      });

      // Refresh data
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Delete error:', error);
      toast({
        title: isRTL ? "خطأ في الحذف" : "Delete Error",
        description: error instanceof Error ? error.message : "Failed to delete item",
        variant: "destructive"
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  // Mark as reviewed
  const handleMarkAsReviewed = async () => {
    setIsSaving(true);
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('User not authenticated');
      }

      const userProfile = await getUserProfile(user.uid);
      const reviewData = {
        isReviewed: true,
        reviewedBy: userProfile?.displayName || user.email || 'Unknown',
        reviewedAt: new Date().toISOString()
      };

      await SystemsService.updateSystemDataBatch(systemId, [{
        documentId: row.id || '',
        data: reviewData
      }]);

      toast({
        title: isRTL ? "تم التمييز" : "Marked as Reviewed",
        description: isRTL ? "تم تمييز العنصر كمراجع" : "Successfully marked as reviewed",
      });

      // Refresh data
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Review error:', error);
      toast({
        title: isRTL ? "خطأ في التمييز" : "Review Error",
        description: error instanceof Error ? error.message : "Failed to mark as reviewed",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Remove review mark
  const handleRemoveReview = async () => {
    setIsSaving(true);
    try {
      const reviewData = {
        isReviewed: false,
        reviewedBy: undefined,
        reviewedAt: undefined
      };

      await SystemsService.updateSystemDataBatch(systemId, [{
        documentId: row.id || '',
        data: reviewData
      }]);

      toast({
        title: isRTL ? "تم إزالة المراجعة" : "Review Mark Removed",
        description: isRTL ? "تم إزالة علامة المراجعة" : "Successfully removed review mark",
      });

      // Refresh data
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Remove review error:', error);
      toast({
        title: isRTL ? "خطأ في إزالة المراجعة" : "Remove Review Error",
        description: error instanceof Error ? error.message : "Failed to remove review mark",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Toggle needs review
  const handleToggleNeedsReview = async () => {
    setIsSaving(true);
    try {
      const needsReviewData = {
        needsReview: !row.needsReview
      };

      await SystemsService.updateSystemDataBatch(systemId, [{
        documentId: row.id || '',
        data: needsReviewData
      }]);

      toast({
        title: isRTL ? "تم التحديث" : "Updated",
        description: isRTL ? 
          (row.needsReview ? "تم إزالة تمييز يحتاج مراجعة" : "تم تمييز العنصر كيحتاج مراجعة") :
          (row.needsReview ? "Removed needs review mark" : "Marked as needs review"),
      });

      // Refresh data
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Toggle needs review error:', error);
      toast({
        title: isRTL ? "خطأ في التحديث" : "Update Error",
        description: error instanceof Error ? error.message : "Failed to update needs review status",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Toggle push to client
  const handleTogglePushToClient = async () => {
    setIsSaving(true);
    try {
      const currentValue = row.pushToClient;
      const newValue = currentValue === "Yes" ? "No" : "Yes";
      
      const pushToClientData = {
        pushToClient: newValue
      };

      await SystemsService.updateSystemDataBatch(systemId, [{
        documentId: row.id || '',
        data: pushToClientData
      }]);

      toast({
        title: isRTL ? "تم التحديث" : "Updated",
        description: isRTL ? 
          `تم تحديث إرسال للعميل إلى: ${newValue === "Yes" ? "نعم" : "لا"}` :
          `Updated push to client to: ${newValue}`,
      });

      // Refresh data
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Toggle push to client error:', error);
      toast({
        title: isRTL ? "خطأ في التحديث" : "Update Error",
        description: error instanceof Error ? error.message : "Failed to update push to client status",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <>
      <div className="flex items-center gap-1">
        {/* Delete Button */}
        <Button
          onClick={() => setShowDeleteConfirm(true)}
          disabled={isDeleting || isSaving}
          size="sm"
          variant="ghost"
          className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          {isDeleting ? (
            <Loader2 className="w-3 h-3 animate-spin" />
          ) : (
            <Trash2 className="w-3 h-3" />
          )}
        </Button>

        {/* Review Actions */}
        {row.isReviewed ? (
          <Button
            onClick={handleRemoveReview}
            disabled={isSaving}
            size="sm"
            variant="ghost"
            className="h-6 w-6 p-0 text-orange-600 hover:text-orange-700 hover:bg-orange-50"
            title={isRTL ? "إزالة المراجعة" : "Remove Review"}
          >
            {isSaving ? (
              <Loader2 className="w-3 h-3 animate-spin" />
            ) : (
              <XCircle className="w-3 h-3" />
            )}
          </Button>
        ) : (
          <Button
            onClick={handleMarkAsReviewed}
            disabled={isSaving}
            size="sm"
            variant="ghost"
            className="h-6 w-6 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
            title={isRTL ? "تمييز كمراجع" : "Mark as Reviewed"}
          >
            {isSaving ? (
              <Loader2 className="w-3 h-3 animate-spin" />
            ) : (
              <CheckCircle className="w-3 h-3" />
            )}
          </Button>
        )}

        {/* Needs Review Toggle */}
        <Button
          onClick={handleToggleNeedsReview}
          disabled={isSaving}
          size="sm"
          variant="ghost"
          className={`h-6 w-6 p-0 ${
            row.needsReview 
              ? "text-amber-600 hover:text-amber-700 hover:bg-amber-50" 
              : "text-gray-400 hover:text-amber-600 hover:bg-amber-50"
          }`}
          title={isRTL ? 
            (row.needsReview ? "إزالة يحتاج مراجعة" : "تمييز كيحتاج مراجعة") :
            (row.needsReview ? "Remove Needs Review" : "Mark as Needs Review")
          }
        >
          {isSaving ? (
            <Loader2 className="w-3 h-3 animate-spin" />
          ) : (
            <Eye className="w-3 h-3" />
          )}
        </Button>

        {/* Push to Client Toggle */}
        <Button
          onClick={handleTogglePushToClient}
          disabled={isSaving}
          size="sm"
          variant="ghost"
          className={`h-6 w-6 p-0 ${
            row.pushToClient === "Yes"
              ? "text-blue-600 hover:text-blue-700 hover:bg-blue-50" 
              : "text-gray-400 hover:text-blue-600 hover:bg-blue-50"
          }`}
          title={isRTL ? 
            `إرسال للعميل: ${row.pushToClient === "Yes" ? "نعم" : "لا"}` :
            `Push to Client: ${row.pushToClient || "No"}`
          }
        >
          {isSaving ? (
            <Loader2 className="w-3 h-3 animate-spin" />
          ) : (
            <User className="w-3 h-3" />
          )}
        </Button>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {isRTL ? "تأكيد الحذف" : "Confirm Delete"}
            </h3>
            <p className="text-gray-600 mb-6">
              {isRTL 
                ? `هل أنت متأكد من حذف "${row.columnName}"؟ لا يمكن التراجع عن هذا الإجراء.`
                : `Are you sure you want to delete "${row.columnName}"? This action cannot be undone.`
              }
            </p>
            <div className="flex gap-3 justify-end">
              <Button
                onClick={() => setShowDeleteConfirm(false)}
                variant="outline"
                disabled={isDeleting}
              >
                {isRTL ? "إلغاء" : "Cancel"}
              </Button>
              <Button
                onClick={handleDelete}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isDeleting ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4 mr-2" />
                )}
                {isRTL ? "حذف" : "Delete"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
