"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Settings, Plus, Edit, Trash2, Server, Globe, Shield, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { System } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";

interface Service {
  id: string;
  name: string;
  description: string;
  type: 'internal' | 'external' | 'third-party';
  dataProcessed: string[];
  purpose: string;
  provider: string;
  location: string;
  securityMeasures: string[];
  status: 'active' | 'inactive' | 'pending';
  createdAt: Date;
  updatedAt: Date;
}

interface ServicesTabProps {
  systemId: string;
  lang: string;
  system: System | null;
}

export function ServicesTab({ systemId, lang }: ServicesTabProps) {
  const [services, setServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);

  const { toast } = useToast();
  const isRTL = lang === "ar";

  const loadServices = useCallback(async () => {
    try {
      setIsLoading(true);
      // TODO: Implement API call to load services
      // For now, no mock data - empty state
      setServices([]);
    } catch (error) {
      console.error('Error loading services:', error);
      toast({
        title: isRTL ? "خطأ في التحميل" : "Loading Error",
        description: isRTL ? "فشل في تحميل الخدمات" : "Failed to load services",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [isRTL, toast]);

  useEffect(() => {
    loadServices();
  }, [systemId, loadServices]);

  const handleCreateService = () => {
    setIsCreating(true);
    // TODO: Implement create service modal
    toast({
      title: isRTL ? "إنشاء خدمة" : "Create Service",
      description: isRTL ? "قريباً..." : "Coming soon...",
    });
    setIsCreating(false);
  };

  const handleEditService = (serviceId: string) => {
    // TODO: Implement edit service functionality
    console.log('Edit service:', serviceId);
    toast({
      title: isRTL ? "تعديل الخدمة" : "Edit Service",
      description: isRTL ? "قريباً..." : "Coming soon...",
    });
  };

  const handleDeleteService = (serviceId: string) => {
    // TODO: Implement delete service functionality
    console.log('Delete service:', serviceId);
    toast({
      title: isRTL ? "حذف الخدمة" : "Delete Service",
      description: isRTL ? "قريباً..." : "Coming soon...",
    });
  };

  const getServiceTypeColor = (type: Service['type']) => {
    switch (type) {
      case 'internal':
        return 'bg-green-100 text-green-800';
      case 'external':
        return 'bg-blue-100 text-blue-800';
      case 'third-party':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: Service['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {isRTL ? "الخدمات" : "Services"}
          </h2>
          <p className="text-gray-600">
            {isRTL 
              ? "إدارة الخدمات التي تعالج البيانات الشخصية" 
              : "Manage services that process personal data"
            }
          </p>
        </div>
        <Button
          onClick={handleCreateService}
          disabled={isCreating}
          className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          {isRTL ? "إضافة خدمة" : "Add Service"}
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-[var(--brand-blue)]/10">
                <Server className="w-5 h-5 text-[var(--brand-blue)]" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{services.length}</div>
                <div className="text-sm text-gray-600">
                  {isRTL ? "إجمالي الخدمات" : "Total Services"}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-green-100">
                <Shield className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {services.filter(s => s.status === 'active').length}
                </div>
                <div className="text-sm text-gray-600">
                  {isRTL ? "خدمات نشطة" : "Active Services"}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-orange-100">
                <Globe className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {services.filter(s => s.type === 'third-party').length}
                </div>
                <div className="text-sm text-gray-600">
                  {isRTL ? "خدمات خارجية" : "Third-party"}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-yellow-100">
                <Clock className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {services.filter(s => s.status === 'pending').length}
                </div>
                <div className="text-sm text-gray-600">
                  {isRTL ? "قيد المراجعة" : "Pending Review"}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Services Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {services.map((service) => (
          <Card key={service.id} className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold text-gray-900">
                  {service.name}
                </CardTitle>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEditService(service.id)}
                    className="h-8 w-8 p-0"
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteService(service.id)}
                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <p className="text-sm text-gray-600">{service.description}</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <Badge className={getServiceTypeColor(service.type)}>
                  {service.type === 'internal' ? (isRTL ? "داخلي" : "Internal") :
                   service.type === 'external' ? (isRTL ? "خارجي" : "External") :
                   (isRTL ? "طرف ثالث" : "Third-party")}
                </Badge>
                <Badge className={getStatusColor(service.status)}>
                  {service.status === 'active' ? (isRTL ? "نشط" : "Active") :
                   service.status === 'inactive' ? (isRTL ? "غير نشط" : "Inactive") :
                   (isRTL ? "قيد المراجعة" : "Pending")}
                </Badge>
              </div>

              <div className="grid grid-cols-1 gap-3 text-sm">
                <div>
                  <div className="font-medium text-gray-700">
                    {isRTL ? "مقدم الخدمة" : "Provider"}
                  </div>
                  <div className="text-gray-600">{service.provider}</div>
                </div>
                <div>
                  <div className="font-medium text-gray-700">
                    {isRTL ? "الموقع" : "Location"}
                  </div>
                  <div className="text-gray-600">{service.location}</div>
                </div>
                <div>
                  <div className="font-medium text-gray-700">
                    {isRTL ? "الغرض" : "Purpose"}
                  </div>
                  <div className="text-gray-600">{service.purpose}</div>
                </div>
              </div>

              <div>
                <div className="text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "البيانات المعالجة" : "Data Processed"}
                </div>
                <div className="flex flex-wrap gap-1">
                  {service.dataProcessed.slice(0, 2).map((data) => (
                    <Badge key={data} variant="secondary" className="text-xs">
                      {data}
                    </Badge>
                  ))}
                  {service.dataProcessed.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{service.dataProcessed.length - 2} {isRTL ? "المزيد" : "more"}
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {services.length === 0 && (
        <div className="text-center py-12">
          <Settings className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {isRTL ? "لا توجد خدمات" : "No Services"}
          </h3>
          <p className="text-gray-600 mb-4">
            {isRTL 
              ? "ابدأ بإضافة خدمة جديدة تعالج البيانات الشخصية" 
              : "Start by adding a new service that processes personal data"
            }
          </p>
          <Button
            onClick={handleCreateService}
            className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            {isRTL ? "إضافة خدمة" : "Add Service"}
          </Button>
        </div>
      )}
    </div>
  );
}
