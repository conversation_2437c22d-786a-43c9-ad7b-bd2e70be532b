import { 
  getFirestore, 
  collection, 
  doc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  DocumentData,
  QueryConstraint
} from 'firebase/firestore';
import { firebaseApp } from '../config';

// Initialize Firestore
const firestore = getFirestore(firebaseApp);

// Helper functions
const createDocument = async (
  collectionPath: string, 
  docId: string, 
  data: DocumentData
): Promise<void> => {
  const docRef = doc(firestore, collectionPath, docId);
  await setDoc(docRef, data);
};

const getDocument = async (
  collectionPath: string, 
  docId: string
): Promise<DocumentData | null> => {
  const docRef = doc(firestore, collectionPath, docId);
  const docSnap = await getDoc(docRef);
  
  if (docSnap.exists()) {
    return { id: docSnap.id, ...docSnap.data() };
  } else {
    return null;
  }
};

const updateDocument = async (
  collectionPath: string, 
  docId: string, 
  data: DocumentData
): Promise<void> => {
  const docRef = doc(firestore, collectionPath, docId);
  await updateDoc(docRef, data);
};

const deleteDocument = async (
  collectionPath: string, 
  docId: string
): Promise<void> => {
  const docRef = doc(firestore, collectionPath, docId);
  await deleteDoc(docRef);
};

const queryDocuments = async (
  collectionPath: string,
  constraints: QueryConstraint[] = []
): Promise<DocumentData[]> => {
  const collectionRef = collection(firestore, collectionPath);
  const q = query(collectionRef, ...constraints);
  const querySnapshot = await getDocs(q);
  
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
};

export {
  firestore,
  createDocument,
  getDocument,
  updateDocument,
  deleteDocument,
  queryDocuments,
  collection,
  doc,
  query,
  where,
  orderBy,
  limit
}; 