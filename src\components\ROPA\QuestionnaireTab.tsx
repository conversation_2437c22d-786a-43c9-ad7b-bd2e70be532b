"use client";

import React, { useState, useEffect, useCallback } from "react";
import { FileText, Check<PERSON>ircle, AlertCircle, Clock, Save, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { System } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";

interface QuestionnaireQuestion {
  id: string;
  category: string;
  question: string;
  type: 'multiple-choice' | 'text' | 'checkbox' | 'yes-no';
  options?: string[];
  required: boolean;
  answer?: string | string[];
}

interface QuestionnaireTabProps {
  systemId: string;
  lang: string;
  system: System | null;
}

export function QuestionnaireTab({ systemId, lang }: QuestionnaireTabProps) {
  const [questions, setQuestions] = useState<QuestionnaireQuestion[]>([]);
  const [answers, setAnswers] = useState<Record<string, string | string[]>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [completionStatus, setCompletionStatus] = useState<{
    completed: number;
    total: number;
    percentage: number;
  }>({ completed: 0, total: 0, percentage: 0 });

  const { toast } = useToast();
  const isRTL = lang === "ar";

  const loadQuestionnaire = useCallback(async () => {
    try {
      setIsLoading(true);
      // TODO: Implement API call to load questionnaire
      // For now, no mock data - empty state
      setQuestions([]);
      setAnswers({});
    } catch (error) {
      console.error('Error loading questionnaire:', error);
      toast({
        title: isRTL ? "خطأ في التحميل" : "Loading Error",
        description: isRTL ? "فشل في تحميل الاستبيان" : "Failed to load questionnaire",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast, isRTL]);

  const calculateCompletionStatus = useCallback(() => {
    const requiredQuestions = questions.filter(q => q.required);
    const answeredRequired = requiredQuestions.filter(q => {
      const answer = answers[q.id];
      return answer && (Array.isArray(answer) ? answer.length > 0 : answer.trim() !== '');
    });

    const completed = answeredRequired.length;
    const total = requiredQuestions.length;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

    setCompletionStatus({ completed, total, percentage });
  }, [questions, answers]);

  useEffect(() => {
    loadQuestionnaire();
  }, [systemId, loadQuestionnaire]);

  useEffect(() => {
    calculateCompletionStatus();
  }, [answers, questions, calculateCompletionStatus]);

  const handleAnswerChange = (questionId: string, value: string | string[]) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      // TODO: Implement API call to save answers
      await new Promise(resolve => setTimeout(resolve, 1000)); // Mock delay
      
      toast({
        title: isRTL ? "تم الحفظ" : "Saved",
        description: isRTL ? "تم حفظ إجاباتك بنجاح" : "Your answers have been saved successfully",
      });
    } catch (error) {
      console.error('Error saving answers:', error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: isRTL ? "فشل في حفظ الإجابات" : "Failed to save answers",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const renderQuestion = (question: QuestionnaireQuestion) => {
    const answer = answers[question.id];

    switch (question.type) {
      case 'text':
        return (
          <Textarea
            value={(answer as string) || ''}
            onChange={(e) => handleAnswerChange(question.id, e.target.value)}
            placeholder={isRTL ? "اكتب إجابتك هنا..." : "Type your answer here..."}
            className="min-h-[100px]"
          />
        );

      case 'yes-no':
        return (
          <RadioGroup
            value={(answer as string) || ''}
            onValueChange={(value) => handleAnswerChange(question.id, value)}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="yes" id={`${question.id}-yes`} />
              <Label htmlFor={`${question.id}-yes`}>
                {isRTL ? "نعم" : "Yes"}
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="no" id={`${question.id}-no`} />
              <Label htmlFor={`${question.id}-no`}>
                {isRTL ? "لا" : "No"}
              </Label>
            </div>
          </RadioGroup>
        );

      case 'multiple-choice':
        return (
          <RadioGroup
            value={(answer as string) || ''}
            onValueChange={(value) => handleAnswerChange(question.id, value)}
          >
            {question.options?.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`${question.id}-${index}`} />
                <Label htmlFor={`${question.id}-${index}`}>
                  {option}
                </Label>
              </div>
            ))}
          </RadioGroup>
        );

      case 'checkbox':
        const selectedOptions = (answer as string[]) || [];
        return (
          <div className="space-y-2">
            {question.options?.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Checkbox
                  id={`${question.id}-${index}`}
                  checked={selectedOptions.includes(option)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      handleAnswerChange(question.id, [...selectedOptions, option]);
                    } else {
                      handleAnswerChange(question.id, selectedOptions.filter(o => o !== option));
                    }
                  }}
                />
                <Label htmlFor={`${question.id}-${index}`}>
                  {option}
                </Label>
              </div>
            ))}
          </div>
        );

      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-32 bg-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  // Group questions by category
  const questionsByCategory = questions.reduce((acc, question) => {
    if (!acc[question.category]) {
      acc[question.category] = [];
    }
    acc[question.category].push(question);
    return acc;
  }, {} as Record<string, QuestionnaireQuestion[]>);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {isRTL ? "استبيان ROPA" : "ROPA Questionnaire"}
          </h2>
          <p className="text-gray-600">
            {isRTL 
              ? "أجب على الأسئلة التالية لإنشاء سجل أنشطة المعالجة" 
              : "Answer the following questions to create your Record of Processing Activities"
            }
          </p>
        </div>
        <Button
          onClick={handleSave}
          disabled={isSaving}
          className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
        >
          {isSaving ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Save className="w-4 h-4 mr-2" />
          )}
          {isRTL ? "حفظ" : "Save"}
        </Button>
      </div>

      {/* Progress Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {isRTL ? "تقدم الاستبيان" : "Questionnaire Progress"}
              </h3>
              <p className="text-sm text-gray-600">
                {isRTL 
                  ? `${completionStatus.completed} من ${completionStatus.total} أسئلة مطلوبة مكتملة`
                  : `${completionStatus.completed} of ${completionStatus.total} required questions completed`
                }
              </p>
            </div>
            <div className="flex items-center gap-2">
              {completionStatus.percentage === 100 ? (
                <CheckCircle className="w-6 h-6 text-green-600" />
              ) : completionStatus.percentage > 0 ? (
                <Clock className="w-6 h-6 text-yellow-600" />
              ) : (
                <AlertCircle className="w-6 h-6 text-red-600" />
              )}
              <Badge 
                className={
                  completionStatus.percentage === 100 
                    ? "bg-green-100 text-green-800"
                    : completionStatus.percentage > 0
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-red-100 text-red-800"
                }
              >
                {completionStatus.percentage}%
              </Badge>
            </div>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-[var(--brand-blue)] h-2 rounded-full transition-all duration-300"
              style={{ width: `${completionStatus.percentage}%` }}
            ></div>
          </div>
        </CardContent>
      </Card>

      {/* Questions by Category */}
      <div className="space-y-8">
        {Object.entries(questionsByCategory).map(([category, categoryQuestions]) => (
          <Card key={category}>
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-gray-900">
                {category}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {categoryQuestions.map((question) => {
                const hasAnswer = answers[question.id] && 
                  (Array.isArray(answers[question.id]) 
                    ? (answers[question.id] as string[]).length > 0 
                    : (answers[question.id] as string).trim() !== '');

                return (
                  <div key={question.id} className="space-y-3">
                    <div className="flex items-start gap-2">
                      <Label className="text-sm font-medium text-gray-900 leading-relaxed">
                        {question.question}
                      </Label>
                      {question.required && (
                        <span className="text-red-500 text-sm">*</span>
                      )}
                      {hasAnswer && (
                        <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                      )}
                    </div>
                    {renderQuestion(question)}
                  </div>
                );
              })}
            </CardContent>
          </Card>
        ))}
      </div>

      {questions.length === 0 && (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {isRTL ? "لا توجد أسئلة" : "No Questions Available"}
          </h3>
          <p className="text-gray-600">
            {isRTL 
              ? "لم يتم تحميل أسئلة الاستبيان بعد" 
              : "Questionnaire questions have not been loaded yet"
            }
          </p>
        </div>
      )}
    </div>
  );
}
