"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { 
  Search, 
  Filter, 
  RotateCcw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ChevronDown,
  ChevronUp,
  FileText,
  Lightbulb,
  AlertCircle,
  BarChart3,
  Shield,
  TrendingUp
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { PolicyAnalysis } from "@/Firebase/firestore/SystemsService";

interface AnalysisDisplayProps {
  analysis: PolicyAnalysis;
  onReAnalyze: () => void;
  isReAnalyzing: boolean;
  isRTL: boolean;
}

export function AnalysisDisplay({ analysis, onReAnalyze, isReAnalyzing, isRTL }: AnalysisDisplayProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterDomain, setFilterDomain] = useState("all");
  const [filterCompliance, setFilterCompliance] = useState("all");
  const [expandedSentences, setExpandedSentences] = useState<Set<number>>(new Set());

  // Get unique domains for filter
  const uniqueDomains = Array.from(new Set(analysis.sentences.map(s => s.domain)));

  // Filter sentences
  const filteredSentences = analysis.sentences.filter(sentence => {
    const matchesSearch = sentence.sentence.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sentence.domain.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDomain = filterDomain === "all" || sentence.domain === filterDomain;
    const matchesCompliance = filterCompliance === "all" || 
                             (filterCompliance === "compliant" && sentence.isCompliant) ||
                             (filterCompliance === "non-compliant" && !sentence.isCompliant);
    
    return matchesSearch && matchesDomain && matchesCompliance;
  });

  const compliantSentences = analysis.sentences.filter(s => s.isCompliant).length;
  const compliancePercentage = analysis.sentences.length > 0 ? Math.round((compliantSentences / analysis.sentences.length) * 100) : 0;

  const toggleSentenceExpansion = (index: number) => {
    const newExpanded = new Set(expandedSentences);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedSentences(newExpanded);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600 bg-green-50 border-green-200";
    if (score >= 60) return "text-yellow-600 bg-yellow-50 border-yellow-200";
    return "text-red-600 bg-red-50 border-red-200";
  };

  const getComplianceIcon = (isCompliant: boolean, score: number) => {
    if (isCompliant && score >= 80) return <CheckCircle className="w-5 h-5 text-green-600" />;
    if (score >= 60) return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
    return <XCircle className="w-5 h-5 text-red-600" />;
  };

  const getDomainColor = (domain: string, index: number) => {
    const colors = [
      "bg-blue-100 text-blue-800 border-blue-200",
      "bg-purple-100 text-purple-800 border-purple-200",
      "bg-green-100 text-green-800 border-green-200",
      "bg-orange-100 text-orange-800 border-orange-200",
      "bg-pink-100 text-pink-800 border-pink-200",
      "bg-indigo-100 text-indigo-800 border-indigo-200",
      "bg-teal-100 text-teal-800 border-teal-200",
      "bg-red-100 text-red-800 border-red-200",
      "bg-yellow-100 text-yellow-800 border-yellow-200",
      "bg-gray-100 text-gray-800 border-gray-200"
    ];
    return colors[index % colors.length];
  };

  const getComplianceColor = (level: string) => {
    switch (level) {
      case 'Excellent': return 'bg-green-100 text-green-800 border-green-300';
      case 'Good': return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'Needs Improvement': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'Poor': return 'bg-red-100 text-red-800 border-red-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  return (
    <div className="space-y-8">
      {/* Analysis Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Overall Score Card */}
        <Card className="border-l-4 border-l-[var(--brand-blue)]">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-[var(--brand-blue)]">
              <BarChart3 className="w-5 h-5" />
              {isRTL ? "النتيجة الإجمالية" : "Overall Score"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-4xl font-bold text-[var(--brand-blue)] mb-2">
                {analysis.overallScore}%
              </div>
              <Badge variant="outline" className={`${getComplianceColor(analysis.complianceLevel)}`}>
                <Shield className="w-4 h-4 mr-1" />
                {isRTL ? 
                  (analysis.complianceLevel === 'Excellent' ? 'ممتاز' :
                   analysis.complianceLevel === 'Good' ? 'جيد' :
                   analysis.complianceLevel === 'Needs Improvement' ? 'يحتاج تحسين' : 'ضعيف')
                  : analysis.complianceLevel
                }
              </Badge>
              <Progress value={analysis.overallScore} className="mt-4" />
            </div>
          </CardContent>
        </Card>

        {/* Statistics Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              {isRTL ? "الإحصائيات" : "Statistics"}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">{isRTL ? "إجمالي الجمل" : "Total Sentences"}</span>
              <span className="font-semibold">{analysis.sentences.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">{isRTL ? "الجمل المتوافقة" : "Compliant"}</span>
              <span className="font-semibold text-green-600">{compliantSentences}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">{isRTL ? "المجالات المفقودة" : "Missing Domains"}</span>
              <span className="font-semibold text-red-600">{analysis.missingDomains.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">{isRTL ? "نسبة التوافق" : "Compliance Rate"}</span>
              <span className="font-semibold">{compliancePercentage}%</span>
            </div>
          </CardContent>
        </Card>

        {/* Actions Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Filter className="w-5 h-5" />
              {isRTL ? "الإجراءات" : "Actions"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Button
              onClick={onReAnalyze}
              disabled={isReAnalyzing}
              className="w-full bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
            >
              {isReAnalyzing ? (
                <>
                  <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  {isRTL ? "جاري إعادة التحليل..." : "Re-analyzing..."}
                </>
              ) : (
                <>
                  <RotateCcw className="w-4 h-4 mr-2" />
                  {isRTL ? "إعادة تحليل" : "Re-analyze"}
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Missing Domains */}
      {analysis.missingDomains.length > 0 && (
        <Card className="border-l-4 border-l-red-500 bg-red-50/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-red-800">
              <AlertTriangle className="w-6 h-6" />
              {isRTL ? "المجالات المفقودة" : "Missing Domains"}
              <Badge variant="destructive">{analysis.missingDomains.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              {analysis.missingDomains.map((domain) => (
                <div key={domain} className="bg-white rounded-lg p-4 border border-red-200">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-medium text-red-900">{domain}</h4>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            {isRTL ? "البحث والتصفية" : "Search & Filter"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder={isRTL ? "البحث في الجمل..." : "Search sentences..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={filterDomain} onValueChange={setFilterDomain}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder={isRTL ? "تصفية حسب المجال" : "Filter by Domain"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{isRTL ? "جميع المجالات" : "All Domains"}</SelectItem>
                {uniqueDomains.map(domain => (
                  <SelectItem key={domain} value={domain}>{domain}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={filterCompliance} onValueChange={setFilterCompliance}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder={isRTL ? "تصفية حسب التوافق" : "Filter by Compliance"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{isRTL ? "جميع الجمل" : "All Sentences"}</SelectItem>
                <SelectItem value="compliant">{isRTL ? "متوافقة" : "Compliant"}</SelectItem>
                <SelectItem value="non-compliant">{isRTL ? "غير متوافقة" : "Non-Compliant"}</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="mt-4 text-sm text-gray-600">
            {isRTL 
              ? `عرض ${filteredSentences.length} من ${analysis.sentences.length} جملة`
              : `Showing ${filteredSentences.length} of ${analysis.sentences.length} sentences`
            }
          </div>
        </CardContent>
      </Card>

      {/* Sentence Analysis */}
      <div className="space-y-4">
        {filteredSentences.map((sentence, index) => {
          const isExpanded = expandedSentences.has(index);
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.02 }}
            >
              <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-[var(--brand-blue)]">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex items-start gap-3 flex-1">
                      <div className="flex-shrink-0 mt-1">
                        {getComplianceIcon(sentence.isCompliant, sentence.completenessScore)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-3">
                          <span className="text-sm font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded">
                            #{index + 1}
                          </span>
                          <Badge variant="outline" className={`text-xs font-medium ${getDomainColor(sentence.domain, index)}`}>
                            {sentence.domain}
                          </Badge>
                          {sentence.issues.length > 0 && (
                            <Badge variant="destructive" className="text-xs">
                              {sentence.issues.length} {isRTL ? 'مشكلة' : 'issues'}
                            </Badge>
                          )}
                          {sentence.suggestions.length > 0 && (
                            <Badge variant="outline" className="text-xs border-green-300 text-green-700 bg-green-50">
                              {sentence.suggestions.length} {isRTL ? 'اقتراح' : 'suggestions'}
                            </Badge>
                          )}
                        </div>
                        <div className="bg-gray-50 rounded-lg p-3 border border-gray-200 mb-2">
                          <p className={`text-sm text-gray-800 leading-relaxed font-medium ${isRTL ? 'text-right' : 'text-left'}`}>
                            &ldquo;{sentence.sentence}&rdquo;
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 flex-shrink-0">
                      <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getScoreColor(sentence.completenessScore)}`}>
                        {sentence.completenessScore}%
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleSentenceExpansion(index)}
                        className="p-1"
                      >
                        {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                      </Button>
                    </div>
                  </div>
                </CardHeader>

                {isExpanded && (
                  <CardContent className="pt-0">
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-4"
                    >
                      {/* Domain Description */}
                      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                        <div className="flex items-center gap-2 mb-3">
                          <FileText className="w-5 h-5 text-blue-600" />
                          <span className="text-sm font-semibold text-blue-900">
                            {isRTL ? "متطلبات المجال حسب قانون حماية البيانات" : "PDPL Domain Requirements"}
                          </span>
                        </div>
                        <div className="bg-white rounded-md p-3 border border-blue-100">
                          <p className="text-sm text-blue-800 leading-relaxed font-medium">
                            {sentence.domainDescription}
                          </p>
                        </div>
                      </div>

                      {/* Issues */}
                      {sentence.issues.length > 0 && (
                        <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                          <div className="flex items-center gap-2 mb-3">
                            <AlertCircle className="w-5 h-5 text-red-600" />
                            <span className="text-sm font-semibold text-red-900">
                              {isRTL ? "المشاكل المحددة والثغرات التنظيمية" : "Specific Issues & Regulatory Gaps"}
                            </span>
                            <span className="bg-red-200 text-red-800 text-xs px-2 py-1 rounded-full font-medium">
                              {sentence.issues.length}
                            </span>
                          </div>
                          <div className="space-y-3">
                            {sentence.issues.map((issue, idx) => (
                              <div key={idx} className="bg-white rounded-md p-3 border border-red-100">
                                <div className="flex items-start gap-3">
                                  <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-xs font-bold text-red-700">{idx + 1}</span>
                                  </div>
                                  <div className="flex-1">
                                    <p className="text-sm text-red-800 leading-relaxed font-medium">
                                      {issue}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Suggestions */}
                      {sentence.suggestions.length > 0 && (
                        <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                          <div className="flex items-center gap-2 mb-3">
                            <Lightbulb className="w-5 h-5 text-green-600" />
                            <span className="text-sm font-semibold text-green-900">
                              {isRTL ? "اقتراحات التحسين التفصيلية" : "Detailed Enhancement Suggestions"}
                            </span>
                            <span className="bg-green-200 text-green-800 text-xs px-2 py-1 rounded-full font-medium">
                              {sentence.suggestions.length}
                            </span>
                          </div>
                          <div className="space-y-3">
                            {sentence.suggestions.map((suggestion, idx) => (
                              <div key={idx} className="bg-white rounded-md p-3 border border-green-100">
                                <div className="flex items-start gap-3">
                                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-xs font-bold text-green-700">{idx + 1}</span>
                                  </div>
                                  <div className="flex-1">
                                    <p className="text-sm text-green-800 leading-relaxed font-medium">
                                      {suggestion}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </motion.div>
                  </CardContent>
                )}
              </Card>
            </motion.div>
          );
        })}

        {filteredSentences.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Filter className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {isRTL ? "لا توجد نتائج" : "No Results Found"}
              </h3>
              <p className="text-gray-600">
                {isRTL ? "جرب تغيير معايير البحث أو التصفية" : "Try adjusting your search or filter criteria"}
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
