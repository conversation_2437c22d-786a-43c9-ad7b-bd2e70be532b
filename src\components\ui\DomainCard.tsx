"use client";

import React from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";

interface DomainData {
  id: string;
  name: string;
  totalControls: number;
  totalSpecs: number;
}

interface DomainCardProps {
  domain: DomainData;
  onClick: () => void;
  isActive: boolean;
  index: number;
  lang: Locale;
}

export function DomainCard({ domain, onClick, isActive, index, lang }: DomainCardProps) {
  const isRTL = lang === "ar";
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      onClick={onClick}
      className={`
        cursor-pointer rounded-xl overflow-hidden transition-all duration-300
        border ${isActive ? 'border-[var(--brand-blue)]' : 'border-white/10'}
        ${isActive ? 'bg-[var(--brand-blue)]/10' : 'bg-white/5'}
        backdrop-blur-sm
      `}
    >
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="h-10 w-10 rounded-full bg-[var(--brand-blue)]/20 flex items-center justify-center">
            <svg className="w-5 h-5 text-[var(--brand-blue)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <div className="bg-white/10 rounded-lg px-2 py-1">
            <span className="text-white/70 text-sm">{isRTL ? "رمز" : "ID"}: {domain.id}</span>
          </div>
        </div>
        
        <h3 className="text-xl font-bold text-white mb-3">{domain.name}</h3>
        
        <div className="flex flex-col space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-white/70 text-sm">{isRTL ? "الضوابط" : "Controls"}</span>
            <span className="text-white font-medium">{domain.totalControls}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-white/70 text-sm">{isRTL ? "المواصفات" : "Specifications"}</span>
            <span className="text-white font-medium">{domain.totalSpecs}</span>
          </div>
        </div>
        
        <div className="mt-6">
          <div className={`
            flex items-center justify-center py-2 px-4 rounded-lg
            ${isActive 
              ? 'bg-[var(--brand-blue)] text-white' 
              : 'bg-white/10 text-white/70'}
            transition-all duration-300
          `}>
            <span className="text-sm font-medium">
              {isActive 
                ? (isRTL ? "عرض التفاصيل" : "Viewing Details") 
                : (isRTL ? "عرض التفاصيل" : "View Details")}
            </span>
            <svg className={`w-4 h-4 ${isRTL ? 'mr-2' : 'ml-2'} transition-transform duration-300 ${isActive ? 'rotate-90' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </div>
      </div>
    </motion.div>
  );
} 