import { FirebaseError } from 'firebase/app';

// Define error types
export type AuthErrorCode = 
  | 'auth/invalid-credential'
  | 'auth/user-not-found'
  | 'auth/wrong-password'
  | 'auth/email-already-in-use'
  | 'auth/weak-password'
  | 'auth/invalid-email'
  | 'auth/operation-not-allowed'
  | 'auth/account-exists-with-different-credential'
  | 'auth/network-request-failed'
  | 'auth/popup-closed-by-user'
  | 'auth/too-many-requests'
  | 'auth/user-disabled'
  | 'auth/requires-recent-login'
  | 'auth/user-token-expired'
  | 'auth/web-storage-unsupported'
  | 'auth/invalid-verification-code'
  | 'auth/missing-verification-code'
  | 'auth/captcha-check-failed'
  | 'auth/invalid-phone-number'
  | 'auth/missing-phone-number'
  | 'auth/quota-exceeded'
  | 'auth/provider-already-linked'
  | 'auth/credential-already-in-use'
  | 'auth/invalid-verification-id'
  | 'auth/missing-verification-id'
  | 'auth/invalid-continue-uri'
  | 'auth/missing-continue-uri'
  | 'auth/unauthorized-continue-uri'
  | 'auth/unauthorized-domain'
  | 'auth/invalid-dynamic-link-domain'
  | 'auth/argument-error'
  | 'auth/invalid-persistence-type'
  | 'auth/unsupported-persistence-type'
  | 'auth/invalid-oauth-provider'
  | 'auth/invalid-oauth-client-id'
  | 'auth/invalid-api-key'
  | 'auth/invalid-cert-hash'
  | 'auth/invalid-tenant-id'
  | 'auth/missing-tenant-id'
  | 'auth/missing-android-pkg-name'
  | 'auth/missing-ios-bundle-id'
  | 'auth/invalid-recipient-email'
  | 'auth/invalid-sender'
  | 'auth/invalid-message-payload'
  | 'auth/invalid-recipient-email'
  | 'auth/missing-iframe-start'
  | 'auth/missing-iframe-end'
  | 'auth/missing-app-credential'
  | 'auth/missing-verification-code'
  | 'auth/missing-verification-id'
  | 'auth/app-deleted'
  | 'auth/app-not-authorized'
  | 'auth/argument-error'
  | 'auth/invalid-api-key'
  | 'auth/invalid-app-credential'
  | 'auth/invalid-app-id'
  | 'auth/invalid-user-token'
  | 'auth/invalid-auth-event'
  | 'auth/invalid-verification-code'
  | 'auth/invalid-cordova-configuration'
  | 'auth/invalid-custom-token'
  | 'auth/invalid-email'
  | 'auth/invalid-credential'
  | 'auth/invalid-message-payload'
  | 'auth/invalid-oauth-client-id'
  | 'auth/invalid-oauth-provider'
  | 'auth/invalid-action-code'
  | 'auth/unauthorized-domain'
  | 'auth/wrong-password'
  | 'auth/invalid-recipient-email'
  | 'auth/invalid-sender'
  | 'auth/invalid-verification-id'
  | 'auth/missing-android-pkg-name'
  | 'auth/auth-domain-config-required'
  | 'auth/missing-app-credential'
  | 'auth/missing-verification-code'
  | 'auth/missing-verification-id'
  | 'auth/missing-iframe-start'
  | 'auth/missing-iframe-end'
  | 'auth/missing-ios-bundle-id'
  | 'auth/missing-or-invalid-nonce'
  | 'auth/missing-password'
  | 'auth/null-user'
  | 'auth/no-auth-event'
  | 'auth/no-such-provider'
  | 'auth/operation-not-allowed'
  | 'auth/operation-not-supported-in-this-environment'
  | 'auth/popup-blocked'
  | 'auth/popup-closed-by-user'
  | 'auth/provider-already-linked'
  | 'auth/quota-exceeded'
  | 'auth/redirect-cancelled-by-user'
  | 'auth/redirect-operation-pending'
  | 'auth/rejected-credential'
  | 'auth/timeout'
  | 'auth/user-token-expired'
  | 'auth/too-many-requests'
  | 'auth/unauthorized-continue-uri'
  | 'auth/unsupported-persistence-type'
  | 'auth/unsupported-tenant-operation'
  | 'auth/unverified-email'
  | 'auth/user-cancelled'
  | 'auth/user-not-found'
  | 'auth/user-disabled'
  | 'auth/user-mismatch'
  | 'auth/user-signed-out'
  | 'auth/weak-password'
  | 'auth/web-storage-unsupported'
  | 'auth/already-initialized'
  | 'auth/recaptcha-not-enabled'
  | 'auth/missing-recaptcha-token'
  | 'auth/invalid-recaptcha-token'
  | 'auth/invalid-recaptcha-action'
  | 'auth/missing-client-type'
  | 'auth/missing-recaptcha-version'
  | 'auth/invalid-recaptcha-version'
  | 'auth/invalid-req-type'
  | string;

// Function to get user-friendly error message
export function getAuthErrorMessage(error: unknown): string {
  // If it's a Firebase error, extract the error code
  if (error instanceof FirebaseError) {
    const errorCode = error.code as AuthErrorCode;
    
    // Map error codes to user-friendly messages
    switch (errorCode) {
      case 'auth/invalid-credential':
      case 'auth/user-not-found':
      case 'auth/wrong-password':
        return 'Invalid email or password';
        
      case 'auth/email-already-in-use':
        return 'This email is already in use';
        
      case 'auth/weak-password':
        return 'Password is too weak';
        
      case 'auth/invalid-email':
        return 'Invalid email address';
        
      case 'auth/operation-not-allowed':
        return 'Operation not allowed';
        
      case 'auth/account-exists-with-different-credential':
        return 'An account already exists with the same email address but different sign-in credentials';
        
      case 'auth/network-request-failed':
        return 'Network error. Please check your connection';
        
      case 'auth/popup-closed-by-user':
        return 'Sign-in popup was closed before completing the sign in';
        
      case 'auth/too-many-requests':
        return 'Too many unsuccessful login attempts. Please try again later';
        
      case 'auth/user-disabled':
        return 'This account has been disabled';
        
      case 'auth/requires-recent-login':
        return 'This operation requires recent authentication. Please sign in again';
        
      case 'auth/user-token-expired':
        return 'Your session has expired. Please sign in again';
        
      case 'auth/web-storage-unsupported':
        return 'Web storage is not supported or is disabled';
        
      default:
        // For any other Firebase auth error, return a generic message
        return 'Authentication error. Please try again';
    }
  }
  
  // For non-Firebase errors, return a generic message
  return 'An unexpected error occurred. Please try again';
}

// Function to handle auth errors (logs to console in development only)
export function handleAuthError(error: unknown): string {
  // In development, log the error to console for debugging
  if (process.env.NODE_ENV === 'development') {
    console.error('Auth error:', error);
  }
  
  // Return user-friendly error message
  return getAuthErrorMessage(error);
}
