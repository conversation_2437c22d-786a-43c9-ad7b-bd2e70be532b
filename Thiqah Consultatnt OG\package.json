{"name": "<PERSON><PERSON><PERSON>-consultant", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/google": "^1.2.12", "@formatjs/intl-localematcher": "^0.6.1", "@google/genai": "^1.0.0", "@google/generative-ai": "^0.24.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.7", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-collapsible": "^1.1.7", "@radix-ui/react-context-menu": "^2.2.11", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-hover-card": "^1.1.10", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.11", "@radix-ui/react-navigation-menu": "^1.2.9", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.3", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.8", "@radix-ui/react-toast": "^1.2.10", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.3", "@types/exceljs": "^0.5.3", "@types/negotiator": "^0.6.3", "accept-language": "^3.0.20", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookies-next": "^5.1.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "exceljs": "^4.4.0", "firebase": "^11.6.0", "framer-motion": "^12.7.4", "i18next": "^25.0.1", "i18next-browser-languagedetector": "^8.0.5", "i18next-resources-to-backend": "^1.2.1", "input-otp": "^1.4.2", "lucide-react": "^0.501.0", "mime": "^4.0.7", "negotiator": "^1.0.0", "next": "^15.4.2", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.0", "react-i18next": "^15.4.1", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.6", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.10", "@types/mime": "^3.0.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4.1.10", "typescript": "^5"}}