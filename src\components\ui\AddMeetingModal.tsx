"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { X, Plus, Trash2, CalendarDays, Users, FileText } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { MeetingType, MeetingStatus, MeetingAttendee, Meeting } from "@/Firebase/firestore/services/MeetingsService";
import { Timestamp } from "firebase/firestore";
import { auth } from "@/Firebase/Authentication/authConfig";
import { getAllUsers, UserProfile } from "@/Firebase/firestore/services/UserService";

interface AddMeetingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (meetingData: Omit<Meeting, 'id' | 'createdAt' | 'updatedAt'>) => void;
  isRTL: boolean;
}

export function AddMeetingModal({ isOpen, onClose, onSave, isRTL }: AddMeetingModalProps) {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    meetingDate: "",
    meetingTime: "",
    duration: 60,
    location: "",
    meetingType: MeetingType.INTERNAL,
    status: MeetingStatus.SCHEDULED,
    agenda: [] as string[],
    notes: ""
  });
  
  const [attendees, setAttendees] = useState<MeetingAttendee[]>([]);
  const [newAttendee, setNewAttendee] = useState({ name: "", email: "", role: "" });
  const [newAgendaItem, setNewAgendaItem] = useState("");
  const [consultants, setConsultants] = useState<UserProfile[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load consultants for attendee suggestions
  useEffect(() => {
    const loadConsultants = async () => {
      try {
        const users = await getAllUsers();
        setConsultants(users);
      } catch (error) {
        console.error("Error loading consultants:", error);
      }
    };
    
    if (isOpen) {
      loadConsultants();
    }
  }, [isOpen]);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setFormData({
        title: "",
        description: "",
        meetingDate: "",
        meetingTime: "",
        duration: 60,
        location: "",
        meetingType: MeetingType.INTERNAL,
        status: MeetingStatus.SCHEDULED,
        agenda: [],
        notes: ""
      });
      setAttendees([]);
      setNewAttendee({ name: "", email: "", role: "" });
      setNewAgendaItem("");
    }
  }, [isOpen]);

  const handleInputChange = (field: string, value: string | number | MeetingType | MeetingStatus | Date) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAddAttendee = () => {
    if (newAttendee.name && newAttendee.email) {
      // Check if this is a consultant
      const consultant = consultants.find(c => c.email === newAttendee.email);
      
      const attendee: MeetingAttendee = {
        name: newAttendee.name,
        email: newAttendee.email,
        role: newAttendee.role,
        isConsultant: !!consultant,
        consultantId: consultant?.uid
      };
      
      setAttendees(prev => [...prev, attendee]);
      setNewAttendee({ name: "", email: "", role: "" });
    }
  };

  const handleRemoveAttendee = (index: number) => {
    setAttendees(prev => prev.filter((_, i) => i !== index));
  };

  const handleAddAgendaItem = () => {
    if (newAgendaItem.trim()) {
      setFormData(prev => ({
        ...prev,
        agenda: [...prev.agenda, newAgendaItem.trim()]
      }));
      setNewAgendaItem("");
    }
  };

  const handleRemoveAgendaItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      agenda: prev.agenda.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.meetingDate || !formData.meetingTime) {
      return;
    }

    setIsLoading(true);
    
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) throw new Error("User not authenticated");

      // Combine date and time
      const meetingDateTime = new Date(`${formData.meetingDate}T${formData.meetingTime}`);
      
      const meetingData = {
        title: formData.title,
        description: formData.description,
        meetingDate: Timestamp.fromDate(meetingDateTime),
        duration: formData.duration,
        location: formData.location,
        meetingType: formData.meetingType,
        status: formData.status,
        organizer: {
          name: currentUser.displayName || currentUser.email?.split('@')[0] || 'User',
          email: currentUser.email || '',
          uid: currentUser.uid
        },
        attendees,
        agenda: formData.agenda,
        notes: formData.notes,
        createdBy: currentUser.uid,
        createdByName: currentUser.displayName || currentUser.email?.split('@')[0] || 'User'
      };

      await onSave(meetingData);
    } catch (error) {
      console.error("Error creating meeting:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleConsultantSelect = (consultantEmail: string) => {
    const consultant = consultants.find(c => c.email === consultantEmail);
    if (consultant && consultant.email) {
      setNewAttendee({
        name: consultant.displayName || consultant.email.split('@')[0],
        email: consultant.email,
        role: consultant.role || 'Consultant'
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-gray-900/20 backdrop-blur-sm"
        onClick={onClose}
      />
      
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="relative bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
            <div className="w-10 h-10 bg-[var(--brand-blue)]/10 rounded-xl flex items-center justify-center">
              <CalendarDays className="w-5 h-5 text-[var(--brand-blue)]" />
            </div>
            {isRTL ? "إضافة اجتماع جديد" : "Add New Meeting"}
          </h2>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "عنوان الاجتماع" : "Meeting Title"} *
                </label>
                <Input
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder={isRTL ? "أدخل عنوان الاجتماع" : "Enter meeting title"}
                  required
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "الوصف" : "Description"}
                </label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder={isRTL ? "وصف الاجتماع" : "Meeting description"}
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "التاريخ" : "Date"} *
                </label>
                <Input
                  type="date"
                  value={formData.meetingDate}
                  onChange={(e) => handleInputChange('meetingDate', e.target.value)}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "الوقت" : "Time"} *
                </label>
                <Input
                  type="time"
                  value={formData.meetingTime}
                  onChange={(e) => handleInputChange('meetingTime', e.target.value)}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "المدة (بالدقائق)" : "Duration (minutes)"}
                </label>
                <Input
                  type="number"
                  value={formData.duration}
                  onChange={(e) => handleInputChange('duration', parseInt(e.target.value))}
                  min="15"
                  step="15"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "الموقع" : "Location"}
                </label>
                <Input
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  placeholder={isRTL ? "موقع الاجتماع" : "Meeting location"}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "نوع الاجتماع" : "Meeting Type"}
                </label>
                <Select value={formData.meetingType} onValueChange={(value) => handleInputChange('meetingType', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="internal">{isRTL ? "داخلي" : "Internal"}</SelectItem>
                    <SelectItem value="client">{isRTL ? "عميل" : "Client"}</SelectItem>
                    <SelectItem value="vendor">{isRTL ? "مورد" : "Vendor"}</SelectItem>
                    <SelectItem value="stakeholder">{isRTL ? "أصحاب مصلحة" : "Stakeholder"}</SelectItem>
                    <SelectItem value="review">{isRTL ? "مراجعة" : "Review"}</SelectItem>
                    <SelectItem value="other">{isRTL ? "أخرى" : "Other"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? "الحالة" : "Status"}
                </label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="scheduled">{isRTL ? "مجدولة" : "Scheduled"}</SelectItem>
                    <SelectItem value="in_progress">{isRTL ? "جارية" : "In Progress"}</SelectItem>
                    <SelectItem value="completed">{isRTL ? "مكتملة" : "Completed"}</SelectItem>
                    <SelectItem value="cancelled">{isRTL ? "ملغية" : "Cancelled"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Attendees Section */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Users className="w-5 h-5 text-[var(--brand-blue)]" />
                {isRTL ? "المشاركون" : "Attendees"}
              </h3>

              {/* Add Attendee */}
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                  <div>
                    <Input
                      placeholder={isRTL ? "الاسم" : "Name"}
                      value={newAttendee.name}
                      onChange={(e) => setNewAttendee(prev => ({ ...prev, name: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Input
                      type="email"
                      placeholder={isRTL ? "البريد الإلكتروني" : "Email"}
                      value={newAttendee.email}
                      onChange={(e) => setNewAttendee(prev => ({ ...prev, email: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Input
                      placeholder={isRTL ? "الدور" : "Role"}
                      value={newAttendee.role}
                      onChange={(e) => setNewAttendee(prev => ({ ...prev, role: e.target.value }))}
                    />
                  </div>
                  <Button
                    type="button"
                    onClick={handleAddAttendee}
                    disabled={!newAttendee.name || !newAttendee.email}
                    className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {isRTL ? "إضافة" : "Add"}
                  </Button>
                </div>

                {/* Consultant Quick Add */}
                {consultants.length > 0 && (
                  <div className="mt-3">
                    <label className="block text-xs font-medium text-gray-600 mb-2">
                      {isRTL ? "إضافة سريعة للاستشاريين:" : "Quick add consultants:"}
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {consultants.slice(0, 5).filter(consultant => consultant.email).map((consultant) => (
                        <Button
                          key={consultant.uid}
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleConsultantSelect(consultant.email!)}
                          className="text-xs"
                        >
                          {consultant.displayName || consultant.email!.split('@')[0]}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Attendees List */}
              {attendees.length > 0 && (
                <div className="space-y-2">
                  {attendees.map((attendee, index) => (
                    <div key={index} className="flex items-center justify-between bg-white border border-gray-200 rounded-lg p-3">
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${
                          attendee.isConsultant ? 'bg-[var(--brand-blue)]' : 'bg-gray-500'
                        }`}>
                          {attendee.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{attendee.name}</p>
                          <p className="text-sm text-gray-600">{attendee.email}</p>
                          {attendee.role && (
                            <p className="text-xs text-gray-500">{attendee.role}</p>
                          )}
                        </div>
                        {attendee.isConsultant && (
                          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                            {isRTL ? "استشاري" : "Consultant"}
                          </Badge>
                        )}
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveAttendee(index)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Agenda Section */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <FileText className="w-5 h-5 text-[var(--brand-blue)]" />
                {isRTL ? "جدول الأعمال" : "Agenda"}
              </h3>

              {/* Add Agenda Item */}
              <div className="flex gap-3 mb-4">
                <Input
                  placeholder={isRTL ? "إضافة بند جديد لجدول الأعمال" : "Add new agenda item"}
                  value={newAgendaItem}
                  onChange={(e) => setNewAgendaItem(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddAgendaItem()}
                />
                <Button
                  type="button"
                  onClick={handleAddAgendaItem}
                  disabled={!newAgendaItem.trim()}
                  className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  {isRTL ? "إضافة" : "Add"}
                </Button>
              </div>

              {/* Agenda List */}
              {formData.agenda.length > 0 && (
                <div className="space-y-2">
                  {formData.agenda.map((item, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center gap-3">
                        <span className="w-6 h-6 bg-[var(--brand-blue)] text-white rounded-full flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </span>
                        <span className="text-gray-900">{item}</span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveAgendaItem(index)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Notes Section */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? "ملاحظات" : "Notes"}
              </label>
              <Textarea
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder={isRTL ? "ملاحظات إضافية حول الاجتماع" : "Additional notes about the meeting"}
                rows={4}
              />
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
          <Button variant="outline" onClick={onClose}>
            {isRTL ? "إلغاء" : "Cancel"}
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={isLoading || !formData.title || !formData.meetingDate || !formData.meetingTime}
            className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
            ) : null}
            {isRTL ? "إنشاء الاجتماع" : "Create Meeting"}
          </Button>
        </div>
      </motion.div>
    </div>
  );
}
