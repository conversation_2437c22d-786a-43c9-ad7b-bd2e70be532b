"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";
import {
  Calendar as CalendarIcon,
  Activity,
  CheckCircle,
  Clock,
  TrendingUp,
  Target,
  BarChart3
} from "lucide-react";

import { ProgressCalendar } from "@/components/ui/ProgressCalendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TasksService, Task, TimelineEntry } from "@/Firebase/firestore/services/TasksService";
import { useToast } from "@/components/ui/use-toast";

interface ProgressPageProps {
  params: Promise<{ lang: Locale }>;
}

interface DayStats {
  created: number;
  updated: number;
  completed: number;
  statusChanges: number;
  progressUpdates: number;
}

interface DayActivities {
  tasksCreated: Task[];
  tasksCompleted: Task[];
  timelineEntries: Array<{
    task: Task;
    entry: TimelineEntry;
  }>;
}

export default function ProgressPage({ params }: ProgressPageProps) {
  const [lang, setLang] = useState<Locale>("en");
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [dailyStats, setDailyStats] = useState<Map<string, DayStats>>(new Map());
  const [dayActivities, setDayActivities] = useState<DayActivities | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingDay, setIsLoadingDay] = useState(false);
  const { toast } = useToast();

  // Resolve params
  useEffect(() => {
    params.then((resolvedParams) => {
      setLang(resolvedParams.lang);
    });
  }, [params]);

  const isRTL = lang === "ar";

  // Load daily statistics for current month
  const loadDailyStats = useCallback(async () => {
    try {
      setIsLoading(true);
      const year = currentMonth.getFullYear();
      const month = currentMonth.getMonth();
      const stats = await TasksService.getDailyTaskStatistics(year, month);
      setDailyStats(stats);
    } catch (error) {
      console.error("Error loading daily stats:", error);
      toast({
        title: isRTL ? "خطأ في تحميل الإحصائيات" : "Error loading statistics",
        description: isRTL ? "حدث خطأ أثناء تحميل إحصائيات التقدم" : "An error occurred while loading progress statistics",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentMonth, isRTL, toast]);

  // Load activities for selected day
  const loadDayActivities = useCallback(async (date: Date) => {
    try {
      setIsLoadingDay(true);
      const activities = await TasksService.getTaskActivitiesForDate(date);
      setDayActivities(activities);
    } catch (error) {
      console.error("Error loading day activities:", error);
      toast({
        title: isRTL ? "خطأ في تحميل الأنشطة" : "Error loading activities",
        description: isRTL ? "حدث خطأ أثناء تحميل أنشطة اليوم" : "An error occurred while loading day activities",
        variant: "destructive",
      });
    } finally {
      setIsLoadingDay(false);
    }
  }, [isRTL, toast]);

  // Load data when component mounts or month changes
  useEffect(() => {
    if (lang) {
      loadDailyStats();
    }
  }, [lang, loadDailyStats]);

  // Load day activities when date is selected
  useEffect(() => {
    if (selectedDate && lang) {
      loadDayActivities(selectedDate);
    }
  }, [selectedDate, lang, loadDayActivities]);



  // Calculate monthly summary
  const monthlyStats = Array.from(dailyStats.values()).reduce(
    (acc, stats) => ({
      created: acc.created + stats.created,
      updated: acc.updated + stats.updated,
      completed: acc.completed + stats.completed,
      statusChanges: acc.statusChanges + stats.statusChanges,
      progressUpdates: acc.progressUpdates + stats.progressUpdates,
    }),
    { created: 0, updated: 0, completed: 0, statusChanges: 0, progressUpdates: 0 }
  );

  if (!lang) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[var(--brand-blue)]"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--brand-blue)]/5 via-white to-[var(--brand-blue)]/10">
      {/* Header */}
      <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 text-white">
        <div className="container mx-auto px-6 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center">
                <CalendarIcon className="w-8 h-8" />
              </div>
            </div>
            
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {isRTL ? "تتبع التقدم" : "Progress Tracking"}
            </h1>
            
            <p className="text-xl text-white/90 max-w-3xl mx-auto">
              {isRTL 
                ? "تتبع شامل لجميع الأنشطة والمهام مع عرض تفصيلي يومي لمراقبة التقدم والإنجازات"
                : "Comprehensive tracking of all activities and tasks with detailed daily view to monitor progress and achievements"
              }
            </p>
          </motion.div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        {/* Calendar and Statistics Row */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8 mb-8">
          {/* Calendar Section */}
          <div className="xl:col-span-3">
            <Card className="shadow-lg border-0">
              <CardHeader className="border-b border-gray-100">
                <CardTitle className="text-xl font-bold text-gray-900">
                  {isRTL ? "التقويم التفاعلي" : "Interactive Calendar"}
                </CardTitle>
              </CardHeader>

              <CardContent className="p-6">
                {isLoading ? (
                  <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--brand-blue)]"></div>
                  </div>
                ) : (
                  <ProgressCalendar
                    selectedDate={selectedDate}
                    onDateSelect={setSelectedDate}
                    currentMonth={currentMonth}
                    onMonthChange={setCurrentMonth}
                    dailyStats={dailyStats}
                    isRTL={isRTL}
                  />
                )}
              </CardContent>
            </Card>
          </div>

          {/* Monthly Statistics */}
          <div className="xl:col-span-1">
            <Card className="shadow-lg border-0 h-full">
              <CardHeader className="border-b border-gray-100">
                <CardTitle className="text-lg font-bold text-gray-900 flex items-center gap-2">
                  <BarChart3 className="w-5 h-5 text-[var(--brand-blue)]" />
                  {isRTL ? "إحصائيات الشهر" : "Monthly Statistics"}
                </CardTitle>
              </CardHeader>

              <CardContent className="p-4">
                <div className="space-y-4">
                  <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                        <Target className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-xs font-medium text-blue-700 uppercase tracking-wide">
                          {isRTL ? "مُنشأة" : "Created"}
                        </p>
                        <p className="text-2xl font-bold text-blue-600">{monthlyStats.created}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-xs font-medium text-green-700 uppercase tracking-wide">
                          {isRTL ? "مكتملة" : "Completed"}
                        </p>
                        <p className="text-2xl font-bold text-green-600">{monthlyStats.completed}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4 border border-yellow-200">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                        <Activity className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-xs font-medium text-yellow-700 uppercase tracking-wide">
                          {isRTL ? "محدثة" : "Updated"}
                        </p>
                        <p className="text-2xl font-bold text-yellow-600">{monthlyStats.updated}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                        <TrendingUp className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-xs font-medium text-purple-700 uppercase tracking-wide">
                          {isRTL ? "تقدم" : "Progress"}
                        </p>
                        <p className="text-2xl font-bold text-purple-600">{monthlyStats.progressUpdates}</p>
                      </div>
                    </div>
                  </div>

                  {/* Completion Rate */}
                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4 border border-gray-200">
                    <div className="text-center">
                      <p className="text-xs font-medium text-gray-700 uppercase tracking-wide mb-2">
                        {isRTL ? "معدل الإكمال" : "Completion Rate"}
                      </p>
                      <div className="relative">
                        <div className="w-16 h-16 mx-auto">
                          <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                            <path
                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                              fill="none"
                              stroke="#e5e7eb"
                              strokeWidth="2"
                            />
                            <path
                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                              fill="none"
                              stroke="#3b82f6"
                              strokeWidth="2"
                              strokeDasharray={`${monthlyStats.created > 0 ? (monthlyStats.completed / monthlyStats.created) * 100 : 0}, 100`}
                            />
                          </svg>
                        </div>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <span className="text-lg font-bold text-gray-700">
                            {monthlyStats.created > 0 ? Math.round((monthlyStats.completed / monthlyStats.created) * 100) : 0}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Selected Day Details */}
        {selectedDate && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="shadow-lg border-0">
              <CardHeader className="border-b border-gray-100 pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl font-bold text-gray-900 flex items-center gap-2">
                    <CalendarIcon className="w-5 h-5 text-[var(--brand-blue)]" />
                    {isRTL ? "تفاصيل يوم" : "Day Details"}
                  </CardTitle>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-[var(--brand-blue)]">
                      {selectedDate.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
                        weekday: 'long'
                      })}
                    </div>
                    <div className="text-sm text-gray-600">
                      {selectedDate.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </div>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="p-6">
                {isLoadingDay ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--brand-blue)]"></div>
                  </div>
                ) : dayActivities ? (
                  <div className="space-y-6">
                    {/* Day Summary */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center shadow-md">
                            <Target className="w-5 h-5 text-white" />
                          </div>
                          <div className="flex-1">
                            <p className="text-2xl font-bold text-blue-600">
                              {dayActivities.tasksCreated.length}
                            </p>
                            <p className="text-sm font-medium text-blue-700">
                              {isRTL ? "مهام جديدة" : "New Tasks"}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center shadow-md">
                            <CheckCircle className="w-5 h-5 text-white" />
                          </div>
                          <div className="flex-1">
                            <p className="text-2xl font-bold text-green-600">
                              {dayActivities.tasksCompleted.length}
                            </p>
                            <p className="text-sm font-medium text-green-700">
                              {isRTL ? "مهام مكتملة" : "Completed Tasks"}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center shadow-md">
                            <Activity className="w-5 h-5 text-white" />
                          </div>
                          <div className="flex-1">
                            <p className="text-2xl font-bold text-purple-600">
                              {dayActivities.timelineEntries.length}
                            </p>
                            <p className="text-sm font-medium text-purple-700">
                              {isRTL ? "التحديثات" : "Updates"}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Tasks Created */}
                    {dayActivities.tasksCreated.length > 0 && (
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                          <Target className="w-5 h-5 text-blue-600" />
                          {isRTL ? "المهام المُنشأة" : "Tasks Created"}
                          <Badge variant="outline" className="ml-2 bg-blue-50 text-blue-700">
                            {dayActivities.tasksCreated.length}
                          </Badge>
                        </h3>
                        <div className="grid gap-4">
                          {dayActivities.tasksCreated.map((task) => (
                            <div key={task.id} className="bg-white rounded-xl border border-blue-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                              <div className="p-6">
                                <div className="flex items-start justify-between mb-4">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                      <h4 className="text-lg font-semibold text-gray-900">{task.title}</h4>
                                    </div>
                                    <p className="text-gray-600 text-sm mb-3 leading-relaxed">{task.description}</p>
                                  </div>
                                  <div className="text-right ml-4">
                                    <div className="text-sm font-medium text-blue-600">
                                      {task.createdAt.toDate().toLocaleTimeString(isRTL ? 'ar-SA' : 'en-US', {
                                        hour: '2-digit',
                                        minute: '2-digit'
                                      })}
                                    </div>
                                    <div className="text-xs text-gray-500 mt-1">
                                      {isRTL ? "وقت الإنشاء" : "Created at"}
                                    </div>
                                  </div>
                                </div>

                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <Badge
                                      variant="outline"
                                      className={`text-xs ${
                                        task.priority === 'urgent' ? 'bg-red-50 text-red-700 border-red-200' :
                                        task.priority === 'high' ? 'bg-orange-50 text-orange-700 border-orange-200' :
                                        task.priority === 'medium' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                                        'bg-gray-50 text-gray-700 border-gray-200'
                                      }`}
                                    >
                                      {task.priority}
                                    </Badge>
                                    <Badge
                                      variant="outline"
                                      className={`text-xs ${
                                        task.status === 'completed' ? 'bg-green-50 text-green-700 border-green-200' :
                                        task.status === 'in_progress' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                                        task.status === 'pending' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                                        'bg-gray-50 text-gray-700 border-gray-200'
                                      }`}
                                    >
                                      {task.status}
                                    </Badge>
                                    {task.progress !== undefined && (
                                      <div className="flex items-center gap-2">
                                        <div className="w-16 bg-gray-200 rounded-full h-2">
                                          <div
                                            className={`h-2 rounded-full transition-all duration-300 ${
                                              task.progress >= 100 ? 'bg-green-500' :
                                              task.progress >= 75 ? 'bg-blue-500' :
                                              task.progress >= 50 ? 'bg-yellow-500' :
                                              'bg-red-500'
                                            }`}
                                            style={{ width: `${task.progress}%` }}
                                          ></div>
                                        </div>
                                        <span className="text-xs text-gray-600 font-medium">{task.progress}%</span>
                                      </div>
                                    )}
                                  </div>

                                  <div className="flex items-center gap-2 text-xs text-gray-500">
                                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                                      <span className="text-blue-600 font-medium text-xs">
                                        {task.createdByName.charAt(0).toUpperCase()}
                                      </span>
                                    </div>
                                    <span>{isRTL ? "بواسطة" : "by"} {task.createdByName}</span>
                                  </div>
                                </div>

                                {/* Assignees */}
                                {task.assignees && task.assignees.length > 0 && (
                                  <div className="mt-4 pt-4 border-t border-gray-100">
                                    <div className="flex items-center gap-2">
                                      <span className="text-xs text-gray-500">
                                        {isRTL ? "المكلفون:" : "Assignees:"}
                                      </span>
                                      <div className="flex items-center gap-1">
                                        {task.assignees.slice(0, 3).map((assignee, index) => (
                                          <div key={index} className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                                            <span className="text-gray-600 font-medium text-xs">
                                              {assignee.displayName.charAt(0).toUpperCase()}
                                            </span>
                                          </div>
                                        ))}
                                        {task.assignees.length > 3 && (
                                          <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                                            <span className="text-gray-600 font-medium text-xs">
                                              +{task.assignees.length - 3}
                                            </span>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Tasks Completed */}
                    {dayActivities.tasksCompleted.length > 0 && (
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                          <CheckCircle className="w-5 h-5 text-green-600" />
                          {isRTL ? "المهام المكتملة" : "Tasks Completed"}
                          <Badge variant="outline" className="ml-2 bg-green-50 text-green-700">
                            {dayActivities.tasksCompleted.length}
                          </Badge>
                        </h3>
                        <div className="grid gap-4">
                          {dayActivities.tasksCompleted.map((task) => (
                            <div key={task.id} className="bg-white rounded-xl border border-green-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                              <div className="p-6">
                                <div className="flex items-start justify-between mb-4">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                      <CheckCircle className="w-4 h-4 text-green-500" />
                                      <h4 className="text-lg font-semibold text-gray-900">{task.title}</h4>
                                    </div>
                                    <p className="text-gray-600 text-sm mb-3 leading-relaxed">{task.description}</p>
                                  </div>
                                  <div className="text-right ml-4">
                                    <div className="text-sm font-medium text-green-600">
                                      {task.completedAt?.toDate().toLocaleTimeString(isRTL ? 'ar-SA' : 'en-US', {
                                        hour: '2-digit',
                                        minute: '2-digit'
                                      })}
                                    </div>
                                    <div className="text-xs text-gray-500 mt-1">
                                      {isRTL ? "وقت الإكمال" : "Completed at"}
                                    </div>
                                  </div>
                                </div>

                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                                      {isRTL ? "مكتملة" : "Completed"}
                                    </Badge>
                                    <Badge
                                      variant="outline"
                                      className={`text-xs ${
                                        task.priority === 'urgent' ? 'bg-red-50 text-red-700 border-red-200' :
                                        task.priority === 'high' ? 'bg-orange-50 text-orange-700 border-orange-200' :
                                        task.priority === 'medium' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                                        'bg-gray-50 text-gray-700 border-gray-200'
                                      }`}
                                    >
                                      {task.priority}
                                    </Badge>

                                    {/* Completion Progress Bar */}
                                    <div className="flex items-center gap-2">
                                      <div className="w-16 bg-gray-200 rounded-full h-2">
                                        <div className="bg-green-500 h-2 rounded-full w-full"></div>
                                      </div>
                                      <span className="text-xs text-green-600 font-medium">100%</span>
                                    </div>

                                    {/* Duration Badge */}
                                    {task.createdAt && task.completedAt && (
                                      <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                        {(() => {
                                          const duration = task.completedAt.toMillis() - task.createdAt.toMillis();
                                          const days = Math.floor(duration / (1000 * 60 * 60 * 24));
                                          const hours = Math.floor((duration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

                                          if (days > 0) {
                                            return `${days}${isRTL ? ' يوم' : 'd'} ${hours}${isRTL ? ' ساعة' : 'h'}`;
                                          } else if (hours > 0) {
                                            return `${hours}${isRTL ? ' ساعة' : 'h'}`;
                                          } else {
                                            const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
                                            return `${minutes}${isRTL ? ' دقيقة' : 'm'}`;
                                          }
                                        })()}
                                      </Badge>
                                    )}
                                  </div>

                                  <div className="flex items-center gap-2 text-xs text-gray-500">
                                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                                      <span className="text-green-600 font-medium text-xs">
                                        {task.createdByName.charAt(0).toUpperCase()}
                                      </span>
                                    </div>
                                    <span>{isRTL ? "بواسطة" : "by"} {task.createdByName}</span>
                                  </div>
                                </div>

                                {/* Assignees */}
                                {task.assignees && task.assignees.length > 0 && (
                                  <div className="mt-4 pt-4 border-t border-gray-100">
                                    <div className="flex items-center gap-2">
                                      <span className="text-xs text-gray-500">
                                        {isRTL ? "المكلفون:" : "Assignees:"}
                                      </span>
                                      <div className="flex items-center gap-1">
                                        {task.assignees.slice(0, 3).map((assignee, index) => (
                                          <div key={index} className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                                            <span className="text-gray-600 font-medium text-xs">
                                              {assignee.displayName.charAt(0).toUpperCase()}
                                            </span>
                                          </div>
                                        ))}
                                        {task.assignees.length > 3 && (
                                          <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                                            <span className="text-gray-600 font-medium text-xs">
                                              +{task.assignees.length - 3}
                                            </span>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Grouped Timeline Activities */}
                    {dayActivities.timelineEntries.length > 0 && (
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                          <Activity className="w-5 h-5 text-purple-600" />
                          {isRTL ? "سجل الأنشطة مجمعة حسب المهمة" : "Activity Timeline Grouped by Task"}
                        </h3>

                        {(() => {
                          // Group timeline entries by task
                          const groupedByTask = dayActivities.timelineEntries.reduce((acc, { task, entry }) => {
                            if (!acc[task.id!]) {
                              acc[task.id!] = {
                                task,
                                entries: []
                              };
                            }
                            acc[task.id!].entries.push(entry);
                            return acc;
                          }, {} as Record<string, { task: Task; entries: TimelineEntry[] }>);

                          // Sort entries within each task by time (most recent first)
                          Object.values(groupedByTask).forEach(group => {
                            group.entries.sort((a, b) => b.createdAt.toMillis() - a.createdAt.toMillis());
                          });

                          return (
                            <div className="space-y-6">
                              {Object.values(groupedByTask).map(({ task, entries }) => (
                                <div key={task.id} className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                                  {/* Task Header */}
                                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
                                    <div className="flex items-center justify-between">
                                      <div className="flex-1">
                                        <h4 className="text-lg font-semibold text-gray-900 mb-1">{task.title}</h4>
                                        <p className="text-sm text-gray-600 line-clamp-2">{task.description}</p>
                                        <div className="flex items-center gap-3 mt-2">
                                          <Badge variant="outline" className="text-xs">
                                            {task.priority}
                                          </Badge>
                                          <Badge
                                            variant="outline"
                                            className={`text-xs ${
                                              task.status === 'completed' ? 'bg-green-100 text-green-800' :
                                              task.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                                              task.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                              'bg-gray-100 text-gray-800'
                                            }`}
                                          >
                                            {task.status}
                                          </Badge>
                                          {task.progress !== undefined && (
                                            <div className="flex items-center gap-2">
                                              <div className="w-20 bg-gray-200 rounded-full h-2">
                                                <div
                                                  className={`h-2 rounded-full transition-all duration-300 ${
                                                    task.progress >= 100 ? 'bg-green-500' :
                                                    task.progress >= 75 ? 'bg-blue-500' :
                                                    task.progress >= 50 ? 'bg-yellow-500' :
                                                    'bg-red-500'
                                                  }`}
                                                  style={{ width: `${task.progress}%` }}
                                                ></div>
                                              </div>
                                              <span className="text-xs text-gray-600 font-medium">{task.progress}%</span>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                      <div className="text-right">
                                        <div className="text-sm font-medium text-gray-900">
                                          {entries.length} {isRTL ? 'نشاط' : 'activities'}
                                        </div>
                                        <div className="text-xs text-gray-500">
                                          {isRTL ? "مُنشأة بواسطة" : "Created by"} {task.createdByName}
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                  {/* Timeline Entries */}
                                  <div className="p-6">
                                    <div className="relative">
                                      {/* Timeline Line */}
                                      <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                                      <div className="space-y-4">
                                        {entries.map((entry) => (
                                          <div key={entry.id} className="relative flex items-start gap-4">
                                            {/* Timeline Dot */}
                                            <div className={`relative z-10 w-3 h-3 rounded-full border-2 border-white shadow-sm ${
                                              entry.type === 'status_change' ? 'bg-blue-500' :
                                              entry.type === 'progress' ? 'bg-purple-500' :
                                              entry.type === 'update' ? 'bg-yellow-500' :
                                              'bg-gray-500'
                                            }`}></div>

                                            {/* Activity Content */}
                                            <div className="flex-1 min-w-0 pb-4">
                                              <div className="flex items-start justify-between">
                                                <div className="flex-1">
                                                  <div className="flex items-center gap-2 mb-1">
                                                    <Badge
                                                      variant="outline"
                                                      className={`text-xs ${
                                                        entry.type === 'status_change' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                                                        entry.type === 'progress' ? 'bg-purple-50 text-purple-700 border-purple-200' :
                                                        entry.type === 'update' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                                                        'bg-gray-50 text-gray-700 border-gray-200'
                                                      }`}
                                                    >
                                                      {entry.type === 'status_change' ? (isRTL ? 'تغيير حالة' : 'Status Change') :
                                                       entry.type === 'progress' ? (isRTL ? 'تحديث تقدم' : 'Progress Update') :
                                                       entry.type === 'update' ? (isRTL ? 'تحديث' : 'Update') :
                                                       entry.type}
                                                    </Badge>
                                                    <span className="text-xs text-gray-500">
                                                      {entry.createdAt.toDate().toLocaleTimeString(isRTL ? 'ar-SA' : 'en-US', {
                                                        hour: '2-digit',
                                                        minute: '2-digit'
                                                      })}
                                                    </span>
                                                  </div>

                                                  <h5 className="text-sm font-medium text-gray-900 mb-1">{entry.title}</h5>

                                                  {entry.description && (
                                                    <p className="text-sm text-gray-600 mb-2">{entry.description}</p>
                                                  )}

                                                  {/* Status Change Details */}
                                                  {entry.type === 'status_change' && entry.oldStatus && entry.newStatus && (
                                                    <div className="flex items-center gap-2 mb-2">
                                                      <Badge variant="outline" className="text-xs bg-red-50 text-red-700">
                                                        {entry.oldStatus}
                                                      </Badge>
                                                      <span className="text-xs text-gray-400">→</span>
                                                      <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                                                        {entry.newStatus}
                                                      </Badge>
                                                    </div>
                                                  )}

                                                  {/* Progress Details */}
                                                  {entry.progress !== undefined && (
                                                    <div className="flex items-center gap-3 mb-2">
                                                      <div className="flex-1 max-w-32 bg-gray-200 rounded-full h-2">
                                                        <div
                                                          className={`h-2 rounded-full transition-all duration-300 ${
                                                            entry.progress >= 100 ? 'bg-green-500' :
                                                            entry.progress >= 75 ? 'bg-blue-500' :
                                                            entry.progress >= 50 ? 'bg-yellow-500' :
                                                            'bg-red-500'
                                                          }`}
                                                          style={{ width: `${entry.progress}%` }}
                                                        ></div>
                                                      </div>
                                                      <span className="text-xs font-medium text-gray-700">{entry.progress}%</span>
                                                    </div>
                                                  )}

                                                  <div className="flex items-center gap-2 text-xs text-gray-500">
                                                    <span>{isRTL ? "بواسطة" : "by"} {entry.createdByName}</span>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          );
                        })()}
                      </div>
                    )}

                    {/* No Activities Message */}
                    {dayActivities.tasksCreated.length === 0 &&
                     dayActivities.tasksCompleted.length === 0 &&
                     dayActivities.timelineEntries.length === 0 && (
                      <div className="text-center py-12">
                        <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          {isRTL ? "لا توجد أنشطة" : "No Activities"}
                        </h3>
                        <p className="text-gray-600">
                          {isRTL
                            ? "لم يتم تسجيل أي أنشطة في هذا اليوم"
                            : "No activities were recorded on this day"
                          }
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {isRTL ? "اختر يوماً" : "Select a Day"}
                    </h3>
                    <p className="text-gray-600">
                      {isRTL
                        ? "اختر يوماً من التقويم لعرض الأنشطة التفصيلية"
                        : "Select a day from the calendar to view detailed activities"
                      }
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
}
