import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow processing up to 60 seconds for personal data analysis
export const maxDuration = 300; // 5 minutes

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Schema for personal data classification response - Updated to match PersonalDataTable requirements
const PersonalDataClassificationSchema = z.object({
  records: z.array(z.object({
    recordId: z.string(),
    personalDataType: z.enum(["direct", "indirect", "pseudonymous", "anonymous"]),
    pseudonyms: z.enum(["Yes", "No"]),
    anonyms: z.enum(["Yes", "No"]),
    specialCategoryType: z.enum(["PII", "PHI", "PCI", "Biometric", "Genetic", "none"]),
    pseudonymsJustification: z.string(),
    anonymsJustification: z.string()
  }))
});

// Schema for request body
const RequestSchema = z.object({
  pageData: z.array(z.object({
    id: z.string(),
    schemaName: z.string().optional(),
    tableName: z.string(),
    columnName: z.string(),
    dataType: z.string(),
    maxLength: z.number().nullable().optional(),
    isNullable: z.boolean().nullable().optional(),
    hasPersonalData: z.boolean().optional(),
    personalDataReasoning: z.string().optional()
  })),
  systemId: z.string(),
  pageNumber: z.number(),
  systemContext: z.string().optional()
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    const { pageData, systemId, pageNumber, systemContext } = RequestSchema.parse(body);

    if (!pageData || pageData.length === 0) {
      return NextResponse.json(
        { error: 'No personal data provided for classification' },
        { status: 400 }
      );
    }

    // Filter only personal data records
    const personalDataRecords = pageData.filter(record => record.hasPersonalData === true);

    if (personalDataRecords.length === 0) {
      return NextResponse.json(
        { error: 'No personal data records found in the provided data' },
        { status: 400 }
      );
    }

    // Use the records as-is since PersonalDataTable already handles pagination
    const recordsToProcess = personalDataRecords;
    console.log(`Processing ${recordsToProcess.length} personal data records for page ${pageNumber}`);

    // Get system information
    let systemName = '';
    let isSaberSystem = false;
    try {
      const { SystemsService } = await import('@/Firebase/firestore/SystemsService');
      const systems = await SystemsService.getSystems();
      const currentSystem = systems.find(sys => sys.id === systemId);
      
      if (currentSystem) {
        systemName = currentSystem.name;
        isSaberSystem = systemName.toLowerCase().includes('saber');
        console.log(`System identified: "${systemName}", SABER system: ${isSaberSystem}`);
      } else {
        console.log('System not found, using generic context');
      }
    } catch (error) {
      console.error('Error fetching system info:', error);
      console.log('Failed to fetch system info, using generic context');
    }

    // Get comprehensive system context from Firebase
    console.log('System context received:', systemContext ? `"${systemContext}"` : 'null/undefined');
    let detailedSystemContext = '';

    try {
      // Import SystemsService to get all context points
      const { SystemsService } = await import('@/Firebase/firestore/SystemsService');
      const contextPoints = await SystemsService.getSystemContextPoints(systemId);

      if (contextPoints && contextPoints.length > 0) {
        // Group context points by tag for better organization
        const contextByTag = contextPoints.reduce((acc, point) => {
          if (!acc[point.tag]) {
            acc[point.tag] = [];
          }
          acc[point.tag].push(point.content);
          return acc;
        }, {} as Record<string, string[]>);

        // Build comprehensive context
        const contextSections = [];

        if (contextByTag['System Description']) {
          contextSections.push(`SYSTEM DESCRIPTION:
${contextByTag['System Description'].join('\n\n')}`);
        }

        if (contextByTag['System Personas']) {
          contextSections.push(`SYSTEM PERSONAS & USERS:
${contextByTag['System Personas'].join('\n\n')}`);
        }

        if (contextByTag['System Service Brief']) {
          contextSections.push(`SYSTEM SERVICES & FUNCTIONS:
${contextByTag['System Service Brief'].join('\n\n')}`);
        }

        detailedSystemContext = contextSections.join('\n\n');
        console.log('Using comprehensive Firebase context for personal data classification');
      } else {
        console.log('No context points found in Firebase');
      }
    } catch (contextError) {
      console.error('Error fetching comprehensive context:', contextError);
      // Fallback to provided context
      if (systemContext && systemContext.trim()) {
        detailedSystemContext = `

ACTUAL BUSINESS CONTEXT FOR THIS SYSTEM:
${systemContext}

This context describes the specific business functions and processes that this database supports.`;
        console.log('Using fallback system context due to error');
      }
    }

    // Prepare system overview for personal data classification
    let systemOverview = '';
    let businessEntityContext = '';

    // Always use comprehensive context-driven approach
    systemOverview = `You are classifying personal data fields for the "${systemName}" system.

COMPREHENSIVE SYSTEM ANALYSIS:
${detailedSystemContext}

This system context provides detailed information about the business functions, user personas, and services that this database supports.`;

    if (isSaberSystem) {
      businessEntityContext = `

SABER PLATFORM SPECIFIC CONTEXT:
- SABER is Saudi Arabia's national conformity assessment system
- Manages product certificates, facility registrations, and compliance requests
- Handles sensitive business data for importers, manufacturers, and certification bodies
- Contains user accounts, facility information, and regulatory compliance data
- Manages financial transactions and payment processing through SADAD
- Stores audit trails and regulatory compliance documentation`;
    }

    const prompt = `${systemOverview}

${businessEntityContext}

You are a GDPR and PDPL (Personal Data Protection Law) expert specializing in personal data classification for Saudi Arabian data protection compliance.

COMPREHENSIVE SYSTEM ANALYSIS - YOU ARE AN EXPERT IN THIS SPECIFIC BUSINESS:
You are using Gemini-2.5-flash for efficient personal data classification. You are now an expert consultant who has deep knowledge of this specific business system. You understand the exact business processes, the specific user types, and how each service operates. When you analyze database fields, you think like someone who has worked with this system for years and understands every business nuance described in the context above.

CRITICAL: You must write as if you are intimately familiar with this specific business. Reference the exact business processes, mention the specific user types by name, and explain how each field impacts the particular services described. Do not use generic business language - use the specific terminology and concepts from the context as if this is your area of expertise.

CLASSIFICATION TASK:
Classify each personal data field based on FOUR dimensions that map to the PersonalDataTable columns:

1. PERSONAL DATA TYPE (Column 1 - Classification of data directness):
   - direct: Data that directly identifies an individual (e.g., full name, email address, phone number, national ID)
   - indirect: Data that could identify an individual when combined with other information (e.g., job title, department, age range, location)
   - pseudonymous: Data that has been pseudonymized but can still be linked to individuals with additional information
   - anonymous: Data that has been anonymized and cannot be linked back to individuals

2. PSEUDONYMS (Column 2 - Whether data can be pseudonymized):
   - Yes: Data can be replaced with artificial identifiers while maintaining utility for analysis (reversible with a key)
   - No: Data cannot be effectively pseudonymized without losing its essential value

3. ANONYMS (Column 3 - Whether data can be anonymized):
   - Yes: Data can be permanently stripped of identifying characteristics making re-identification impossible
   - No: Data cannot be anonymized without losing its essential purpose or value

CRITICAL DISTINCTION BETWEEN PSEUDONYMIZATION AND ANONYMIZATION:

PSEUDONYMIZATION = REVERSIBLE TRANSFORMATION
- The original data can be recovered using a key or mapping table
- Maintains referential integrity across systems
- Allows for re-identification if needed for legitimate purposes
- Examples: Hashing with salt, tokenization, encryption with key
- Use case: Replace "John Smith" with "USER_12345" but keep mapping table

ANONYMIZATION = IRREVERSIBLE TRANSFORMATION
- The original data cannot be recovered by any means
- Permanently removes all identifying characteristics
- Makes re-identification impossible even with additional data
- Examples: Aggregation, generalization, data suppression
- Use case: Replace individual ages with age ranges (25-30, 31-35)

4. SPECIAL CATEGORY (Column 4 - Sensitive data category classification):
   - PII: General Personally Identifiable Information (names, addresses, contact details)
   - PHI: Protected Health Information (medical records, health conditions, treatment data)
   - PCI: Payment Card Industry data (credit card numbers, financial account information)
   - Biometric: Biometric identifiers (fingerprints, facial recognition data, voice prints)
   - Genetic: Genetic or hereditary information (DNA data, genetic test results)
   - none: No special category applies (general personal data without special sensitivity)

DETAILED CLASSIFICATION RULES:

DIRECT PERSONAL DATA:
- Full name, first name, last name
- National ID, passport number, social security number
- Email address (personal)
- Phone number (personal)
- Home address, personal address
- Username (if it contains personal identifiers)
- Employee ID (if it directly identifies individuals)
- Customer ID (if it directly identifies individuals)
- Personal photos, profile pictures

INDIRECT PERSONAL DATA:
- IP addresses
- Device identifiers
- Session tokens
- Location coordinates
- Browser fingerprints
- User preferences
- Behavioral data
- Transaction IDs (when combined with other data)
- Log entries with user activities
- Timestamps of user actions

PSEUDONYMOUS PERSONAL DATA:
- Data that has been processed to replace direct identifiers with pseudonyms
- Hashed or encrypted identifiers that can be reversed with a key
- Tokenized data that maintains referential integrity
- Data that requires additional information to identify individuals

ANONYMOUS PERSONAL DATA:
- Data that has been irreversibly processed to remove identifying characteristics
- Aggregated data that cannot be linked back to individuals
- Statistical summaries that preserve privacy
- Data where re-identification is practically impossible

DETAILED PSEUDONYMIZATION ASSESSMENT:

PSEUDONYMS = YES (Can be pseudonymized):
✓ DIRECT IDENTIFIERS that can be replaced with tokens:
  - Names (first, last, full) → USER_TOKEN_12345
  - Email addresses → HASHED_EMAIL_ABC123
  - Phone numbers → PHONE_TOKEN_XYZ789
  - National IDs → ID_TOKEN_DEF456
  - Employee IDs → EMP_TOKEN_GHI789
  - Customer IDs → CUST_TOKEN_JKL012

✓ STRUCTURED DATA that allows systematic replacement:
  - Addresses → LOCATION_TOKEN_MNO345
  - Account numbers → ACCOUNT_TOKEN_PQR678
  - License plates → VEHICLE_TOKEN_STU901

✓ MAINTAINS BUSINESS UTILITY while hiding identity:
  - Can still perform analytics and reporting
  - Preserves relationships between records
  - Allows for legitimate re-identification when needed

PSEUDONYMS = NO (Cannot be pseudonymized):
✗ ALREADY TRANSFORMED DATA:
  - Data that's already hashed, encrypted, or tokenized
  - System-generated UUIDs or random identifiers
  - Masked or obfuscated data

✗ BEHAVIORAL/PREFERENCE DATA:
  - User preferences and settings
  - Behavioral patterns and habits
  - Usage statistics and metrics
  - Log entries and timestamps

✗ LOSES ESSENTIAL MEANING when replaced:
  - Data where the actual value is critical for business function
  - Reference data that must remain readable

DETAILED ANONYMIZATION ASSESSMENT:

ANONYMS = YES (Can be anonymized):
✓ CAN BE AGGREGATED into statistical summaries:
  - Individual ages → Age ranges (25-30, 31-35, 36-40)
  - Specific locations → Geographic regions (North, South, East, West)
  - Individual salaries → Salary bands ($50K-60K, $60K-70K)
  - Exact dates → Time periods (Q1 2023, Q2 2023)

✓ CAN BE GENERALIZED without losing analytical value:
  - Specific job titles → Job categories (Manager, Developer, Analyst)
  - Detailed addresses → City or postal code areas
  - Precise timestamps → Date ranges or time buckets

✓ INDIVIDUAL RECORDS can be grouped:
  - Personal data that can be combined into demographic statistics
  - Transaction data that can become aggregate spending patterns
  - Usage data that can become general usage statistics

ANONYMS = NO (Cannot be anonymized):
✗ MUST REMAIN AT INDIVIDUAL LEVEL:
  - Unique identifiers required for system operation
  - Transaction records needed for audit trails
  - Legal compliance data that must be individually trackable
  - Financial records requiring individual accountability

✗ CRITICAL FOR BUSINESS OPERATIONS:
  - Data needed for individual customer service
  - Records required for individual billing or payments
  - Information needed for individual legal compliance

✗ REGULATORY REQUIREMENTS:
  - Data that must be maintained at individual level by law
  - Audit trails that require individual record integrity
  - Compliance data that cannot be aggregated

DECISION MATRIX FOR CLASSIFICATION:

PSEUDONYMS = YES + ANONYMS = YES:
- Most general personal data (names, emails, addresses)
- Non-critical identifiers
- Data used primarily for analytics

PSEUDONYMS = YES + ANONYMS = NO:
- Financial transaction data
- Legal compliance records
- Audit-required individual records

PSEUDONYMS = NO + ANONYMS = YES:
- Already processed behavioral data
- System-generated metrics
- Usage statistics

PSEUDONYMS = NO + ANONYMS = NO:
- Biometric data (fingerprints, facial recognition)
- Genetic information (DNA, hereditary data)
- Critical system identifiers

SPECIAL CATEGORY CLASSIFICATIONS:

PII (General Personal Information):
- Names, addresses, phone numbers
- National IDs, passport numbers
- Personal email addresses
- Date of birth, age
- Personal identifiers
- Contact details
- Employment information

PHI (Protected Health Information):
- Medical records, health conditions
- Prescription data, medication history
- Health insurance information
- Medical test results
- Mental health data
- Disability information
- Treatment data

PCI (Payment Card Industry Data):
- Credit card numbers, debit card numbers
- Bank account numbers
- Payment transaction data
- Billing information
- Financial account details
- IBAN numbers

Biometric (Biometric Identifiers):
- Fingerprints, palm prints
- Facial recognition data
- Voice prints, iris scans
- Retinal scans
- Behavioral biometrics

Genetic (Genetic Information):
- DNA sequences, genetic markers
- Family medical history
- Hereditary condition data
- Genetic test results

none (No Special Category):
- General personal data that doesn't fall into sensitive categories
- Basic contact information without special sensitivity
- Standard business data with personal elements
- Non-sensitive personal identifiers

CLASSIFICATION PRINCIPLES:
1. Analyze each field for its ability to be pseudonymized and anonymized separately
2. Consider the business context and data usage requirements
3. For personal data type, choose based on current state of the data:
   - "direct": Raw identifiable data (names, emails, IDs)
   - "indirect": Data that needs combination to identify (IP, device ID, location)
   - "pseudonymous": Already processed data with reversible transformation
   - "anonymous": Already processed data with irreversible transformation
4. For special categories, use "none" for general personal data without special sensitivity
5. When in doubt about special categories, classify as "none" rather than PII
6. Focus on the actual data content and its potential for transformation
7. Remember that pseudonymization and anonymization are different capabilities

DATA TO CLASSIFY:
${recordsToProcess.map((record, index) =>
  `${index + 1}. Field: ${record.tableName}.${record.columnName}
     Data Type: ${record.dataType}
     Context: ${record.personalDataReasoning || 'Personal data field'}
     Record ID: ${record.id}

     FIELD-SPECIFIC ANALYSIS REQUIRED:
     1. Personal Data Type: Is "${record.columnName}" direct, indirect, pseudonymous, or anonymous based on its name and purpose?
     2. Pseudonyms: Can "${record.columnName}" be tokenized while maintaining its business function?
     3. Anonyms: Can "${record.columnName}" be aggregated/generalized without losing analytical value?
     4. Special Category: What sensitive data category does "${record.columnName}" represent (PII, PHI, PCI, Biometric, Genetic, or none)?
     5. Pseudonyms Justification: Why can/cannot "${record.columnName}" be pseudonymized? (exactly 10 words)
     6. Anonyms Justification: Why can/cannot "${record.columnName}" be anonymized? (exactly 10 words)`
).join('\n\n')}

RESPONSE FORMAT:
Provide ONLY the classification results without reasoning or explanation. Return a JSON object with a "records" array where each record has:
- recordId: The exact ID provided
- personalDataType: "direct", "indirect", "pseudonymous", or "anonymous"
- pseudonyms: "Yes" or "No"
- anonyms: "Yes" or "No"
- specialCategoryType: "PII", "PHI", "PCI", "Biometric", "Genetic", or "none"
- pseudonymsJustification: Exactly 10 words explaining why pseudonymization is/isn't possible
- anonymsJustification: Exactly 10 words explaining why anonymization is/isn't possible

Classification must be immediate and definitive - no explanations needed.

FIELD-SPECIFIC JUSTIFICATION EXAMPLES:

For "customer_email":
- pseudonymsJustification: "Email can be hashed while preserving communication functionality"
- anonymsJustification: "Can group by domain patterns for statistical analysis"

For "credit_card_number":
- pseudonymsJustification: "Card numbers can be tokenized for secure payment processing"
- anonymsJustification: "Individual records required for transaction audit and compliance"

For "fingerprint_data":
- pseudonymsJustification: "Biometric data loses uniqueness when replaced with tokens"
- anonymsJustification: "Individual precision required for biometric identification cannot aggregate"

For "user_age":
- pseudonymsJustification: "Age values can be coded while maintaining demographic analysis"
- anonymsJustification: "Can be grouped into age ranges for statistical studies"

For "ip_address":
- pseudonymsJustification: "IP addresses can be hashed while preserving network analysis"
- anonymsJustification: "Can be grouped by geographic regions for usage patterns"

For "medical_condition":
- pseudonymsJustification: "Conditions can be coded while maintaining medical research value"
- anonymsJustification: "Can be aggregated for epidemiological studies without individual identification"

JUSTIFICATION REQUIREMENTS:
- Each justification must be EXACTLY 10 words
- Analyze the SPECIFIC field name and data type
- Consider the field's business purpose and technical constraints
- Explain WHY pseudonymization/anonymization is possible or impossible for THIS specific field
- Use the actual field name context (e.g., "email", "age", "address")
- Focus on the unique characteristics of each data element

CRITICAL FIELD-SPECIFIC ANALYSIS RULES:

FOR PSEUDONYMS JUSTIFICATION (exactly 10 words):
- Analyze the SPECIFIC field name and its business purpose
- If YES: Explain HOW it can be tokenized (e.g., "Email addresses can be hashed while preserving communication functionality")
- If NO: Explain WHY it cannot be tokenized (e.g., "Biometric fingerprints lose uniqueness when replaced with tokens")
- Reference the actual field name in your justification
- Focus on the technical or business constraint specific to this field

FOR ANONYMS JUSTIFICATION (exactly 10 words):
- Consider the SPECIFIC data element and its analytical value
- If YES: Explain HOW it can be aggregated (e.g., "Ages can be grouped into ranges for demographic analysis")
- If NO: Explain WHY individual records are required (e.g., "Transaction records required individually for audit and compliance")
- Reference the field's specific business or regulatory requirements
- Focus on what makes THIS field aggregatable or non-aggregatable

FIELD NAME ANALYSIS PATTERNS:
- "email" → Can be hashed, can be grouped by domain
- "age" → Can be coded, can be grouped into ranges
- "credit_card" → Can be tokenized, cannot be aggregated (audit)
- "fingerprint" → Cannot be tokenized (loses uniqueness), cannot be aggregated
- "address" → Can be tokenized, can be grouped by region
- "phone" → Can be hashed, can be grouped by area code
- "medical_condition" → Can be coded, can be aggregated for research

GENERATE UNIQUE JUSTIFICATIONS FOR EACH FIELD BASED ON ITS SPECIFIC CHARACTERISTICS!`;

    let result;
    try {
      // Use fast model for straightforward classification task with increased token limit
      console.log(`Sending prompt to AI with context length: ${detailedSystemContext.length} characters`);
      console.log(`Processing ${recordsToProcess.length} personal data records`);
      console.log(`Estimated prompt length: ${prompt.length} characters`);
      console.log(`Detailed system context being used: ${detailedSystemContext.substring(0, 500)}...`);

      // Create a fresh model instance to reset context between calls
      const freshModel = google('gemini-2.5-flash');

      result = await generateObject({
        model: freshModel,
        prompt: prompt,
        schema: PersonalDataClassificationSchema,
        maxTokens: 80000, // Increased from 4000 to handle more records
        // Add experimental settings for better processing
        experimental_telemetry: {
          isEnabled: true,
          recordInputs: false,
          recordOutputs: false,
        },
      });

      if (!result.object || !result.object.records) {
        throw new Error('AI model did not return a valid response object');
      }

    } catch (aiError) {
      console.error('AI personal data classification failed:', aiError);

      return NextResponse.json(
        {
          error: 'AI personal data classification service unavailable',
          details: 'The AI classification service is currently unavailable. Please try again later.',
          systemId,
          pageNumber,
          recordsProcessed: 0
        },
        { status: 503 }
      );
    }

    console.log(`Personal data classification completed for ${recordsToProcess.length} records (page ${pageNumber})`);
    console.log('=== CLASSIFICATION RESULTS ===');
    console.log('Raw AI Response:', JSON.stringify(result.object.records, null, 2));

    // Validate that we got results for all records
    if (result.object.records.length !== recordsToProcess.length) {
      console.warn(`Expected ${recordsToProcess.length} classifications but got ${result.object.records.length}`);
    }

    // Log each classification result
    result.object.records.forEach((record, index) => {
      console.log(`Record ${index + 1}:`);
      console.log(`  - ID: ${record.recordId}`);
      console.log(`  - Personal Data Type: ${record.personalDataType}`);
      console.log(`  - Pseudonyms: ${record.pseudonyms} (${record.pseudonymsJustification})`);
      console.log(`  - Anonyms: ${record.anonyms} (${record.anonymsJustification})`);
      console.log(`  - Special Category: ${record.specialCategoryType}`);
    });
    console.log('=== END CLASSIFICATION RESULTS ===');

    // Return the classifications with pagination info
    return NextResponse.json({
      success: true,
      feature: 'personal_data_classification',
      systemId,
      pageNumber,
      recordsProcessed: recordsToProcess.length,
      classifications: result.object.records
    });

  } catch (error) {
    console.error('Personal data classification error:', error);
    
    // Handle different types of errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error during personal data classification' },
      { status: 500 }
    );
  }
} 