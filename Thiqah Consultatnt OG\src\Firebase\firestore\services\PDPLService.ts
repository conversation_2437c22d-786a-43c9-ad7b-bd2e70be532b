import {
  collection,
  doc,
  addDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  Timestamp,
  where,
  getDoc
} from 'firebase/firestore';
import { firestore as db } from '../firestoreConfig';

export enum PDPLDocumentType {
  PDPL_LAW = 'pdpl_law',
  IMPLEMENTING_REGULATION = 'implementing_regulation',
  TRANSFER_RULES = 'transfer_rules'
}

export interface PDPLDefinition {
  id?: string;
  term: string;
  definition: string;
  documentType: PDPLDocumentType;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface PDPLSubPoint {
  id: string;
  content: string;
  order: number;
  subSubPoints?: PDPLSubPoint[];
}

export interface PDPLPoint {
  id: string;
  content: string;
  order: number;
  subPoints?: PDPLSubPoint[];
}

export interface PDPLArticle {
  id?: string;
  articleNumber: string;
  title: string;
  description: string;
  documentType: PDPLDocumentType;
  points?: PDPLPoint[];
  subPoints?: PDPLSubPoint[];
  linkedArticles?: string[]; // IDs of linked articles
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface PDPLDocument {
  id?: string;
  type: PDPLDocumentType;
  title: string;
  description: string;
  version: string;
  effectiveDate?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

const pdplArticlesCollection = collection(db, 'pdplArticles');
const pdplDefinitionsCollection = collection(db, 'pdplDefinitions');
const pdplDocumentsCollection = collection(db, 'pdplDocuments');

export class PDPLService {
  // ==================== DOCUMENT MANAGEMENT ====================
  
  /**
   * Creates a new PDPL document
   */
  static async createDocument(documentData: Omit<PDPLDocument, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = Timestamp.now();
    const newDocument = {
      ...documentData,
      createdAt: now,
      updatedAt: now
    };

    const docRef = await addDoc(pdplDocumentsCollection, newDocument);
    return docRef.id;
  }

  /**
   * Retrieves all PDPL documents
   */
  static async getDocuments(): Promise<PDPLDocument[]> {
    const q = query(pdplDocumentsCollection, orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);

    const documents: PDPLDocument[] = [];
    querySnapshot.forEach((doc) => {
      documents.push({
        id: doc.id,
        ...doc.data()
      } as PDPLDocument);
    });

    return documents;
  }

  /**
   * Updates a PDPL document
   */
  static async updateDocument(documentId: string, updates: Partial<PDPLDocument>): Promise<void> {
    const documentRef = doc(pdplDocumentsCollection, documentId);
    const updateData = {
      ...updates,
      updatedAt: Timestamp.now()
    };

    await updateDoc(documentRef, updateData);
  }

  /**
   * Deletes a PDPL document
   */
  static async deleteDocument(documentId: string): Promise<void> {
    const documentRef = doc(pdplDocumentsCollection, documentId);
    await deleteDoc(documentRef);
  }

  // ==================== ARTICLE MANAGEMENT ====================

  /**
   * Creates a new PDPL article
   */
  static async createArticle(articleData: Omit<PDPLArticle, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = Timestamp.now();
    const newArticle = {
      ...articleData,
      createdAt: now,
      updatedAt: now,
      points: articleData.points || [],
      subPoints: articleData.subPoints || [],
      linkedArticles: articleData.linkedArticles || []
    };

    const docRef = await addDoc(pdplArticlesCollection, newArticle);
    return docRef.id;
  }

  /**
   * Retrieves all articles by document type
   */
  static async getArticlesByType(documentType: PDPLDocumentType): Promise<PDPLArticle[]> {
    const q = query(
      pdplArticlesCollection,
      where('documentType', '==', documentType),
      orderBy('articleNumber', 'asc')
    );
    const querySnapshot = await getDocs(q);

    const articles: PDPLArticle[] = [];
    querySnapshot.forEach((doc) => {
      articles.push({
        id: doc.id,
        ...doc.data()
      } as PDPLArticle);
    });

    return articles;
  }

  /**
   * Retrieves all articles
   */
  static async getAllArticles(): Promise<PDPLArticle[]> {
    const q = query(pdplArticlesCollection, orderBy('documentType', 'asc'), orderBy('articleNumber', 'asc'));
    const querySnapshot = await getDocs(q);

    const articles: PDPLArticle[] = [];
    querySnapshot.forEach((doc) => {
      articles.push({
        id: doc.id,
        ...doc.data()
      } as PDPLArticle);
    });

    return articles;
  }

  /**
   * Gets a single article by ID
   */
  static async getArticle(articleId: string): Promise<PDPLArticle | null> {
    const articleRef = doc(pdplArticlesCollection, articleId);
    const docSnap = await getDoc(articleRef);

    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data()
      } as PDPLArticle;
    }

    return null;
  }

  /**
   * Updates an article
   */
  static async updateArticle(articleId: string, updates: Partial<PDPLArticle>): Promise<void> {
    const articleRef = doc(pdplArticlesCollection, articleId);
    const updateData = {
      ...updates,
      updatedAt: Timestamp.now()
    };

    await updateDoc(articleRef, updateData);
  }

  /**
   * Deletes an article
   */
  static async deleteArticle(articleId: string): Promise<void> {
    const articleRef = doc(pdplArticlesCollection, articleId);
    await deleteDoc(articleRef);
  }

  /**
   * Links articles together
   */
  static async linkArticles(articleId: string, linkedArticleIds: string[]): Promise<void> {
    const articleRef = doc(pdplArticlesCollection, articleId);
    await updateDoc(articleRef, {
      linkedArticles: linkedArticleIds,
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Adds a point to an article
   */
  static async addPointToArticle(articleId: string, point: Omit<PDPLPoint, 'id'>): Promise<void> {
    const articleRef = doc(pdplArticlesCollection, articleId);
    const articleDoc = await getDoc(articleRef);
    
    if (!articleDoc.exists()) {
      throw new Error('Article not found');
    }

    const articleData = articleDoc.data() as PDPLArticle;
    const currentPoints = articleData.points || [];
    
    const newPoint: PDPLPoint = {
      ...point,
      id: Date.now().toString(),
      subPoints: point.subPoints || []
    };
    
    await updateDoc(articleRef, {
      points: [...currentPoints, newPoint],
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Updates a point in an article
   */
  static async updatePointInArticle(articleId: string, pointId: string, updates: Partial<PDPLPoint>): Promise<void> {
    const articleRef = doc(pdplArticlesCollection, articleId);
    const articleDoc = await getDoc(articleRef);
    
    if (!articleDoc.exists()) {
      throw new Error('Article not found');
    }

    const articleData = articleDoc.data() as PDPLArticle;
    const currentPoints = articleData.points || [];
    
    const updatedPoints = currentPoints.map(point => 
      point.id === pointId ? { ...point, ...updates } : point
    );
    
    await updateDoc(articleRef, {
      points: updatedPoints,
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Removes a point from an article
   */
  static async removePointFromArticle(articleId: string, pointId: string): Promise<void> {
    const articleRef = doc(pdplArticlesCollection, articleId);
    const articleDoc = await getDoc(articleRef);
    
    if (!articleDoc.exists()) {
      throw new Error('Article not found');
    }

    const articleData = articleDoc.data() as PDPLArticle;
    const currentPoints = articleData.points || [];
    
    const updatedPoints = currentPoints.filter(point => point.id !== pointId);
    
    await updateDoc(articleRef, {
      points: updatedPoints,
      updatedAt: Timestamp.now()
    });
  }

  // ==================== DEFINITION MANAGEMENT ====================

  /**
   * Creates a new definition
   */
  static async createDefinition(definitionData: Omit<PDPLDefinition, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = Timestamp.now();
    const newDefinition = {
      ...definitionData,
      createdAt: now,
      updatedAt: now
    };

    const docRef = await addDoc(pdplDefinitionsCollection, newDefinition);
    return docRef.id;
  }

  /**
   * Retrieves all definitions by document type
   */
  static async getDefinitionsByType(documentType: PDPLDocumentType): Promise<PDPLDefinition[]> {
    const q = query(
      pdplDefinitionsCollection,
      where('documentType', '==', documentType),
      orderBy('term', 'asc')
    );
    const querySnapshot = await getDocs(q);

    const definitions: PDPLDefinition[] = [];
    querySnapshot.forEach((doc) => {
      definitions.push({
        id: doc.id,
        ...doc.data()
      } as PDPLDefinition);
    });

    return definitions;
  }

  /**
   * Retrieves all definitions
   */
  static async getAllDefinitions(): Promise<PDPLDefinition[]> {
    const q = query(pdplDefinitionsCollection, orderBy('term', 'asc'));
    const querySnapshot = await getDocs(q);

    const definitions: PDPLDefinition[] = [];
    querySnapshot.forEach((doc) => {
      definitions.push({
        id: doc.id,
        ...doc.data()
      } as PDPLDefinition);
    });

    return definitions;
  }

  /**
   * Updates a definition
   */
  static async updateDefinition(definitionId: string, updates: Partial<PDPLDefinition>): Promise<void> {
    const definitionRef = doc(pdplDefinitionsCollection, definitionId);
    const updateData = {
      ...updates,
      updatedAt: Timestamp.now()
    };

    await updateDoc(definitionRef, updateData);
  }

  /**
   * Deletes a definition
   */
  static async deleteDefinition(definitionId: string): Promise<void> {
    const definitionRef = doc(pdplDefinitionsCollection, definitionId);
    await deleteDoc(definitionRef);
  }

  // ==================== STATISTICS AND UTILITIES ====================

  /**
   * Gets PDPL statistics
   */
  static async getPDPLStatistics(): Promise<{
    totalArticles: number;
    pdplLawArticles: number;
    implementingRegulationArticles: number;
    transferRulesArticles: number;
    totalDefinitions: number;
  }> {
    const allArticles = await this.getAllArticles();
    const allDefinitions = await this.getAllDefinitions();
    
    return {
      totalArticles: allArticles.length,
      pdplLawArticles: allArticles.filter(a => a.documentType === PDPLDocumentType.PDPL_LAW).length,
      implementingRegulationArticles: allArticles.filter(a => a.documentType === PDPLDocumentType.IMPLEMENTING_REGULATION).length,
      transferRulesArticles: allArticles.filter(a => a.documentType === PDPLDocumentType.TRANSFER_RULES).length,
      totalDefinitions: allDefinitions.length,
    };
  }

  /**
   * Searches articles by content
   */
  static async searchArticles(searchTerm: string, documentType?: PDPLDocumentType): Promise<PDPLArticle[]> {
    let articles: PDPLArticle[];
    
    if (documentType) {
      articles = await this.getArticlesByType(documentType);
    } else {
      articles = await this.getAllArticles();
    }

    const searchTermLower = searchTerm.toLowerCase();
    return articles.filter(article => 
      article.title.toLowerCase().includes(searchTermLower) ||
      article.description.toLowerCase().includes(searchTermLower) ||
      article.articleNumber.toLowerCase().includes(searchTermLower)
    );
  }
} 