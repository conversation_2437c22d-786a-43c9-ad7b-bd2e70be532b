"use client";

import React, { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { ReferencesService, ReferenceItem } from "@/Firebase/firestore/services/ReferencesService";
import { Timestamp } from "firebase/firestore";
import { EditReferenceModal } from "@/components/ui/EditReferenceModal";
import {
  Edit,
  Trash2,
  ExternalLink,
  Hash,
  FileText,
  Calendar,
  AlertTriangle,
  BookOpen
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface ReferencesListProps {
  references: ReferenceItem[];
  onReferenceUpdated: (reference: ReferenceItem) => void;
  onReferenceDeleted: (referenceId: string) => void;
  isRTL?: boolean;
}

export function ReferencesList({
  references,
  onReferenceUpdated,
  onReferenceDeleted,
  isRTL = false
}: ReferencesListProps) {
  const { toast } = useToast();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [referenceToDelete, setReferenceToDelete] = useState<ReferenceItem | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [referenceToEdit, setReferenceToEdit] = useState<ReferenceItem | null>(null);

  const handleDeleteClick = (reference: ReferenceItem) => {
    setReferenceToDelete(reference);
    setDeleteDialogOpen(true);
  };

  const handleEditClick = (reference: ReferenceItem) => {
    setReferenceToEdit(reference);
    setEditModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!referenceToDelete?.id) return;

    setIsDeleting(true);
    try {
      await ReferencesService.deleteReference(referenceToDelete.id);
      onReferenceDeleted(referenceToDelete.id);
      
      toast({
        title: isRTL ? "تم حذف المرجع بنجاح" : "Reference deleted successfully",
        variant: "default",
      });
    } catch (error) {
      console.error("Error deleting reference:", error);
      toast({
        title: isRTL ? "خطأ في حذف المرجع" : "Error deleting reference",
        description: isRTL ? "حدث خطأ أثناء حذف المرجع" : "An error occurred while deleting the reference",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setReferenceToDelete(null);
    }
  };

  const formatDate = (timestamp: Timestamp | Date | string | null | undefined) => {
    if (!timestamp) return "";

    let date: Date;
    if (timestamp instanceof Timestamp) {
      date = timestamp.toDate();
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else if (typeof timestamp === 'string') {
      date = new Date(timestamp);
    } else {
      return "";
    }
    
    return date.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const openLink = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  if (references.length === 0) {
    return (
      <div className={`text-center py-12 ${isRTL ? 'rtl' : 'ltr'}`}>
        <BookOpen size={48} className="mx-auto text-[var(--brand-blue)]/60 mb-4" />
        <h3 className="text-lg font-medium text-[var(--brand-dark-gray)] mb-2">
          {isRTL ? "لا توجد مراجع" : "No References"}
        </h3>
        <p className="text-[var(--brand-dark-gray)]/70">
          {isRTL ? "لم يتم إضافة أي مراجع بعد. ابدأ بإضافة مرجع جديد." : "No references have been added yet. Start by adding a new reference."}
        </p>
      </div>
    );
  }

  return (
    <>
      <div className={`space-y-4 ${isRTL ? 'rtl' : 'ltr'}`}>
        {references.map((reference) => (
          <Card key={reference.id} className="hover:shadow-lg transition-all duration-300 border-[var(--brand-blue)]/10 hover:border-[var(--brand-blue)]/30">
            <CardHeader className="pb-3 bg-white">
              <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
                <CardTitle className={`text-lg font-semibold text-[var(--brand-dark-gray)] flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                  {reference.title}
                </CardTitle>
                <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditClick(reference)}
                    className="h-8 w-8 p-0 border-[var(--brand-blue)]/30 text-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/10"
                  >
                    <Edit size={14} />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteClick(reference)}
                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                  >
                    <Trash2 size={14} />
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-3 bg-white">
              {/* Link */}
              {reference.link && (
                <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
                  <ExternalLink size={16} className="text-[var(--brand-blue)] flex-shrink-0" />
                  <button
                    onClick={() => openLink(reference.link!)}
                    className={`text-[var(--brand-blue)] hover:text-[var(--brand-blue)]/80 hover:underline truncate ${isRTL ? 'text-right' : 'text-left'} font-medium`}
                  >
                    {reference.link}
                  </button>
                </div>
              )}

              {/* Page Number */}
              {reference.pageNumber && (
                <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
                  <Hash size={16} className="text-[var(--brand-dark-gray)]/60 flex-shrink-0" />
                  <span className="text-[var(--brand-dark-gray)] font-medium">
                    {isRTL ? `صفحة ${reference.pageNumber}` : `Page ${reference.pageNumber}`}
                  </span>
                </div>
              )}

              {/* Details */}
              {reference.details && (
                <div className={`flex items-start gap-2 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
                  <FileText size={16} className="text-[var(--brand-dark-gray)]/60 flex-shrink-0 mt-0.5" />
                  <p className={`text-[var(--brand-dark-gray)]/80 text-sm leading-relaxed ${isRTL ? 'text-right' : 'text-left'}`}>
                    {reference.details}
                  </p>
                </div>
              )}

              {/* Metadata */}
              <div className={`flex items-center justify-between pt-2 border-t border-[var(--brand-blue)]/10 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
                <div className={`flex items-center gap-2 text-sm text-[var(--brand-dark-gray)]/60 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
                  <Calendar size={14} />
                  <span>
                    {isRTL ? "تم الإنشاء في" : "Created on"} {formatDate(reference.createdAt)}
                  </span>
                </div>

                {/* Status badges */}
                <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
                  {reference.link && (
                    <Badge className="text-xs bg-[var(--brand-blue)]/10 text-[var(--brand-blue)] border-[var(--brand-blue)]/20">
                      {isRTL ? "رابط" : "Link"}
                    </Badge>
                  )}
                  {reference.pageNumber && (
                    <Badge variant="outline" className="text-xs border-[var(--brand-blue)]/30 text-[var(--brand-blue)]">
                      {isRTL ? "صفحة" : "Page"}
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent className={isRTL ? 'rtl' : 'ltr'}>
          <AlertDialogHeader>
            <AlertDialogTitle className={`flex items-center gap-2 ${isRTL ? 'text-right flex-row-reverse' : 'text-left'}`}>
              <AlertTriangle size={20} className="text-red-600" />
              {isRTL ? "تأكيد الحذف" : "Confirm Deletion"}
            </AlertDialogTitle>
            <AlertDialogDescription className={isRTL ? 'text-right' : 'text-left'}>
              {isRTL 
                ? `هل أنت متأكد من أنك تريد حذف المرجع "${referenceToDelete?.title}"؟ لا يمكن التراجع عن هذا الإجراء.`
                : `Are you sure you want to delete the reference "${referenceToDelete?.title}"? This action cannot be undone.`
              }
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className={isRTL ? 'flex-row-reverse' : ''}>
            <AlertDialogCancel disabled={isDeleting}>
              {isRTL ? "إلغاء" : "Cancel"}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? (
                isRTL ? "جاري الحذف..." : "Deleting..."
              ) : (
                isRTL ? "حذف" : "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit Reference Modal */}
      <EditReferenceModal
        isOpen={editModalOpen}
        onClose={() => {
          setEditModalOpen(false);
          setReferenceToEdit(null);
        }}
        reference={referenceToEdit}
        onReferenceUpdated={onReferenceUpdated}
        isRTL={isRTL}
      />
    </>
  );
}
