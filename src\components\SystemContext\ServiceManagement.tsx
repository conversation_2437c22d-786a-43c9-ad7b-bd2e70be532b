"use client";

import React, { useState } from "react";
import { Plus, Edit, Trash2, Settings, Users, Clock, DollarSign, CheckCircle, List, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { SystemsService, SystemService } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";
import { AddServiceModal } from "./AddServiceModal";
import { ServiceStepsVisualization } from "./ServiceStepsVisualization";
import { ServiceRequirementsVisualization } from "./ServiceRequirementsVisualization";

interface ServiceManagementProps {
  systemId: string;
  lang: string;
  services: SystemService[];
  onServicesUpdate: () => void;
}

export function ServiceManagement({ systemId, lang, services, onServicesUpdate }: ServiceManagementProps) {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingService, setEditingService] = useState<SystemService | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [expandedService, setExpandedService] = useState<string | null>(null);
  const [deleteConfirmService, setDeleteConfirmService] = useState<SystemService | null>(null);

  const { toast } = useToast();
  const isRTL = lang === "ar";

  const handleDeleteClick = (service: SystemService) => {
    setDeleteConfirmService(service);
  };

  const handleConfirmDelete = async () => {
    if (!deleteConfirmService?.id) return;
    
    try {
      setIsDeleting(deleteConfirmService.id);
      await SystemsService.deleteSystemService(systemId, deleteConfirmService.id);
      
      toast({
        title: isRTL ? "تم الحذف" : "Deleted",
        description: isRTL ? "تم حذف الخدمة بنجاح" : "Service deleted successfully",
      });
      
      onServicesUpdate();
      setDeleteConfirmService(null);
    } catch (error) {
      console.error('Error deleting service:', error);
      toast({
        title: isRTL ? "خطأ في الحذف" : "Delete Error",
        description: isRTL ? "فشل في حذف الخدمة" : "Failed to delete service",
        variant: "destructive"
      });
    } finally {
      setIsDeleting(null);
    }
  };

  const handleEditService = (service: SystemService) => {
    setEditingService(service);
    setIsAddModalOpen(true);
  };

  const handleModalClose = () => {
    setIsAddModalOpen(false);
    setEditingService(null);
  };



  const getAvailabilityColor = (availability: string) => {
    const colors = {
      '24/7': 'bg-green-100 text-green-800',
      'Business Hours': 'bg-blue-100 text-blue-800',
      'Scheduled': 'bg-yellow-100 text-yellow-800',
      'On-Demand': 'bg-purple-100 text-purple-800'
    };
    return colors[availability as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getTimeColor = (time: string) => {
    const colors = {
      'Instant': 'bg-green-100 text-green-800',
      'Minutes': 'bg-blue-100 text-blue-800',
      'Hours': 'bg-yellow-100 text-yellow-800',
      'Days': 'bg-orange-100 text-orange-800',
      'Weeks': 'bg-red-100 text-red-800'
    };
    return colors[time as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">
            {isRTL ? "إدارة الخدمات" : "Service Management"}
          </h3>
          <p className="text-gray-600">
            {isRTL 
              ? "إدارة الخدمات التي يقدمها النظام" 
              : "Manage services provided by the system"
            }
          </p>
        </div>
        <Button
          onClick={() => setIsAddModalOpen(true)}
          className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          {isRTL ? "إضافة خدمة" : "Add Service"}
        </Button>
      </div>

      {/* Services Grid */}
      {services.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {services.map((service) => (
            <div key={service.id} className="group relative">
              <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-3xl border border-gray-200/60 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-1">
                {/* Header Section */}
                <div className="p-6 bg-gradient-to-r from-[var(--brand-blue)]/5 to-[var(--brand-blue)]/10 border-b border-gray-100">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-[var(--brand-blue)] to-[var(--brand-blue)]/80 flex items-center justify-center shadow-lg">
                        <Settings className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-1">
                          {service.serviceName}
                        </h3>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                          <span className="text-sm text-gray-600 font-medium">
                            {isRTL ? "خدمة نشطة" : "Active Service"}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditService(service)}
                        className="h-9 w-9 p-0 hover:bg-white/80 hover:shadow-md transition-all duration-200"
                      >
                        <Edit className="w-4 h-4 text-gray-600" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteClick(service)}
                        disabled={isDeleting === service.id}
                        className="h-9 w-9 p-0 hover:bg-red-50 hover:text-red-600 transition-all duration-200"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {service.serviceDescription}
                  </p>
                </div>

                {/* Content Section */}
                <div className="p-6">
                  {/* Service Metrics */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-2xl p-4 border border-blue-200/50">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-xl bg-blue-500 flex items-center justify-center">
                          <Users className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <div className="text-xs font-semibold text-blue-700 uppercase tracking-wider">
                            {isRTL ? "المستفيدون" : "Beneficiaries"}
                          </div>
                          <div className="text-sm font-bold text-blue-900">
                            {service.serviceBeneficiaries}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-br from-green-50 to-green-100/50 rounded-2xl p-4 border border-green-200/50">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-xl bg-green-500 flex items-center justify-center">
                          <DollarSign className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <div className="text-xs font-semibold text-green-700 uppercase tracking-wider">
                            {isRTL ? "الرسوم" : "Fees"}
                          </div>
                          <div className="text-sm font-bold text-green-900">
                            {service.serviceFees} {isRTL ? "ر.س" : "SAR"}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Service Details */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-gray-500" />
                        <span className="text-sm font-medium text-gray-700">
                          {isRTL ? "التوفر" : "Availability"}
                        </span>
                      </div>
                      <Badge className={getAvailabilityColor(service.serviceAvailability)}>
                        {service.serviceAvailability}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-gray-500" />
                        <span className="text-sm font-medium text-gray-700">
                          {isRTL ? "وقت المعالجة" : "Processing Time"}
                        </span>
                      </div>
                      <Badge className={getTimeColor(service.serviceTime)}>
                        {service.serviceTime}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Expandable Content */}
                {(service.serviceSteps?.length || service.serviceRequirements?.length) && (
                  <div className="px-6 pb-6">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setExpandedService(expandedService === service.id ? null : service.id!)}
                      className="w-full mb-4 bg-white hover:bg-gray-50 border-gray-200"
                    >
                      {expandedService === service.id ? (
                        <>
                          {isRTL ? "إخفاء التفاصيل" : "Hide Details"}
                        </>
                      ) : (
                        <>
                          <List className="w-4 h-4 mr-2" />
                          {isRTL ? "عرض التفاصيل" : "Show Details"}
                        </>
                      )}
                    </Button>

                    {expandedService === service.id && (
                      <div className="space-y-6 bg-white rounded-2xl p-4 border border-gray-100">
                        {/* Service Steps */}
                        {service.serviceSteps && service.serviceSteps.length > 0 && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                              <CheckCircle className="w-4 h-4 text-[var(--brand-blue)]" />
                              {isRTL ? "خطوات الخدمة" : "Service Steps"}
                            </h4>
                            <ServiceStepsVisualization
                              steps={service.serviceSteps}
                              lang={lang}
                            />
                          </div>
                        )}

                        {/* Service Requirements */}
                        {service.serviceRequirements && service.serviceRequirements.length > 0 && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                              <FileText className="w-4 h-4 text-[var(--brand-blue)]" />
                              {isRTL ? "متطلبات الخدمة" : "Service Requirements"}
                            </h4>
                            <ServiceRequirementsVisualization
                              requirements={service.serviceRequirements}
                              lang={lang}
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Settings className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {isRTL ? "لا توجد خدمات" : "No Services"}
          </h3>
          <p className="text-gray-600 mb-4">
            {isRTL 
              ? "ابدأ بإضافة خدمة جديدة يقدمها النظام" 
              : "Start by adding a new service provided by the system"
            }
          </p>
          <Button
            onClick={() => setIsAddModalOpen(true)}
            className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            {isRTL ? "إضافة خدمة" : "Add Service"}
          </Button>
        </div>
      )}

      {/* Add/Edit Service Modal */}
      <AddServiceModal
        isOpen={isAddModalOpen}
        onClose={handleModalClose}
        systemId={systemId}
        lang={lang}
        editingService={editingService}
        onSuccess={() => {
          onServicesUpdate();
          handleModalClose();
        }}
      />

      {/* Delete Confirmation Modal */}
      {deleteConfirmService && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 rounded-lg bg-red-100">
                  <Trash2 className="w-5 h-5 text-red-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {isRTL ? "تأكيد حذف الخدمة" : "Confirm Service Deletion"}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {isRTL ? "هذا الإجراء لا يمكن التراجع عنه" : "This action cannot be undone"}
                  </p>
                </div>
              </div>

              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 rounded-xl bg-[var(--brand-blue)] flex items-center justify-center">
                    <Settings className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      {deleteConfirmService.serviceName}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {deleteConfirmService.serviceBeneficiaries} • {deleteConfirmService.serviceFees} {isRTL ? "ر.س" : "SAR"}
                    </p>
                  </div>
                </div>
                <p className="text-sm text-gray-700">
                  {deleteConfirmService.serviceDescription}
                </p>
              </div>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => setDeleteConfirmService(null)}
                  className="flex-1"
                  disabled={isDeleting === deleteConfirmService.id}
                >
                  {isRTL ? "إلغاء" : "Cancel"}
                </Button>
                <Button
                  onClick={handleConfirmDelete}
                  disabled={isDeleting === deleteConfirmService.id}
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white"
                >
                  {isDeleting === deleteConfirmService.id
                    ? (isRTL ? "جاري الحذف..." : "Deleting...")
                    : (isRTL ? "حذف الخدمة" : "Delete Service")
                  }
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
