"use client";

import React from "react";
import { motion } from "framer-motion";
import { Globe, Check } from "lucide-react";
import { Button } from "@/components/ui/button";

interface LanguageSelectorProps {
  selectedLanguage: 'en' | 'ar';
  onLanguageSelect: (language: 'en' | 'ar') => void;
  isRTL: boolean;
}

export function LanguageSelector({ selectedLanguage, onLanguageSelect, isRTL }: LanguageSelectorProps) {
  const languages = [
    {
      code: 'en' as const,
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸'
    },
    {
      code: 'ar' as const,
      name: 'Arabic',
      nativeName: 'العربية',
      flag: '🇸🇦'
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 bg-[var(--brand-blue)]/10 rounded-xl flex items-center justify-center">
          <Globe className="w-5 h-5 text-[var(--brand-blue)]" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            {isRTL ? "اختر اللغة" : "Select Language"}
          </h3>
          <p className="text-sm text-gray-600">
            {isRTL 
              ? "اختر اللغة لكتابة سياسة الخصوصية" 
              : "Choose the language for your privacy policy"
            }
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-3">
        {languages.map((language) => (
          <motion.div
            key={language.code}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Button
              variant={selectedLanguage === language.code ? "default" : "outline"}
              onClick={() => onLanguageSelect(language.code)}
              className={`w-full h-16 justify-start gap-4 text-left ${
                selectedLanguage === language.code
                  ? "bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white border-[var(--brand-blue)]"
                  : "hover:bg-gray-50 border-gray-200"
              }`}
            >
              <div className="flex items-center gap-3 flex-1">
                <span className="text-2xl">{language.flag}</span>
                <div className="flex-1">
                  <div className="font-semibold">{language.name}</div>
                  <div className={`text-sm ${
                    selectedLanguage === language.code ? "text-white/80" : "text-gray-500"
                  }`}>
                    {language.nativeName}
                  </div>
                </div>
                {selectedLanguage === language.code && (
                  <Check className="w-5 h-5 text-white" />
                )}
              </div>
            </Button>
          </motion.div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-start gap-3">
          <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
            <div className="w-2 h-2 bg-[var(--brand-blue)] rounded-full"></div>
          </div>
          <div className="flex-1">
            <p className="text-sm text-blue-800 font-medium mb-1">
              {isRTL ? "ملاحظة مهمة" : "Important Note"}
            </p>
            <p className="text-xs text-blue-700">
              {isRTL 
                ? "يمكنك إنشاء سياسات خصوصية منفصلة لكل لغة. سيتم حفظ كل سياسة بشكل منفصل."
                : "You can create separate privacy policies for each language. Each policy will be saved independently."
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
