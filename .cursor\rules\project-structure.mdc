---
description: 
globs: 
alwaysApply: true
---
# Project Structure Guide

This is a Next.js project built with TypeScript. Here's a breakdown of the key directories:

*   **`src/`**: Contains the main source code for the application.
    *   **`src/app/`**: Core application logic, likely using the Next.js App Router.
        *   **`src/app/[lang]/`**: Contains pages or components related to internationalization (i18n). Check [src/app/[lang]/page.tsx](mdc:src/app/[lang]/page.tsx) for the main page structure.
    *   **`src/components/`**: Reusable UI components used throughout the application. Shared components might be in [src/components/ui/](mdc:src/components/ui).
    *   **`src/dictionaries/`**: Likely holds translation files for different languages.
    *   **`src/Firebase/`**: Contains Firebase integration code, possibly including [src/Firebase/Authentication/](mdc:src/Firebase/Authentication) and [src/Firebase/firestore/](mdc:src/Firebase/firestore).
        *   Any Firebase service should be implemented within the relevant subdirectory under `src/Firebase/`, such as [src/Firebase/firestore/](mdc:src/Firebase/firestore).
    *   **`src/hooks/`**: Custom React hooks.
    *   **`src/lib/`**: Utility functions and shared libraries.
    *   **`src/api/`**: Server-side API routes.
        *   Any AI usage should be implemented in a subdirectory named after the AI Function Name, e.g., `src/api/Erd Generation/`.
            *   AI implementations should follow the Google Gen AI provider guidelines from the Vercel AI SDK documentation.
            *   AI implementations must incorporate rate limiting using Vercel KV and Upstash Ratelimit as described in the AI SDK documentation.
*   **`public/`**: Static assets accessible directly via the web server.
*   **`.env.local`**: Local environment variables. See [.env.local](mdc:.env.local). **Do not commit this file.**
*   **`next.config.ts`**: Next.js configuration file. See [next.config.ts](mdc:next.config.ts).
*   **`package.json`**: Project dependencies and scripts. See [package.json](mdc:package.json).
*   **`tsconfig.json`**: TypeScript configuration file. See [tsconfig.json](mdc:tsconfig.json).

## Additional Rules

1.  All pages must support both English and Arabic languages.
2.  All Pages should follow Mobile First Approach 
