# AI Classification Improvements

## Issues Addressed

### 1. User Identity Fields (CreatedBy, UpdatedBy, ModifiedBy)
**Problem**: Sometimes classified as personal data, sometimes not
**Solution**: Added context-dependent analysis rules:
- If contains usernames, email addresses, or personal names → CONFIDENTIAL + PERSONAL DATA
- If contains system user IDs or role codes → CONFIDEN<PERSON>AL + NOT PERSONAL DATA
- AI must analyze the business context to determine which type applies

### 2. User ID Fields (CreatorId, UserId, ClientId, TenantId)
**Problem**: Inconsistent classification of ID fields
**Solution**: Added decision tree logic:
- If IDs are linked to personal accounts or individual users → CONFIDENTIAL + PERSONAL DATA
- If IDs are system-generated tokens or organizational codes → CONF<PERSON>ENTIAL + NOT PERSONAL DATA
- AI must consider the business model (B2C vs B2B) to make the determination

### 3. Name Fields (ClientName, TenantName)
**Problem**: Sometimes personal data, sometimes not
**Solution**: Added specific analysis requirements:
- If names identify individual persons or sole proprietors → CONFIDENTIAL + PERSONAL DATA
- If names identify companies, organizations, or business entities → CONFIDENTIAL + NOT PERSONAL DATA
- AI must distinguish between natural persons and legal entities

### 4. Free Text Fields (Comments, Arguments, Parameters, ExtraProperties)
**Problem**: Inconsistent classification despite high risk of containing personal data
**Solution**: Added default assumption with context override:
- DEFAULT ASSUMPTION: CONFIDENTIAL + PERSONAL DATA (unless context clearly indicates otherwise)
- AI must assess business purpose and typical content
- Higher scrutiny for user-generated content fields

### 5. Vague Descriptions
**Problem**: Reasoning was too generic and not descriptive enough
**Solution**: Added specific requirements for detailed reasoning:
- Must specify exactly what type of data the field contains
- Must explain the business context and usage
- Must reference specific user personas and business processes
- Added good/bad examples to guide AI reasoning
- Banned uncertain language completely

## New Classification Rules

### Context-Dependent Analysis
The AI now performs deep contextual analysis for problematic field types:

1. **User Identity Fields**: Analyzes if fields store personal names vs system identifiers
2. **User ID Fields**: Determines if IDs trace back to individuals vs organizational entities
3. **Name Fields**: Distinguishes between person names and business entity names
4. **Free Text Fields**: Assesses potential for personal information content

### Decision Trees
Added specific decision trees for each problematic field type:
- Business context analysis (B2C vs B2B)
- User type analysis (individual vs organizational)
- Content type analysis (personal vs system-generated)
- Risk assessment for free text fields

### Enhanced Reasoning Requirements
- Must specify exact data type contained in each field
- Must explain why classification was chosen based on business context
- Must reference specific business processes and user personas
- Must provide detailed, unique reasoning for each field
- Completely eliminated uncertain language

## Implementation Details

### Updated Prompt Structure
1. **Context-Aware Field Analysis**: Specific rules for each problematic field type
2. **Decision Trees**: Step-by-step logic for classification decisions
3. **Enhanced Reasoning**: Detailed requirements for explanation quality
4. **Good/Bad Examples**: Clear examples of proper vs improper reasoning
5. **Anti-Templating Rules**: Prevents generic, copy-paste responses

### Key Improvements
- **Specificity**: AI must state exactly what type of data each field contains
- **Context Awareness**: Business context drives classification decisions
- **Consistency**: Decision trees ensure similar fields get similar treatment
- **Detail**: Reasoning must be comprehensive and business-specific
- **Certainty**: Eliminated all uncertain language from responses

## Expected Outcomes

1. **More Accurate Classifications**: Context-driven analysis should reduce misclassifications
2. **Consistent Decisions**: Similar fields should now receive consistent treatment
3. **Better Reasoning**: Explanations will be more detailed and business-specific
4. **Reduced False Positives/Negatives**: Better identification of personal data
5. **Improved Audit Trail**: More detailed reasoning for compliance purposes

## Testing Recommendations

1. Test with systems that have both individual and organizational users
2. Verify classification consistency across similar field types
3. Check reasoning quality and specificity
4. Validate personal data identification accuracy
5. Ensure business context is properly utilized in decisions
