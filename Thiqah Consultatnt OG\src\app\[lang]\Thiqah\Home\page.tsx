"use client";

import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { auth } from "@/Firebase/Authentication/authConfig";
import { Locale } from "@/i18n-config";
import { usePathname, useRouter } from "next/navigation";
import { 
  Database, 
  Users, 
  CheckCircle, 
  Plus,
  ArrowRight,
  Activity,
  UserCheck,
  FileCheck,
  ClipboardList
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { AuditService } from "@/Firebase/firestore/services/AuditService";
import { TasksService } from "@/Firebase/firestore/services/TasksService";
import { getUserProfile, getAllUsers, UserProfile } from "@/Firebase/firestore/services/UserService";
import { SystemsService } from "@/Firebase/firestore/SystemsService";

interface DashboardStats {
  observations: {
    total: number;
    open: number;
    inProgress: number;
    resolved: number;
    closed: number;
    byRating: {
      low: number;
      medium: number;
      high: number;
      critical: number;
    };
  };
  tasks: {
    total: number;
    pending: number;
    inProgress: number;
    completed: number;
    cancelled: number;
  };
  systems: {
    total: number;
    classified: number;
    pending: number;
  };
  users: {
    total: number;
    active: number;
  };
}

export default function HomePage() {
  const [userName, setUserName] = useState<string | null>(null);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const pathname = usePathname();
  const router = useRouter();
  const lang = pathname?.split('/')[1] as Locale;
  const isRTL = lang === "ar";

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (user) => {
      if (user) {
        setUserName(user.displayName || user.email?.split('@')[0] || 'User');
        try {
          await getUserProfile(user.uid);
        } catch (error) {
          console.error('Error fetching user profile:', error);
        }
      }
    });

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      setIsLoading(true);
      
      // Load all statistics in parallel
      const [observationStats, taskStats, systems, users] = await Promise.all([
        AuditService.getObservationStatistics(),
        TasksService.getTaskStatistics(),
        SystemsService.getSystems(),
        getAllUsers()
      ]);

      // Calculate system statistics
      const systemStats = {
        total: systems.length,
        classified: 0, // TODO: Implement proper classification status
        pending: systems.length // TODO: Implement proper pending status
      };

      // Calculate user statistics
      const userStats = {
        total: users.length,
        active: users.filter((u: UserProfile) => !u.disabled).length
      };

      setStats({
        observations: observationStats,
        tasks: taskStats,
        systems: systemStats,
        users: userStats
      });
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const navigateTo = (path: string) => {
    router.push(`/${lang}/Thiqah${path}`);
  };

  const quickActions = [
    {
      id: 'new-observation',
      title: isRTL ? "ملاحظة جديدة" : "New Observation",
      description: isRTL ? "إضافة ملاحظة تدقيق جديدة" : "Add new audit observation",
      icon: FileCheck,
      color: "from-red-500 to-red-600",
      onClick: () => navigateTo('/AuditFindings')
    },
    {
      id: 'new-task',
      title: isRTL ? "مهمة جديدة" : "New Task",
      description: isRTL ? "إنشاء مهمة جديدة" : "Create new task",
      icon: ClipboardList,
      color: "from-blue-500 to-blue-600",
      onClick: () => navigateTo('/Tasks')
    },
    {
      id: 'new-system',
      title: isRTL ? "نظام جديد" : "New System",
      description: isRTL ? "إضافة نظام للتصنيف" : "Add system for classification",
      icon: Database,
      color: "from-green-500 to-green-600",
      onClick: () => navigateTo('/DataClassification')
    },
    {
      id: 'user-management',
      title: isRTL ? "إدارة المستخدمين" : "User Management",
      description: isRTL ? "إدارة المستخدمين والأدوار" : "Manage users and roles",
      icon: UserCheck,
      color: "from-purple-500 to-purple-600",
      onClick: () => navigateTo('/UserManagement')
    }
  ];

  const moduleCards = [
    {
      id: 'audit-findings',
      title: isRTL ? "نتائج التدقيق" : "Audit Findings",
      description: isRTL ? "إدارة ملاحظات التدقيق والمخاطر" : "Manage audit observations and risks",
      icon: FileCheck,
      stats: stats ? [
        { label: isRTL ? "إجمالي" : "Total", value: stats.observations.total, color: "text-gray-600" },
        { label: isRTL ? "مفتوح" : "Open", value: stats.observations.open, color: "text-blue-600" },
        { label: isRTL ? "حرج" : "Critical", value: stats.observations.byRating.critical, color: "text-red-600" }
      ] : [],
      path: '/AuditFindings',
      gradient: "from-red-50 to-red-100",
      borderColor: "border-red-200"
    },
    {
      id: 'tasks',
      title: isRTL ? "إدارة المهام" : "Task Management",
      description: isRTL ? "تتبع وإدارة المهام والمشاريع" : "Track and manage tasks and projects",
      icon: ClipboardList,
      stats: stats ? [
        { label: isRTL ? "إجمالي" : "Total", value: stats.tasks.total, color: "text-gray-600" },
        { label: isRTL ? "قيد التنفيذ" : "In Progress", value: stats.tasks.inProgress, color: "text-blue-600" },
        { label: isRTL ? "مكتمل" : "Completed", value: stats.tasks.completed, color: "text-green-600" }
      ] : [],
      path: '/Tasks',
      gradient: "from-blue-50 to-blue-100",
      borderColor: "border-blue-200"
    },
    {
      id: 'data-classification',
      title: isRTL ? "تصنيف البيانات" : "Data Classification",
      description: isRTL ? "تصنيف وحماية البيانات الحساسة" : "Classify and protect sensitive data",
      icon: Database,
      stats: stats ? [
        { label: isRTL ? "الأنظمة" : "Systems", value: stats.systems.total, color: "text-gray-600" },
        { label: isRTL ? "مُصنف" : "Classified", value: stats.systems.classified, color: "text-green-600" },
        { label: isRTL ? "معلق" : "Pending", value: stats.systems.pending, color: "text-yellow-600" }
      ] : [],
      path: '/DataClassification',
      gradient: "from-green-50 to-green-100",
      borderColor: "border-green-200"
    },
    {
      id: 'user-management',
      title: isRTL ? "إدارة المستخدمين" : "User Management",
      description: isRTL ? "إدارة المستخدمين والصلاحيات" : "Manage users and permissions",
      icon: Users,
      stats: stats ? [
        { label: isRTL ? "المستخدمين" : "Users", value: stats.users.total, color: "text-gray-600" },
        { label: isRTL ? "نشط" : "Active", value: stats.users.active, color: "text-green-600" },
        { label: isRTL ? "المسؤولين" : "Admins", value: 2, color: "text-purple-600" }
      ] : [],
      path: '/UserManagement',
      gradient: "from-purple-50 to-purple-100",
      borderColor: "border-purple-200"
    }
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'observation',
      title: isRTL ? "ملاحظة تدقيق جديدة" : "New audit observation created",
      description: isRTL ? "تم إنشاء ملاحظة حول أمان البيانات" : "Data security observation created",
      time: isRTL ? "منذ ساعتين" : "2 hours ago",
      icon: FileCheck,
      color: "text-red-600 bg-red-100"
    },
    {
      id: 2,
      type: 'task',
      title: isRTL ? "مهمة مكتملة" : "Task completed",
      description: isRTL ? "تم إكمال مراجعة النظام" : "System review task completed",
      time: isRTL ? "منذ 4 ساعات" : "4 hours ago",
      icon: CheckCircle,
      color: "text-green-600 bg-green-100"
    },
    {
      id: 3,
      type: 'system',
      title: isRTL ? "نظام جديد مُضاف" : "New system added",
      description: isRTL ? "تم إضافة نظام CRM للتصنيف" : "CRM system added for classification",
      time: isRTL ? "أمس" : "Yesterday",
      icon: Database,
      color: "text-blue-600 bg-blue-100"
    }
  ];

  if (isLoading) {
    return (
      <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
        <div className="relative min-h-screen bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
          <div className="relative z-10 flex flex-col justify-center items-center min-h-screen px-8 py-16">
            <div className="animate-pulse text-center">
              <div className="w-24 h-24 bg-white/20 rounded-3xl mx-auto mb-8"></div>
              <div className="h-12 bg-white/20 rounded-lg w-96 mx-auto mb-4"></div>
              <div className="h-6 bg-white/20 rounded-lg w-64 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-64 h-64 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
        </div>

        <div className="relative z-10 px-8 py-12">
          <div className="max-w-7xl mx-auto">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="flex items-center justify-between mb-8"
            >
              <div>
                <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
                  {isRTL ? `مرحباً، ${userName || 'المستخدم'}` : `Welcome, ${userName || 'User'}`}
                </h1>
                <p className="text-white/90 text-lg">
                  {isRTL ? "لوحة التحكم الشاملة لإدارة المشاريع والامتثال" : "Comprehensive dashboard for project and compliance management"}
                </p>
              </div>
              <div className="flex items-center gap-4">
                <Button
                  onClick={loadDashboardStats}
                  className="bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20"
                >
                  <Activity className="w-4 h-4 mr-2" />
                  {isRTL ? "تحديث" : "Refresh"}
                </Button>
              </div>
            </motion.div>

            {/* Quick Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
            >
              {[
                { 
                  label: isRTL ? "ملاحظات التدقيق" : "Audit Observations", 
                  value: stats?.observations.total || 0, 
                  icon: FileCheck, 
                  color: "bg-white/10" 
                },
                { 
                  label: isRTL ? "المهام النشطة" : "Active Tasks", 
                  value: stats?.tasks.inProgress || 0, 
                  icon: ClipboardList, 
                  color: "bg-white/10" 
                },
                { 
                  label: isRTL ? "الأنظمة المُصنفة" : "Classified Systems", 
                  value: stats?.systems.classified || 0, 
                  icon: Database, 
                  color: "bg-white/10" 
                },
                { 
                  label: isRTL ? "المستخدمين النشطين" : "Active Users", 
                  value: stats?.users.active || 0, 
                  icon: Users, 
                  color: "bg-white/10" 
                }
              ].map((stat, index) => (
                <div key={index} className={`${stat.color} backdrop-blur-xl rounded-2xl p-6 border border-white/20`}>
                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-xl bg-white/20">
                      <stat.icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-white">{stat.value}</p>
                      <p className="text-white/80 text-sm">{stat.label}</p>
                    </div>
                  </div>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-8 py-8">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Quick Actions */}
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                {isRTL ? "الإجراءات السريعة" : "Quick Actions"}
              </h2>
              <Button
                onClick={() => navigateTo('/Tasks')}
                className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
              >
                <Plus className="w-4 h-4 mr-2" />
                {isRTL ? "جديد" : "New"}
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {quickActions.map((action) => (
                <motion.button
                  key={action.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={action.onClick}
                  className="group bg-white rounded-2xl shadow-lg border border-gray-200 p-6 text-left hover:shadow-xl transition-all duration-300"
                >
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${action.color} flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <action.icon className="w-6 h-6" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-[var(--brand-blue)] transition-colors duration-300">
                    {action.title}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    {action.description}
                  </p>
                  <div className="flex items-center text-[var(--brand-blue)] text-sm font-medium">
                    {isRTL ? "ابدأ الآن" : "Get Started"}
                    <ArrowRight className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'} group-hover:translate-x-1 transition-transform duration-300`} />
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.section>

          {/* Module Cards */}
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              {isRTL ? "الوحدات الرئيسية" : "Main Modules"}
            </h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {moduleCards.map((module) => (
                <motion.div
                  key={module.id}
                  whileHover={{ scale: 1.01 }}
                  onClick={() => navigateTo(module.path)}
                  className={`cursor-pointer bg-gradient-to-br ${module.gradient} rounded-2xl border ${module.borderColor} p-6 hover:shadow-lg transition-all duration-300`}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-sm">
                        <module.icon className="w-6 h-6 text-gray-700" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{module.title}</h3>
                        <p className="text-gray-600 text-sm">{module.description}</p>
                      </div>
                    </div>
                    <ArrowRight className={`w-5 h-5 text-gray-400 ${isRTL ? 'rotate-180' : ''}`} />
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4">
                    {module.stats.map((stat, index) => (
                      <div key={index} className="text-center">
                        <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>
                        <p className="text-xs text-gray-600">{stat.label}</p>
                      </div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Recent Activities */}
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                {isRTL ? "الأنشطة الأخيرة" : "Recent Activities"}
              </h2>
              <Button
                onClick={() => navigateTo('/Tasks')}
                variant="outline"
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                {isRTL ? "عرض الكل" : "View All"}
                <ArrowRight className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} />
              </Button>
            </div>
            
            <div className="bg-white rounded-2xl shadow-lg border border-gray-200 divide-y divide-gray-100">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                  <div className="flex items-start gap-4">
                    <div className={`p-2 rounded-lg ${activity.color}`}>
                      <activity.icon className="w-5 h-5" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-lg font-semibold text-gray-900 mb-1">
                        {activity.title}
                      </h4>
                      <p className="text-gray-600 text-sm mb-2">
                        {activity.description}
                      </p>
                      <p className="text-xs text-gray-500">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.section>


        </div>
      </div>
    </div>
  );
}
