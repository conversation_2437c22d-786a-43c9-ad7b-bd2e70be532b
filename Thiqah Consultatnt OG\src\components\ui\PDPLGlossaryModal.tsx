"use client";

import React, { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Search, BookOpen, Volume2, Calendar, Edit, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Locale } from "@/i18n-config";
import { Timestamp } from 'firebase/firestore';

interface PDPLGlossaryModalProps {
  isOpen: boolean;
  onClose: () => void;
  definitions: Array<{
    id?: string;
    term: string;
    definition: string;
    createdAt: Timestamp | Date | string;
    updatedAt: Timestamp | Date | string;
  }>;
  lang: Locale;
  onEdit?: (definitionId: string) => void;
  onDelete?: (definitionId: string) => void;
}

export function PDPLGlossaryModal({ isOpen, onClose, definitions, lang, onEdit, onDelete }: PDPLGlossaryModalProps) {
  const isRTL = lang === "ar";
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLetter, setSelectedLetter] = useState<string | null>(null);



  // Filter and sort definitions
  const filteredDefinitions = useMemo(() => {
    let filtered = definitions.filter(def => 
      def.term.toLowerCase().includes(searchTerm.toLowerCase()) ||
      def.definition.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    if (selectedLetter) {
      filtered = filtered.filter(def => 
        def.term.charAt(0).toUpperCase() === selectedLetter
      );
    }
    
    return filtered.sort((a, b) => a.term.localeCompare(b.term));
  }, [definitions, searchTerm, selectedLetter]);

  // Group definitions by first letter
  const groupedDefinitions = useMemo(() => {
    const groups: Record<string, typeof filteredDefinitions> = {};
    
    filteredDefinitions.forEach(def => {
      const firstLetter = def.term.charAt(0).toUpperCase();
      if (!groups[firstLetter]) {
        groups[firstLetter] = [];
      }
      groups[firstLetter].push(def);
    });
    
    return groups;
  }, [filteredDefinitions]);

  // Get all available letters
  const availableLetters = useMemo(() => {
    return Array.from(new Set(definitions.map(def => def.term.charAt(0).toUpperCase()))).sort();
  }, [definitions]);

  const alphabetKeys = selectedLetter ? [selectedLetter] : Object.keys(groupedDefinitions).sort();

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-4 md:inset-8 bg-white z-50 overflow-hidden"
            style={{ direction: isRTL ? "rtl" : "ltr" }}
          >
            {/* Dictionary Header */}
            <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/90 text-white p-6 border-b border-blue-300">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
                    <BookOpen className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold font-serif tracking-wide">
                      {isRTL ? "معجم تعريفات حماية البيانات الشخصية" : "Personal Data Protection Definitions Dictionary"}
                    </h2>
                    <p className="text-blue-100 font-medium">
                      {isRTL ? "الطبعة الموحدة" : "Unified Edition"} • {filteredDefinitions.length} {isRTL ? "تعريف" : "entries"}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  onClick={onClose}
                  className="text-white/80 hover:text-white hover:bg-white/20 border border-white/30 backdrop-blur-sm"
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>
            </div>

            <div className="flex h-full">
              {/* Alphabetical Navigation Sidebar */}
              <div className="w-20 bg-gradient-to-b from-blue-50 to-blue-100 border-r border-blue-200 overflow-y-auto">
                <div className="p-2 space-y-1">
                  {availableLetters.map((letter) => (
                    <button
                      key={letter}
                      onClick={() => setSelectedLetter(letter)}
                      className={`w-full h-12 rounded-lg font-bold text-sm transition-all duration-200 ${
                        selectedLetter === letter
                          ? 'bg-[var(--brand-blue)] text-white shadow-lg scale-105'
                          : 'bg-white/70 text-[var(--brand-blue)] hover:bg-white hover:shadow-md border border-blue-200/50'
                      }`}
                    >
                      {letter}
                    </button>
                  ))}
                </div>
              </div>

              {/* Main Content */}
              <div className="flex-1 flex flex-col">
                {/* Search Bar */}
                <div className="p-6 bg-gradient-to-r from-blue-50 to-blue-50/50 border-b border-blue-200">
                  <div className="relative">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-[var(--brand-blue)] w-5 h-5" />
                    <input
                      type="text"
                      placeholder={isRTL ? "ابحث في التعريفات..." : "Search definitions..."}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-12 pr-4 py-3 border border-blue-200 rounded-xl bg-white text-gray-900 placeholder:text-gray-500 focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-transparent font-medium"
                    />
                  </div>
                  {searchTerm && (
                    <p className="mt-3 text-sm text-[var(--brand-blue)] font-medium">
                      {isRTL ? `${filteredDefinitions.length} نتيجة بحث` : `${filteredDefinitions.length} search results`}
                    </p>
                  )}
                </div>

                {/* Dictionary Content */}
                <div className="flex-1 overflow-y-auto p-6 bg-white">
                  {filteredDefinitions.length > 0 ? (
                    <div className="space-y-8">
                      {alphabetKeys.map((letter) => (
                        <motion.div
                          key={letter}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          {/* Letter Section Header */}
                          <div className="flex items-center gap-4 mb-6">
                            <div className="w-16 h-16 bg-gradient-to-br from-[var(--brand-blue)] to-[var(--brand-blue)]/80 text-white rounded-lg flex items-center justify-center shadow-lg">
                              <span className="text-2xl font-bold font-serif">{letter}</span>
                            </div>
                            <div className="flex-1 h-px bg-gradient-to-r from-blue-200 to-transparent"></div>
                            <span className="text-sm font-medium text-[var(--brand-blue)] bg-blue-50 px-3 py-1 rounded-full">
                              {groupedDefinitions[letter].length} {isRTL ? "تعريف" : "entries"}
                            </span>
                          </div>

                          {/* Dictionary Entries */}
                          <div className="space-y-4">
                            {groupedDefinitions[letter].map((definition, index) => (
                              <motion.div
                                key={definition.id}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.05 }}
                                className="bg-gradient-to-r from-white to-blue-50/30 border border-blue-200/50 rounded-xl p-6 hover:shadow-lg transition-all duration-300 group"
                              >
                                <div className="flex items-start gap-4">
                                  <div className="flex-shrink-0 w-8 h-8 bg-[var(--brand-blue)] text-white rounded-full flex items-center justify-center text-sm font-bold shadow-sm group-hover:scale-110 transition-transform duration-300">
                                    {index + 1}
                                  </div>
                                  
                                  <div className="flex-1">
                                    <div className="flex items-start justify-between mb-3">
                                      <h3 className="text-xl font-bold text-gray-900 font-serif leading-tight group-hover:text-[var(--brand-blue)] transition-colors duration-300">
                                        {definition.term}
                                      </h3>
                                      <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                        {onEdit && (
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => onEdit(definition.id!)}
                                            className="text-[var(--brand-blue)] hover:bg-blue-50 border border-blue-200/50"
                                          >
                                            <Edit className="w-4 h-4" />
                                          </Button>
                                        )}
                                        {onDelete && (
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => onDelete(definition.id!)}
                                            className="text-red-600 hover:bg-red-50 border border-red-200/50"
                                          >
                                            <Trash2 className="w-4 h-4" />
                                          </Button>
                                        )}
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          className="text-[var(--brand-blue)] hover:bg-blue-50"
                                        >
                                          <Volume2 className="w-4 h-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    
                                    <p className="text-gray-700 leading-relaxed text-base font-medium mb-3">
                                      {definition.definition}
                                    </p>
                                    
                                    <div className="flex items-center text-xs text-[var(--brand-blue)] font-medium">
                                      <span className="bg-blue-100 px-2 py-1 rounded-full">
                                        {isRTL ? "مشترك" : "Shared"} • {isRTL ? "جميع الوثائق" : "All Documents"}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-center py-20"
                    >
                      <div className="w-32 h-32 bg-gradient-to-br from-blue-100 to-blue-50 rounded-full flex items-center justify-center mx-auto mb-6">
                        <Search className="w-12 h-12 text-[var(--brand-blue)]" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3">
                        {isRTL ? "لا توجد نتائج" : "No Results Found"}
                      </h3>
                      <p className="text-gray-600">
                        {isRTL ? "جرب البحث بكلمات مختلفة" : "Try searching with different terms"}
                      </p>
                    </motion.div>
                  )}
                </div>

                {/* Dictionary Footer */}
                <div className="bg-gradient-to-r from-blue-50 to-blue-50/50 border-t border-blue-200 px-6 py-4">
                  <div className="flex items-center justify-between text-xs text-[var(--brand-blue)] font-medium">
                    <div className="flex items-center gap-2">
                      <BookOpen className="w-3 h-3" />
                      <span>{isRTL ? "معجم حماية البيانات الشخصية" : "Personal Data Protection Dictionary"}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-3 h-3" />
                      <span>{isRTL ? "الطبعة الموحدة ٢٠٢٤" : "Unified Edition 2024"}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
} 