import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow processing up to 60 seconds for entity grouping analysis
export const maxDuration = 60;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Schema for entity grouping response with descriptions
const EntityGroupingSchema = z.object({
  groups: z.array(z.object({
    groupName: z.string(),
    groupDescription: z.string(),
    attributeIds: z.array(z.string()),
    attributeReasons: z.array(z.object({
      attributeId: z.string(),
      reason: z.string() // 8-word reason why this attribute is in this group
    }))
  }))
});

// Schema for request body
const RequestSchema = z.object({
  personalData: z.array(z.object({
    id: z.string(),
    schemaName: z.string().optional(),
    tableName: z.string(),
    columnName: z.string(),
    dataType: z.string(),
    hasPersonalData: z.boolean().optional(),
    personalDataType: z.string().optional(),
    specialCategoryType: z.string().optional(),
    confidentialityLevel: z.string().optional(),
    personalDataReasoning: z.string().optional(),
    auditTrail: z.string().optional()
  })),
  systemId: z.string(),
  systemContext: z.string().optional()
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    const { personalData, systemId, systemContext } = RequestSchema.parse(body);

    if (!personalData || personalData.length === 0) {
      return NextResponse.json(
        { error: 'No personal data provided for entity grouping' },
        { status: 400 }
      );
    }

    // Filter only personal data with audit trail = "No"
    const eligibleData = personalData.filter(record => 
      record.hasPersonalData === true && record.auditTrail === "No"
    );

    if (eligibleData.length === 0) {
      return NextResponse.json(
        { error: 'No eligible personal data found (must have auditTrail = "No")' },
        { status: 400 }
      );
    }

    console.log(`Processing ${eligibleData.length} personal data records for entity grouping`);

    // Get system information
    let systemName = '';
    let isSaberSystem = false;
    try {
      const { SystemsService } = await import('@/Firebase/firestore/SystemsService');
      const systems = await SystemsService.getSystems();
      const currentSystem = systems.find(sys => sys.id === systemId);
      
      if (currentSystem) {
        systemName = currentSystem.name;
        isSaberSystem = systemName.toLowerCase().includes('saber');
        console.log(`System identified: "${systemName}", SABER system: ${isSaberSystem}`);
      } else {
        console.log('System not found, using generic context');
      }
    } catch (error) {
      console.error('Error fetching system info:', error);
      console.log('Failed to fetch system info, using generic context');
    }

    // Get business context
    let businessContext = '';
    if (systemContext && systemContext.trim()) {
      businessContext = `

ACTUAL BUSINESS CONTEXT FOR THIS SYSTEM:
${systemContext}

This context describes the specific business functions and processes that this database supports.`;
      console.log('Using Firebase system context in AI prompt');
    } else {
      console.log('No system context provided - using system name for context');
    }

    // Prepare system overview for entity grouping
    let systemOverview = '';
    if (isSaberSystem) {
      systemOverview = `You are creating entity groups for the SABER platform - Saudi Arabia's national conformity assessment system.

SABER Platform Overview:
- SABER manages product certificates, facility registrations, and compliance requests
- Handles sensitive business data for importers, manufacturers, and certification bodies
- Contains user accounts, facility information, and regulatory compliance data
- Manages financial transactions and payment processing through SADAD
- Stores audit trails and regulatory compliance documentation${businessContext}`;
    } else {
      systemOverview = `You are creating entity groups for the "${systemName}" system.

${systemName} System Overview:
- This is a business system that manages operational data and processes
- Contains user accounts, business transactions, and operational records
- Stores customer information, business relationships, and workflow data${businessContext}`;
    }

    const prompt = `${systemOverview}

TASK: Create intelligent groupings of personal data attributes with HIGHLY DETAILED, SPECIFIC explanations.

CRITICAL REQUIREMENTS:
- Create 6-15 logical groups based on business functions and data subjects
- Every record must be assigned to exactly one group
- Generate UNIQUE, SPECIFIC descriptions and reasons (absolutely NO generic text)
- Each description must be tailored to the ACTUAL attributes in that specific group

GROUP DESCRIPTION REQUIREMENTS:
- Write exactly 120 words describing the group's specific business purpose and context
- Analyze the ACTUAL table and column names in the group to create meaningful explanations
- Explain what type of data is included and WHY these specific attributes belong together
- Detail the business processes, workflows, and operations that use this data
- Mention specific business scenarios where this data group is utilized
- Reference the data subjects and their relationship to the business
- Use professional, business-focused language with concrete examples
- Make each description completely unique - no template language

ATTRIBUTE REASON REQUIREMENTS:
- Write exactly 8 words explaining why THIS SPECIFIC attribute belongs in THIS SPECIFIC group
- Each reason must be unique and tailored to the individual attribute's business purpose
- Consider the attribute's specific role in business processes and workflows
- Reference the data subject, business function, or operational context
- Analyze the column name and table context to create meaningful explanations
- NO GENERIC PHRASES like "related data attribute grouping" or "supports group operations"

EXAMPLES OF EXCELLENT ATTRIBUTE REASONS:
- "stores customer email address for communication and authentication"
- "tracks user login timestamps for security audit trails"
- "contains employee salary information for payroll processing workflows"
- "manages product pricing data for inventory and sales"
- "records financial transaction amounts for accounting and reporting"
- "captures user profile preferences for personalization and recommendations"
- "stores document attachment metadata for regulatory compliance tracking"

GROUPING STRATEGY:
- Group by data subject (customers, employees, vendors, products, transactions)
- Group by business function (authentication, billing, inventory, reporting, compliance)
- Group by processing purpose (identity verification, payment processing, analytics, auditing)

PERSONAL DATA TO GROUP:
${eligibleData.map((record, index) =>
  `${index + 1}. Table: ${record.tableName}, Column: ${record.columnName}
     Data Type: ${record.dataType}
     Personal Data Type: ${record.personalDataType || 'Not classified'}
     Special Category: ${record.specialCategoryType || 'None'}
     Confidentiality: ${record.confidentialityLevel || 'Not classified'}
     Context: ${record.personalDataReasoning || 'Personal data field'}
     Record ID: ${record.id}`
).join('\n\n')}

RESPONSE FORMAT:
Return ONLY a JSON object with "groups" array. Each group should have:
{
  "groupName": "Clear Business Function Name",
  "groupDescription": "Exactly 120 words describing the specific business purpose, data types included, business processes that use this data, operational workflows, and business scenarios. Be specific to the actual attributes in this group, analyze their table and column names, and explain their business value and usage context with concrete examples.",
  "attributeIds": ["recordId1", "recordId2", "recordId3"],
  "attributeReasons": [
    {
      "attributeId": "recordId1",
      "reason": "unique eight words specific to this attribute's purpose"
    },
    {
      "attributeId": "recordId2",
      "reason": "different eight words explaining this attribute's business role"
    }
  ]
}

CRITICAL RULES:
- Every record ID must be assigned to exactly one group
- No record should be left ungrouped
- Group descriptions must be EXACTLY 120 words
- Attribute reasons must be EXACTLY 8 words each
- NO GENERIC TEXT - every description and reason must be unique and specific
- Analyze the actual table and column names to create meaningful explanations
- Reference specific business processes and workflows
- Create 6-15 groups total
- Each description must be completely unique with no template language

FORBIDDEN PHRASES (DO NOT USE):
- "Related data attribute grouping"
- "Existing group with X attributes"
- "Data grouping"
- "Personal data field"
- "supports group operations"
- "belongs to this category"
- Any generic or template-like language
- Any placeholder text

ANALYSIS REQUIREMENTS:
- Study each table name and column name carefully
- Infer business purpose from naming conventions
- Consider data relationships and workflows
- Reference specific business scenarios and use cases
- Create descriptions that demonstrate deep understanding of the data's business context

Generate intelligent, specific groupings with detailed business context and analysis.`;

    // Use Gemini-2.5-pro for complex entity grouping analysis
    const result = await generateObject({
      model: google('gemini-2.5-pro'),
      prompt: prompt,
      schema: EntityGroupingSchema,
      maxTokens: 66000, // High limit to ensure complete response even with large datasets
    });

    console.log(`Entity grouping completed for ${eligibleData.length} records`);
    console.log('=== ENTITY GROUPING RESULTS ===');
    console.log(`Generated ${result.object.groups.length} groups:`);

    result.object.groups.forEach((group, index) => {
      console.log(`Group ${index + 1}: ${group.groupName}`);
      console.log(`  - Description: ${group.groupDescription}`);
      console.log(`  - Attributes: ${group.attributeIds.length} items`);
      console.log(`  - Reasons: ${group.attributeReasons.length} explanations`);
    });
    console.log('=== END ENTITY GROUPING RESULTS ===');

    // Verify all attributes are assigned
    const totalAssigned = result.object.groups.reduce((sum, group) => sum + group.attributeIds.length, 0);
    console.log(`Total attributes assigned: ${totalAssigned} / ${eligibleData.length}`);

    // Return the entity groups
    return NextResponse.json({
      success: true,
      feature: 'entity_grouping',
      systemId,
      recordsProcessed: eligibleData.length,
      groupsCreated: result.object.groups.length,
      groups: result.object.groups
    });

  } catch (error) {
    console.error('Entity grouping error:', error);
    
    // Handle different types of errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error during entity grouping' },
      { status: 500 }
    );
  }
}
