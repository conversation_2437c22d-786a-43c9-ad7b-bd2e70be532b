"use client";

import React, { useState } from "react";

import { X, Download, Filter, Search } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SentenceAnalysisCard } from "./SentenceAnalysisCard";
import { MissingDomainsCard } from "./MissingDomainsCard";
import { AnalysisSummaryCard } from "./AnalysisSummaryCard";

interface SentenceAnalysis {
  sentence: string;
  domain: string;
  domainDescription: string;
  isCompliant: boolean;
  completenessScore: number;
  issues: string[];
  suggestions: string[];
}

interface PolicyAnalysis {
  sentences: SentenceAnalysis[];
  overallScore: number;
  missingDomains: string[];
  recommendations: string[];
  complianceLevel: 'Excellent' | 'Good' | 'Needs Improvement' | 'Poor';
}

interface AnalysisResultsModalProps {
  isOpen: boolean;
  onClose: () => void;
  analysis: PolicyAnalysis | null;
  systemName: string;
  isRTL: boolean;
}

export function AnalysisResultsModal({ 
  isOpen, 
  onClose, 
  analysis, 
  systemName, 
  isRTL 
}: AnalysisResultsModalProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterDomain, setFilterDomain] = useState("all");
  const [filterCompliance, setFilterCompliance] = useState("all");

  if (!analysis) return null;

  // Get unique domains for filter
  const uniqueDomains = Array.from(new Set(analysis.sentences.map(s => s.domain)));

  // Filter sentences based on search and filters
  const filteredSentences = analysis.sentences.filter(sentence => {
    const matchesSearch = sentence.sentence.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sentence.domain.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDomain = filterDomain === "all" || sentence.domain === filterDomain;
    const matchesCompliance = filterCompliance === "all" || 
                             (filterCompliance === "compliant" && sentence.isCompliant) ||
                             (filterCompliance === "non-compliant" && !sentence.isCompliant);
    
    return matchesSearch && matchesDomain && matchesCompliance;
  });

  const compliantSentences = analysis.sentences.filter(s => s.isCompliant).length;

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log("Export analysis results");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[95vw] w-[95vw] max-h-[95vh] h-[95vh] flex flex-col p-0">
        {/* Header */}
        <DialogHeader className="flex-shrink-0 px-8 py-6 border-b border-gray-200 bg-gradient-to-r from-[var(--brand-blue)]/5 to-[var(--brand-blue)]/10">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl font-bold text-gray-900">
                {isRTL ? "نتائج تحليل سياسة الخصوصية" : "Privacy Policy Analysis Results"}
              </DialogTitle>
              <p className="text-lg text-gray-600 mt-2">
                {systemName}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                onClick={handleExport}
                variant="outline"
                className="border-[var(--brand-blue)] text-[var(--brand-blue)] hover:bg-[var(--brand-blue)] hover:text-white"
              >
                <Download className="w-4 h-4 mr-2" />
                {isRTL ? "تصدير" : "Export"}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="w-6 h-6" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex">
          {/* Left Sidebar - Summary */}
          <div className="w-80 flex-shrink-0 border-r border-gray-200 bg-gray-50 overflow-y-auto">
            <div className="p-6 space-y-6">
              <AnalysisSummaryCard
                overallScore={analysis.overallScore}
                complianceLevel={analysis.complianceLevel}
                totalSentences={analysis.sentences.length}
                compliantSentences={compliantSentences}
                missingDomainsCount={analysis.missingDomains.length}
                isRTL={isRTL}
              />
              
              <MissingDomainsCard
                missingDomains={analysis.missingDomains}
                recommendations={analysis.recommendations}
                isRTL={isRTL}
              />
            </div>
          </div>

          {/* Main Content - Sentence Analysis */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Filters */}
            <div className="flex-shrink-0 p-6 border-b border-gray-200 bg-white">
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder={isRTL ? "البحث في الجمل..." : "Search sentences..."}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <Select value={filterDomain} onValueChange={setFilterDomain}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder={isRTL ? "تصفية حسب المجال" : "Filter by Domain"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{isRTL ? "جميع المجالات" : "All Domains"}</SelectItem>
                    {uniqueDomains.map(domain => (
                      <SelectItem key={domain} value={domain}>{domain}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={filterCompliance} onValueChange={setFilterCompliance}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder={isRTL ? "تصفية حسب التوافق" : "Filter by Compliance"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{isRTL ? "جميع الجمل" : "All Sentences"}</SelectItem>
                    <SelectItem value="compliant">{isRTL ? "متوافقة" : "Compliant"}</SelectItem>
                    <SelectItem value="non-compliant">{isRTL ? "غير متوافقة" : "Non-Compliant"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="mt-4 text-sm text-gray-600">
                {isRTL 
                  ? `عرض ${filteredSentences.length} من ${analysis.sentences.length} جملة`
                  : `Showing ${filteredSentences.length} of ${analysis.sentences.length} sentences`
                }
              </div>
            </div>

            {/* Sentence Cards */}
            <div className="flex-1 overflow-y-auto p-6">
              <div className="space-y-4">
                {filteredSentences.map((sentence, index) => (
                  <SentenceAnalysisCard
                    key={index}
                    analysis={sentence}
                    index={index}
                    isRTL={isRTL}
                  />
                ))}
                
                {filteredSentences.length === 0 && (
                  <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                      <Filter className="w-12 h-12 mx-auto" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {isRTL ? "لا توجد نتائج" : "No Results Found"}
                    </h3>
                    <p className="text-gray-600">
                      {isRTL 
                        ? "جرب تغيير معايير البحث أو التصفية"
                        : "Try adjusting your search or filter criteria"
                      }
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
