"use client";

import React from "react";
import { FileText, Info, CreditCard, Shield, CheckCircle, AlertCircle } from "lucide-react";
import { ServiceRequirement } from "@/Firebase/firestore/SystemsService";

interface ServiceRequirementsVisualizationProps {
  requirements: ServiceRequirement[];
  lang: string;
}

export function ServiceRequirementsVisualization({ requirements, lang }: ServiceRequirementsVisualizationProps) {
  const isRTL = lang === "ar";

  if (!requirements || requirements.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        {isRTL ? "لا توجد متطلبات محددة" : "No requirements defined"}
      </div>
    );
  }

  const getRequirementIcon = (type: string) => {
    switch (type) {
      case 'Document':
        return FileText;
      case 'Information':
        return Info;
      case 'Payment':
        return CreditCard;
      case 'Authentication':
        return Shield;
      default:
        return FileText;
    }
  };

  const getRequirementColor = (type: string) => {
    switch (type) {
      case 'Document':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'Information':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'Payment':
        return 'bg-orange-100 text-orange-700 border-orange-200';
      case 'Authentication':
        return 'bg-purple-100 text-purple-700 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getTypeLabel = (type: string) => {
    const labels = {
      'Document': isRTL ? 'وثيقة' : 'Document',
      'Information': isRTL ? 'معلومات' : 'Information',
      'Payment': isRTL ? 'دفع' : 'Payment',
      'Authentication': isRTL ? 'مصادقة' : 'Authentication'
    };
    return labels[type as keyof typeof labels] || type;
  };

  // Group requirements by type
  const groupedRequirements = requirements.reduce((acc, req) => {
    if (!acc[req.type]) {
      acc[req.type] = [];
    }
    acc[req.type].push(req);
    return acc;
  }, {} as Record<string, ServiceRequirement[]>);

  return (
    <div className="space-y-6">
      {/* Requirements by Type */}
      {Object.entries(groupedRequirements).map(([type, typeRequirements]) => {
        const IconComponent = getRequirementIcon(type);
        const colorClass = getRequirementColor(type);
        
        return (
          <div key={type} className="space-y-3">
            {/* Type Header */}
            <div className={`flex items-center gap-3 p-3 rounded-lg border ${colorClass}`}>
              <IconComponent className="w-5 h-5" />
              <h4 className="font-semibold">
                {getTypeLabel(type)} ({typeRequirements.length})
              </h4>
            </div>

            {/* Requirements List */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 ml-4">
              {typeRequirements.map((requirement) => (
                <div
                  key={requirement.id}
                  className="p-4 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-300"
                >
                  <div className="flex items-start justify-between mb-2">
                    <h5 className="font-medium text-gray-900 text-sm">
                      {requirement.title}
                    </h5>
                    <div className={`flex items-center gap-1 text-xs px-2 py-1 rounded-full ${
                      requirement.isRequired 
                        ? 'bg-red-100 text-red-700' 
                        : 'bg-green-100 text-green-700'
                    }`}>
                      {requirement.isRequired ? (
                        <AlertCircle className="w-3 h-3" />
                      ) : (
                        <CheckCircle className="w-3 h-3" />
                      )}
                      {requirement.isRequired 
                        ? (isRTL ? "مطلوب" : "Required")
                        : (isRTL ? "اختياري" : "Optional")
                      }
                    </div>
                  </div>
                  
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {requirement.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        );
      })}

      {/* Summary Statistics */}
      <div className="mt-6 p-4 bg-gray-50 rounded-xl border border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-gray-900">
              {requirements.length}
            </div>
            <div className="text-sm text-gray-600">
              {isRTL ? "إجمالي المتطلبات" : "Total Requirements"}
            </div>
          </div>
          
          <div>
            <div className="text-2xl font-bold text-red-600">
              {requirements.filter(r => r.isRequired).length}
            </div>
            <div className="text-sm text-gray-600">
              {isRTL ? "مطلوبة" : "Required"}
            </div>
          </div>
          
          <div>
            <div className="text-2xl font-bold text-green-600">
              {requirements.filter(r => !r.isRequired).length}
            </div>
            <div className="text-sm text-gray-600">
              {isRTL ? "اختيارية" : "Optional"}
            </div>
          </div>
          
          <div>
            <div className="text-2xl font-bold text-blue-600">
              {Object.keys(groupedRequirements).length}
            </div>
            <div className="text-sm text-gray-600">
              {isRTL ? "أنواع" : "Types"}
            </div>
          </div>
        </div>
      </div>

      {/* Requirements Checklist */}
      <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
        <h4 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
          <CheckCircle className="w-4 h-4" />
          {isRTL ? "قائمة التحقق" : "Requirements Checklist"}
        </h4>
        <div className="space-y-2">
          {requirements.map((requirement) => (
            <div key={requirement.id} className="flex items-center gap-3 text-sm">
              <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                requirement.isRequired 
                  ? 'border-red-400 bg-red-50' 
                  : 'border-green-400 bg-green-50'
              }`}>
                {requirement.isRequired ? (
                  <AlertCircle className="w-3 h-3 text-red-600" />
                ) : (
                  <CheckCircle className="w-3 h-3 text-green-600" />
                )}
              </div>
              <span className="text-blue-800">
                {requirement.title}
              </span>
              <span className="text-blue-600 text-xs">
                ({getTypeLabel(requirement.type)})
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
