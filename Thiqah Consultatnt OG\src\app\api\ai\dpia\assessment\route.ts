import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import type { SystemData } from '@/Firebase/firestore/SystemsService';

// Allow processing up to 60 seconds for DPIA assessment
export const maxDuration = 60;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Schema for DPIA assessment response
const DPIAAssessmentSchema = z.object({
  assessments: z.array(z.object({
    attributeId: z.string(),
    tableName: z.string(),
    columnName: z.string(),
    domainAnswers: z.object({
      individualHarm: z.object({
        identityTheft: z.string(),
        financialExploitation: z.string(),
        privacyInvasion: z.string(),
        psychologicalImpact: z.string(),
        riskLevel: z.enum(["HIGH", "MEDIUM", "LOW"])
      }),
      businessReputation: z.object({
        trustErosion: z.string(),
        mediaCoverage: z.string(),
        brandDamage: z.string(),
        competitiveIssues: z.string(),
        riskLevel: z.enum(["HIGH", "MEDIUM", "LOW"])
      }),
      financialConsequences: z.object({
        regulatoryFines: z.string(),
        legalCosts: z.string(),
        riskLevel: z.enum(["HIGH", "MEDIUM", "LOW"])
      }),
      legalRegulatoryConsequences: z.object({
        pdplViolations: z.string(),
        criminalLiability: z.string(),
        internationalIssues: z.string(),
        riskLevel: z.enum(["HIGH", "MEDIUM", "LOW"])
      })
    }),
    finalRiskLevel: z.enum(["HIGH", "MEDIUM", "LOW"]),
    overallImpact: z.string()
  }))
});

// Schema for request body
const RequestSchema = z.object({
  systemId: z.string(),
  attributeIds: z.array(z.string()).max(5), // Max 5 attributes per assessment
  systemContext: z.string().optional()
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    const { systemId, attributeIds, systemContext } = RequestSchema.parse(body);

    if (!attributeIds || attributeIds.length === 0) {
      return NextResponse.json(
        { error: 'No attribute IDs provided for DPIA assessment' },
        { status: 400 }
      );
    }

    if (attributeIds.length > 5) {
      return NextResponse.json(
        { error: 'Maximum 5 attributes allowed per DPIA assessment' },
        { status: 400 }
      );
    }

    // Get system information and attribute data
    let systemName = '';
    let isSaberSystem = false;
    let attributeData: SystemData[] = [];
    
    try {
      // Import SystemsService dynamically to avoid circular dependencies
      const { SystemsService } = await import('@/Firebase/firestore/SystemsService');
      const systems = await SystemsService.getSystems();
      const currentSystem = systems.find(sys => sys.id === systemId);
      
      if (currentSystem) {
        systemName = currentSystem.name;
        isSaberSystem = systemName.toLowerCase().includes('saber');
        console.log(`System identified: "${systemName}", SABER system: ${isSaberSystem}`);
      } else {
        console.log('System not found, using generic context');
      }

      // Get attribute data for the specified IDs
      const allSystemData = await SystemsService.getSystemData(systemId);
      attributeData = allSystemData.filter(item => attributeIds.includes(item.id!));
      
      if (attributeData.length === 0) {
        return NextResponse.json(
          { error: 'No valid attributes found for the provided IDs' },
          { status: 400 }
        );
      }

    } catch (error) {
      console.error('Error fetching system/attribute data:', error);
      return NextResponse.json(
        { error: 'Failed to fetch system data for assessment' },
        { status: 500 }
      );
    }

    // Get actual business context from the system
    console.log('System context received:', systemContext ? `"${systemContext}"` : 'null/undefined');
    let businessContext = '';
    if (systemContext && systemContext.trim()) {
      businessContext = `

ACTUAL BUSINESS CONTEXT FOR THIS SYSTEM:
${systemContext}

This context describes the specific business functions and processes that this database supports.`;
      console.log('Using Firebase system context in AI prompt');
    } else {
      console.log('No system context provided - using system name for context');
    }

    // Prepare prompt for DPIA assessment
    let systemOverview = '';
    
    if (isSaberSystem) {
      systemOverview = `You are conducting a DPIA (Data Protection Impact Assessment) for the SABER platform - Saudi Arabia's national conformity assessment system.

SABER Platform Overview:
- SABER manages product certificates, facility registrations, and compliance requests
- Handles sensitive business data for importers, manufacturers, and certification bodies
- Processes regulatory compliance documents and technical regulations
- Manages user accounts for facilities, organizations, and government entities
- Stores financial data for fees, bills, and payments through SADAD system
- Contains audit trails for regulatory compliance and government oversight${businessContext}`;
    } else {
      systemOverview = `You are conducting a DPIA (Data Protection Impact Assessment) for the "${systemName}" system.

${systemName} System Overview:
- This is a business system that manages operational data and processes
- Contains user accounts, business transactions, and operational records
- Stores configuration data, audit trails, and business workflow information
- Manages relationships between different business entities and processes${businessContext}`;
    }

    const prompt = `${systemOverview}

DPIA ASSESSMENT MISSION:
You are evaluating the impact of personal data LEAKAGE/BREACH for each database column. For each attribute, assess what would happen if this specific data was exposed, stolen, or misused.

ASSESSMENT DOMAINS AND CRITERIA:

1. 📱 INDIVIDUAL HARM DOMAIN:
   - Identity Theft: Can this data be used for complete identity theft?
   - Financial Exploitation: Can this data enable banking/financial fraud?
   - Privacy Invasion: Does this data expose personal life completely?
   - Psychological Impact: Would exposure cause severe anxiety/vulnerability?

2. 🏢 BUSINESS REPUTATION DOMAIN:
   - Trust Erosion: Would customers have major exodus from breach?
   - Media Coverage: Would this attract national headlines?
   - Brand Damage: Would this cause long-term trust issues?
   - Competitive Issues: Would security failures be highlighted?

3. 💰 FINANCIAL CONSEQUENCES DOMAIN:
   - Regulatory Fines: What SAR amount in PDPL penalties?
   - Legal Costs: What class action lawsuits expected?

4. ⚖️ LEGAL/REGULATORY CONSEQUENCES DOMAIN:
   - PDPL Violations: What specific Saudi PDPL violations?
   - Criminal Liability: Could individual executives face charges?
   - International Issues: Would EU GDPR fines apply for European customers?

RISK LEVEL CRITERIA:

HIGH RISK:
- Individual Harm: Complete identity theft, major financial fraud, severe privacy violation, significant psychological trauma
- Business Reputation: Major customer exodus, front-page news coverage, permanent brand damage, significant competitive loss
- Financial Consequences: SAR 1M+ fines, major legal battles
- Legal/Regulatory: Serious PDPL violations, criminal prosecution, international sanctions

MEDIUM RISK:
- Individual Harm: Partial identity exposure, moderate financial risk, some privacy concerns, mild distress
- Business Reputation: Some customer concern, local news coverage, temporary reputation impact, minor competitive effect
- Financial Consequences: SAR 100K-1M fines, moderate legal costs
- Legal/Regulatory: Minor PDPL violations, regulatory scrutiny, compliance requirements

LOW RISK:
- Individual Harm: Minimal personal impact, no financial risk, limited privacy concern, no psychological effect
- Business Reputation: Little customer impact, no media attention, minimal reputation effect, no competitive impact
- Financial Consequences: Under SAR 100K fines, minimal legal costs
- Legal/Regulatory: Technical violations only, minor compliance issues, no international impact

ATTRIBUTES TO ASSESS:

${attributeData.map(attr => `
ATTRIBUTE ID: ${attr.id}
DATABASE FIELD: ${attr.tableName}.${attr.columnName}
DATA TYPE: ${attr.dataType}
CONFIDENTIALITY LEVEL: ${attr.confidentialityLevel || 'Not classified'}
CONFIDENTIALITY REASONING: ${attr.confidentialityReasoning || 'No reasoning provided'}
HAS PERSONAL DATA: ${attr.hasPersonalData ? 'Yes' : 'No'}
PERSONAL DATA REASONING: ${attr.personalDataReasoning || attr.personalDataReason || 'No reasoning provided'}
PERSONAL DATA TYPE: ${attr.personalDataType || 'Not classified'}
SPECIAL CATEGORY: ${attr.specialCategoryType || 'Not classified'}
EXISTING CONTEXT: This column was previously analyzed and classified. Use this existing analysis to inform your DPIA assessment.
BREACH SCENARIO: Analyze what happens if the "${attr.columnName}" column data from the ${attr.tableName} table is leaked or breached, considering the existing classification reasoning.
`).join('\n')}

ASSESSMENT REQUIREMENTS:

1. USE EXISTING CLASSIFICATION CONTEXT: Build upon the existing confidentiality and personal data reasoning provided for each attribute. Your DPIA assessment should be consistent with and expand upon the previous analysis.

2. ATTRIBUTE-SPECIFIC ANALYSIS: Each attribute must have completely unique reasoning based on:
   - The EXACT column name and its specific data purpose
   - The existing confidentiality reasoning (why it was classified as Public/Confidential/Secret/Top Secret)
   - The existing personal data reasoning (why it was identified as personal data or not)
   - The personal data type classification (direct/indirect/pseudonymous/anonymous)
   - The special category classification (PII/PHI/PCI/Genetic/Biometric)

3. CONTEXTUAL CONSISTENCY: Your DPIA assessment must be logically consistent with the existing classification:
   - If classified as "Top Secret" with reasoning about "critical security credentials", your DPIA should reflect HIGH risk across multiple domains
   - If classified as "Public" with reasoning about "generic system labels", your DPIA should reflect LOW risk
   - If identified as "direct personal data" with "PII" category, emphasize identity theft and privacy invasion risks
   - If identified as "indirect personal data", focus on profiling and inference risks

4. SAUDI ARABIA SPECIFIC SCENARIOS: Provide concrete, believable examples based on:
   - Saudi Personal Data Protection Law (PDPL) requirements and penalties
   - Saudi business culture and customer expectations
   - SABER platform context (if applicable) with regulatory compliance implications
   - Regional media reaction patterns and business reputation impacts

5. DOMAIN-SPECIFIC REASONING: For each domain, consider the existing classification context:
   - Individual Harm: Build on personal data reasoning to assess identity, financial, privacy, and psychological impacts
   - Business Reputation: Consider confidentiality level and business context for trust, media, brand, and competitive impacts
   - Financial Consequences: Align with confidentiality level for regulatory fines and legal costs
   - Legal/Regulatory: Use existing classification to assess PDPL violations, criminal liability, and international issues

6. RISK LEVEL CONSISTENCY: Risk levels should align with existing classification:
   - Top Secret + Direct PII → Likely HIGH risk
   - Secret + Indirect Personal Data → Likely MEDIUM risk
   - Confidential + No Personal Data → Likely LOW risk
   - Public + Generic Data → Likely LOW risk

7. MAXIMUM 10 WORDS PER REASONING: Keep each reasoning text to maximum 10 words only, but make them highly specific to the attribute and its existing classification.

8. CLEAN REASONING TEXT: Do NOT include risk level indicators (HIGH, MEDIUM, LOW) in the reasoning text. The risk level is captured separately in the riskLevel field. Reasoning should only contain the actual justification without prefixes like "Medium:", "Low:", or "High:".

9. FINAL RISK LEVEL CALCULATION: The finalRiskLevel should be calculated based on the highest risk level among all domains using this logic:
   - If ANY domain has HIGH risk → finalRiskLevel = HIGH
   - If ANY domain has MEDIUM risk (and no HIGH) → finalRiskLevel = MEDIUM
   - If ALL domains have LOW risk → finalRiskLevel = LOW
   This ensures logical consistency between domain-specific risks and the overall final risk.

CRITICAL ANALYSIS APPROACH:

For each attribute, follow this analysis pattern:
1. READ the existing confidentiality reasoning and personal data reasoning
2. UNDERSTAND why this specific column was classified the way it was
3. BUILD your DPIA assessment on top of that existing analysis
4. ENSURE consistency between the existing classification and your risk assessment
5. MAKE each reasoning unique and specific to the column's actual purpose and content

EXAMPLE ANALYSIS APPROACH:
If an attribute has:
- Column: "UserPassword"
- Confidentiality: "Top Secret" with reasoning "Contains authentication credentials for system access"
- Personal Data: "Yes" with reasoning "Directly identifies users through login credentials"

Your DPIA should reflect:
- Identity Theft: "Password enables complete account takeover and impersonation"
- Financial Exploitation: "Account access allows unauthorized financial transactions"
- Business Reputation: "Password breach destroys customer trust in security"
- PDPL Violations: "Authentication data breach violates PDPL Article X"

Every assessment must be unique, specific to the actual column name and its existing classification context, and reflect real Saudi Arabian business and regulatory consequences.

JSON format: {"assessments": [{"attributeId": "...", "tableName": "...", "columnName": "...", "domainAnswers": {"individualHarm": {"identityTheft": "...", "financialExploitation": "...", "privacyInvasion": "...", "psychologicalImpact": "...", "riskLevel": "HIGH/MEDIUM/LOW"}, "businessReputation": {"trustErosion": "...", "mediaCoverage": "...", "brandDamage": "...", "competitiveIssues": "...", "riskLevel": "HIGH/MEDIUM/LOW"}, "financialConsequences": {"regulatoryFines": "...", "legalCosts": "...", "riskLevel": "HIGH/MEDIUM/LOW"}, "legalRegulatoryConsequences": {"pdplViolations": "...", "criminalLiability": "...", "internationalIssues": "...", "riskLevel": "HIGH/MEDIUM/LOW"}}, "finalRiskLevel": "HIGH/MEDIUM/LOW", "overallImpact": "..."}]}`;

    let processedAssessments;

    try {
      console.log(`Sending DPIA assessment prompt to AI for ${attributeData.length} attributes`);

      // Use gemini-2.5-flash for DPIA assessment
      const result = await generateObject({
        model: google('gemini-2.5-flash'),
        prompt: prompt,
        schema: DPIAAssessmentSchema,
        maxTokens: 8000,
      });

      console.log('AI DPIA assessment response received:', result.object);
      processedAssessments = result.object.assessments;

      // Ensure final risk level is calculated correctly based on domain risk levels
      processedAssessments = processedAssessments.map(assessment => {
        const domainRisks = [
          assessment.domainAnswers.individualHarm.riskLevel,
          assessment.domainAnswers.businessReputation.riskLevel,
          assessment.domainAnswers.financialConsequences.riskLevel,
          assessment.domainAnswers.legalRegulatoryConsequences.riskLevel
        ];

        // Calculate final risk level: highest risk wins
        let calculatedFinalRisk: "HIGH" | "MEDIUM" | "LOW" = "LOW";
        if (domainRisks.includes("HIGH")) {
          calculatedFinalRisk = "HIGH";
        } else if (domainRisks.includes("MEDIUM")) {
          calculatedFinalRisk = "MEDIUM";
        }

        // Use calculated risk if different from AI's assessment
        if (calculatedFinalRisk !== assessment.finalRiskLevel) {
          console.log(`Correcting final risk level for ${assessment.attributeId}: AI said ${assessment.finalRiskLevel}, calculated ${calculatedFinalRisk} based on domains [${domainRisks.join(', ')}]`);
        }

        return {
          ...assessment,
          finalRiskLevel: calculatedFinalRisk
        };
      });



    } catch (aiError) {
      console.error('AI DPIA assessment failed:', aiError);

      return NextResponse.json(
        {
          error: 'AI DPIA assessment service unavailable',
          details: 'The AI DPIA assessment service is currently unavailable. Please try again later.',
          systemId,
          attributeIds,
          recordsProcessed: 0
        },
        { status: 503 }
      );
    }

    // Save DPIA assessments to SystemsService
    try {
      const { SystemsService } = await import('@/Firebase/firestore/SystemsService');

      // Transform AI response to match SystemsService DPIAAssessment interface
      const dpiaAssessments = processedAssessments.map(assessment => ({
        attributeId: assessment.attributeId,
        systemId: systemId,
        tableName: assessment.tableName,
        columnName: assessment.columnName,
        domainAnswers: {
          individualHarm: {
            riskLevel: assessment.domainAnswers.individualHarm.riskLevel,
            identityRecreation: assessment.domainAnswers.individualHarm.identityTheft,
            financialExploitation: assessment.domainAnswers.individualHarm.financialExploitation,
            privacyInvasion: assessment.domainAnswers.individualHarm.privacyInvasion,
            psychologicalImpact: assessment.domainAnswers.individualHarm.psychologicalImpact
          },
          businessReputation: {
            riskLevel: assessment.domainAnswers.businessReputation.riskLevel,
            trustErosion: assessment.domainAnswers.businessReputation.trustErosion,
            mediaCoverage: assessment.domainAnswers.businessReputation.mediaCoverage,
            brandDamage: assessment.domainAnswers.businessReputation.brandDamage,
            competitiveDisadvantage: assessment.domainAnswers.businessReputation.competitiveIssues
          },
          financialImpact: {
            riskLevel: assessment.domainAnswers.financialConsequences.riskLevel,
            regulatoryFines: assessment.domainAnswers.financialConsequences.regulatoryFines,
            legalCosts: assessment.domainAnswers.financialConsequences.legalCosts,
            remediationCosts: "Not assessed in this implementation",
            revenueLoss: "Not assessed in this implementation"
          },
          operationalImpact: {
            riskLevel: "LOW" as const, // Default since not assessed in current implementation
            recoveryTime: "Not assessed in this implementation",
            resourceDiversion: "Not assessed in this implementation",
            processBreakdown: "Not assessed in this implementation",
            customerService: "Not assessed in this implementation"
          },
          legalRegulatory: {
            riskLevel: assessment.domainAnswers.legalRegulatoryConsequences.riskLevel,
            pdplViolations: assessment.domainAnswers.legalRegulatoryConsequences.pdplViolations,
            sdaiaRisk: "Not assessed in this implementation",
            criminalLiability: assessment.domainAnswers.legalRegulatoryConsequences.criminalLiability,
            internationalIssues: assessment.domainAnswers.legalRegulatoryConsequences.internationalIssues
          }
        },
        finalRiskLevel: assessment.finalRiskLevel,
        overallImpact: assessment.overallImpact
      }));

      await SystemsService.saveDPIAAssessment(systemId, dpiaAssessments);
      console.log(`Successfully saved ${dpiaAssessments.length} DPIA assessments`);

    } catch (saveError) {
      console.error('Error saving DPIA assessments:', saveError);
      return NextResponse.json(
        {
          error: 'Failed to save DPIA assessments',
          details: 'The assessments were completed but could not be saved. Please try again.',
          systemId,
          attributeIds,
          assessments: processedAssessments
        },
        { status: 500 }
      );
    }

    // Return assessment results
    return NextResponse.json({
      success: true,
      feature: 'dpia_assessment',
      systemId,
      attributeIds,
      recordsProcessed: attributeData.length,
      assessments: processedAssessments
    });

  } catch (error) {
    console.error('Error in DPIA assessment:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error during DPIA assessment' },
      { status: 500 }
    );
  }
}
