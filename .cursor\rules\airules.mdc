---
description: 
globs: 
alwaysApply: false
---
# AI Foundational Development Rules

## Overview
This rule defines the foundational requirements for implementing AI features in our Next.js application using Google Generative AI within the AI SDK framework with per-endpoint rate limiting strategy.

## Core Requirements

### 1. AI SDK Integration
- **ALWAYS** use AI SDK for all AI integrations
- Reference the official AI SDK documentation: https://sdk.vercel.ai/docs
- Use AI SDK Core functions like `generateText()`, `streamText()`, `generateObject()`, `streamObject()`
- Follow AI SDK patterns and best practices

### 2. Google Generative AI Provider
- **ALWAYS** use Google Generative AI within AI SDK
- Use `@ai-sdk/google` provider: `import { google } from '@ai-sdk/google';`
- Reference Google Generative AI Provider documentation: https://sdk.vercel.ai/providers/ai-sdk-providers/google-generative-ai
- Set up proper environment variables: `GOOGLE_GENERATIVE_AI_API_KEY`

### 3. Per-Endpoint Model Selection Strategy
- **Model selection is done per API call** based on task complexity within each endpoint
- **gemini-2.0-flash-thinking-exp-1219**: Use for super difficult tasks
  - Strategy creation
  - Long context window requirements
  - Complex reasoning tasks
  - Multi-step problem solving
  - Data analysis and insights
- **gemini-2.0-flash-exp**: Use for standard tasks
  - Low thinking requirements
  - Shorter context windows
  - Quick responses
  - General content generation
  - Simple chat interactions

### 4. Per-Endpoint Rate Limiting Implementation
- **Rate limits differ per API endpoint** - each AI feature has its own monthly limit
- **NOT one general limit** for all AI features
- Use Vercel KV and Upstash for rate limiting
- Track rate limiting separately per endpoint/feature for granular usage control
- Reference: https://sdk.vercel.ai/docs/advanced/rate-limiting
- Implementation pattern per endpoint:
  ```typescript
  import kv from '@vercel/kv';
  import { Ratelimit } from '@upstash/ratelimit';
  
  // Each endpoint has its own rate limit
  const ratelimit = new Ratelimit({
    redis: kv,
    limiter: Ratelimit.fixedWindow(ENDPOINT_SPECIFIC_LIMIT, '30d'),
    prefix: 'ai_feature_name', // Unique prefix per feature
  });
  ```

### 5. Frontend UI Components
- Create AI frontend components in: @src/components/ui/ai-frontend/
- **MUST** display remaining and used request counts **per feature/endpoint**
- Show rate limit status for each AI feature separately
- Include loading states and error handling
- Follow consistent design patterns with existing UI components

### 6. API Endpoints Structure
- Create all AI API endpoints in: @src/app/api/
- Each AI feature gets its own endpoint with specific rate limiting
- Follow Next.js 14 App Router API patterns
- Implement proper error handling per endpoint
- Include rate limiting middleware per endpoint
- Use proper TypeScript interfaces

### 7. Per-Feature Rate Limit Configuration
- **NEVER** implement before asking user for rate limits for that specific feature
- Each endpoint/feature has its own monthly limit that resets each month
- Always confirm specific limits for each feature before implementation
- Document rate limits clearly in code comments per endpoint

### 8. Error Handling and Validation
- Implement comprehensive error handling for AI operations per endpoint
- Validate all AI inputs and outputs per feature
- Handle rate limit exceeded scenarios gracefully per endpoint
- Provide meaningful error messages to users per feature

### 9. Development Process
- **ALWAYS** ask for clarification when something is unclear
- Never make assumptions about requirements
- Follow the principle: "Ask first, implement second"
- Document all AI feature decisions and configurations per endpoint

## File Structure References

### API Routes
- @src/app/api/ - All AI API endpoints go here
- @src/app/api/ai/ - Existing AI directory structure
- Each feature gets its own route: `/api/ai/chat`, `/api/ai/strategy`, `/api/ai/analysis`

### UI Components
- @src/components/ui/ai-frontend/ - AI-specific frontend components
- @src/components/ui/ - General UI components for reference

### Configuration Files
- @package.json - Dependencies and scripts
- @tsconfig.json - TypeScript configuration
- @next.config.js - Next.js configuration

## Implementation Checklist

Before implementing any AI feature:
- [ ] Confirm rate limits with user for this specific feature
- [ ] Choose appropriate Gemini model based on task complexity for this endpoint
- [ ] Set up rate limiting on both frontend and API for this specific feature
- [ ] Create API endpoint in correct directory with unique rate limiting prefix
- [ ] Build UI component with request count display for this feature
- [ ] Implement comprehensive error handling for this endpoint
- [ ] Test rate limiting functionality for this feature
- [ ] Document configuration and usage for this endpoint

## Best Practices

1. **Security**: Never expose API keys in client-side code
2. **Performance**: Use streaming for long responses
3. **UX**: Always show loading states and progress indicators per feature
4. **Monitoring**: Log AI usage for debugging and optimization per endpoint
5. **Caching**: Implement appropriate caching strategies per feature
6. **Fallbacks**: Provide fallback content when AI services are unavailable
7. **Granular Control**: Track and display usage separately for each AI feature

## Example Implementation Pattern

```typescript
// API Route Example: /api/ai/strategy/route.ts
import { google } from '@ai-sdk/google';
import { generateText } from 'ai';
import { Ratelimit } from '@upstash/ratelimit';
import kv from '@vercel/kv';

// Strategy feature specific rate limiting
const ratelimit = new Ratelimit({
  redis: kv,
  limiter: Ratelimit.fixedWindow(STRATEGY_MONTHLY_LIMIT, '30d'),
  prefix: 'ai_strategy', // Unique prefix for strategy feature
});

export async function POST(req: Request) {
  const { success, remaining } = await ratelimit.limit(userIdentifier);
  
  if (!success) {
    return Response.json({ 
      error: 'Strategy generation limit exceeded',
      feature: 'strategy'
    }, { status: 429 });
  }

  // Use thinking model for complex strategy tasks
  const result = await generateText({
    model: google('gemini-2.0-flash-thinking-exp-1219'),
    prompt: strategyPrompt,
    maxTokens: 2000,
  });

  return Response.json({ 
    text: result.text, 
    remaining,
    usage: result.usage,
    feature: 'strategy'
  });
}
```

```typescript
// API Route Example: /api/ai/chat/route.ts
import { google } from '@ai-sdk/google';
import { generateText } from 'ai';
import { Ratelimit } from '@upstash/ratelimit';
import kv from '@vercel/kv';

// Chat feature specific rate limiting
const ratelimit = new Ratelimit({
  redis: kv,
  limiter: Ratelimit.fixedWindow(CHAT_MONTHLY_LIMIT, '30d'),
  prefix: 'ai_chat', // Unique prefix for chat feature
});

export async function POST(req: Request) {
  const { success, remaining } = await ratelimit.limit(userIdentifier);
  
  if (!success) {
    return Response.json({ 
      error: 'Chat limit exceeded',
      feature: 'chat'
    }, { status: 429 });
  }

  // Use fast model for simple chat interactions
  const result = await generateText({
    model: google('gemini-2.0-flash-exp'),
    prompt: chatPrompt,
    maxTokens: 1000,
  });

  return Response.json({ 
    text: result.text, 
    remaining,
    usage: result.usage,
    feature: 'chat'
  });
}
```

This rule ensures flexible, granular AI feature implementation with proper per-endpoint rate limiting and model selection.
