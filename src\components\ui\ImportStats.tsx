"use client";

import React from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";

interface ImportStatsProps {
  stats: {
    domains: number;
    controls: number;
    specs: number;
  };
  lang: Locale;
}

export function ImportStats({ stats, lang }: ImportStatsProps) {
  const isRTL = lang === "ar";
  
  const statItems = [
    {
      title: isRTL ? "المجالات" : "Domains",
      value: stats.domains,
      color: "from-blue-500/50 to-blue-600/50",
      icon: (
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      delay: 0.2,
    },
    {
      title: isRTL ? "الضوابط" : "Controls",
      value: stats.controls,
      color: "from-purple-500/50 to-purple-600/50",
      icon: (
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
        </svg>
      ),
      delay: 0.4,
    },
    {
      title: isRTL ? "المواصفات" : "Specifications",
      value: stats.specs,
      color: "from-emerald-500/50 to-emerald-600/50",
      icon: (
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
        </svg>
      ),
      delay: 0.6,
    },
  ];
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-4xl mx-auto mt-8 bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10"
    >
      <h2 className="text-xl md:text-2xl font-bold text-white mb-6 text-center">
        {isRTL ? "تم استيراد البيانات بنجاح" : "Data Imported Successfully"}
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {statItems.map((item, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: item.delay }}
            className={`bg-gradient-to-br ${item.color} rounded-lg p-4 flex items-center`}
          >
            <div className="bg-white/20 rounded-full p-3 mr-4">
              {item.icon}
            </div>
            <div>
              <p className="text-white/70 text-sm">{item.title}</p>
              <p className="text-white text-2xl font-bold">{item.value}</p>
            </div>
          </motion.div>
        ))}
      </div>
      
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="mt-6 text-center"
      >
        <p className="text-white/60 text-sm">
          {isRTL 
            ? "يمكنك الآن استعراض البيانات المستوردة ضمن المجالات والضوابط أدناه."
            : "You can now explore the imported data within domains and controls below."}
        </p>
      </motion.div>
    </motion.div>
  );
} 