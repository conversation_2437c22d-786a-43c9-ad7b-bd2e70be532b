"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Calendar, FileText, Eye } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { SystemPrivacyPolicy } from "@/Firebase/firestore/SystemsService";

interface PrivacyPolicyViewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  privacyPolicies: SystemPrivacyPolicy[];
  systemName: string;
  isRTL: boolean;
}

export function PrivacyPolicyViewerModal({ 
  isOpen, 
  onClose, 
  privacyPolicies, 
  systemName, 
  isRTL 
}: PrivacyPolicyViewerModalProps) {
  const [selectedPolicy, setSelectedPolicy] = useState<SystemPrivacyPolicy | null>(null);

  const handlePolicySelect = (policy: SystemPrivacyPolicy) => {
    setSelectedPolicy(policy);
  };

  const handleBack = () => {
    setSelectedPolicy(null);
  };

  const handleClose = () => {
    setSelectedPolicy(null);
    onClose();
  };

  const formatDate = (timestamp: unknown) => {
    if (!timestamp) return 'N/A';

    // Handle different timestamp types
    let date: Date;
    if (timestamp && typeof timestamp === 'object' && 'toDate' in timestamp && typeof (timestamp as { toDate: () => Date }).toDate === 'function') {
      date = (timestamp as { toDate: () => Date }).toDate();
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else if (typeof timestamp === 'string' || typeof timestamp === 'number') {
      date = new Date(timestamp);
    } else {
      return 'N/A';
    }

    return date.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[95vw] w-[95vw] max-h-[95vh] h-[95vh] flex flex-col p-0">
        {/* Header */}
        <DialogHeader className="flex-shrink-0 px-8 py-6 border-b border-gray-200 bg-gradient-to-r from-[var(--brand-blue)]/5 to-[var(--brand-blue)]/10">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <div className="w-10 h-10 bg-[var(--brand-blue)]/10 rounded-xl flex items-center justify-center">
                  <Eye className="w-5 h-5 text-[var(--brand-blue)]" />
                </div>
                {isRTL ? "عرض سياسة الخصوصية" : "Privacy Policy Viewer"}
              </DialogTitle>
              <p className="text-lg text-gray-600 mt-2">
                {systemName}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-6 h-6" />
            </Button>
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <AnimatePresence mode="wait">
            {!selectedPolicy ? (
              // Policy Selection View
              <motion.div
                key="selection"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="h-full overflow-y-auto p-8"
              >
                <div className="max-w-4xl mx-auto">
                  <div className="text-center mb-8">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {isRTL ? "اختر سياسة الخصوصية للعرض" : "Select Privacy Policy to View"}
                    </h3>
                    <p className="text-gray-600">
                      {isRTL 
                        ? `${privacyPolicies.length} سياسة خصوصية متاحة`
                        : `${privacyPolicies.length} privacy policies available`
                      }
                    </p>
                  </div>

                  <div className="grid gap-6">
                    {privacyPolicies.map((policy) => (
                      <motion.div
                        key={policy.id}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => handlePolicySelect(policy)}
                        className="bg-white border-2 border-gray-200 rounded-2xl p-6 cursor-pointer hover:border-[var(--brand-blue)] hover:shadow-lg transition-all duration-300"
                      >
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-4">
                            <div className="w-12 h-12 bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 rounded-xl flex items-center justify-center text-white text-xl">
                              {policy.language === 'ar' ? '🇸🇦' : '🇺🇸'}
                            </div>
                            <div>
                              <h4 className="text-lg font-semibold text-gray-900">
                                {policy.language === 'ar' ? 'سياسة الخصوصية - العربية' : 'Privacy Policy - English'}
                              </h4>
                              <div className="flex items-center gap-4 mt-1">
                                <div className="flex items-center gap-1 text-sm text-gray-500">
                                  <Calendar className="w-4 h-4" />
                                  {formatDate(policy.createdAt)}
                                </div>
                                <div className="flex items-center gap-1 text-sm text-gray-500">
                                  <FileText className="w-4 h-4" />
                                  {policy.content.split(' ').length} {isRTL ? 'كلمة' : 'words'}
                                </div>
                              </div>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-[var(--brand-blue)] text-[var(--brand-blue)] hover:bg-[var(--brand-blue)] hover:text-white"
                          >
                            {isRTL ? "عرض" : "View"}
                          </Button>
                        </div>
                        
                        <div className={`text-sm text-gray-700 line-clamp-3 ${
                          policy.language === 'ar' ? 'text-right' : 'text-left'
                        }`}>
                          {policy.content}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ) : (
              // Policy Reading View
              <motion.div
                key="reading"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="h-full flex flex-col"
              >
                {/* Policy Header */}
                <div className="flex-shrink-0 px-8 py-6 border-b border-gray-200 bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleBack}
                        className="flex items-center gap-2"
                      >
                        {isRTL ? "رجوع" : "Back"}
                      </Button>
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">
                          {selectedPolicy.language === 'ar' ? '🇸🇦' : '🇺🇸'}
                        </span>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">
                            {selectedPolicy.language === 'ar' ? 'سياسة الخصوصية - العربية' : 'Privacy Policy - English'}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {formatDate(selectedPolicy.createdAt)}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <FileText className="w-4 h-4" />
                      {selectedPolicy.content.split(' ').length} {isRTL ? 'كلمة' : 'words'}
                    </div>
                  </div>
                </div>

                {/* Policy Content */}
                <div className="flex-1 overflow-y-auto bg-white">
                  <div className="max-w-5xl mx-auto px-12 py-16">
                    {/* Document Header */}
                    <div className="text-center mb-12 pb-8 border-b-2 border-gray-200">
                      <h1 className={`text-4xl font-bold text-gray-900 mb-4 ${
                        selectedPolicy.language === 'ar' ? 'text-right' : 'text-left'
                      }`}>
                        {selectedPolicy.language === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy'}
                      </h1>
                      <div className={`text-lg text-gray-600 ${
                        selectedPolicy.language === 'ar' ? 'text-right' : 'text-left'
                      }`}>
                        <p className="mb-2">{systemName}</p>
                        <p className="text-sm">
                          {isRTL ? 'تاريخ الإنشاء:' : 'Created:'} {formatDate(selectedPolicy.createdAt)}
                        </p>
                      </div>
                    </div>

                    {/* Policy Content */}
                    <div className={`prose prose-xl max-w-none ${
                      selectedPolicy.language === 'ar' ? 'text-right' : 'text-left'
                    }`}>
                      <div
                        className="whitespace-pre-wrap text-gray-800 leading-relaxed"
                        dir={selectedPolicy.language === 'ar' ? 'rtl' : 'ltr'}
                        style={{
                          fontSize: '18px',
                          lineHeight: '2.0',
                          fontFamily: selectedPolicy.language === 'ar'
                            ? '"Segoe UI", Tahoma, Arial, sans-serif'
                            : '"Georgia", "Times New Roman", serif',
                          textAlign: selectedPolicy.language === 'ar' ? 'right' : 'justify',
                          color: '#1f2937',
                          letterSpacing: '0.025em'
                        }}
                      >
                        {selectedPolicy.content}
                      </div>
                    </div>

                    {/* Document Footer */}
                    <div className={`mt-16 pt-8 border-t-2 border-gray-200 text-center ${
                      selectedPolicy.language === 'ar' ? 'text-right' : 'text-left'
                    }`}>
                      <div className="text-sm text-gray-500">
                        <p className="mb-2">
                          {isRTL ? 'آخر تحديث:' : 'Last Updated:'} {formatDate(selectedPolicy.updatedAt || selectedPolicy.createdAt)}
                        </p>
                        <p>
                          {selectedPolicy.content.split(' ').length} {isRTL ? 'كلمة' : 'words'} •
                          {selectedPolicy.language === 'ar' ? ' العربية' : ' English'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </DialogContent>
    </Dialog>
  );
}
