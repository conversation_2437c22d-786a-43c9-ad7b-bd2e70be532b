"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";
import {
  CalendarDays,
  Plus,
  Users,
  Clock,
  MapPin,
  CheckCircle,
  AlertCircle,
  Calendar as CalendarIcon,
  Filter,
  Search,
  Eye,
  Trash2,
  FileText
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MeetingsService, Meeting, MeetingStatus, MeetingType } from "@/Firebase/firestore/services/MeetingsService";
import { useToast } from "@/components/ui/use-toast";
import { AddMeetingModal } from "@/components/ui/AddMeetingModal";
import { MeetingDetailsModal } from "@/components/ui/MeetingDetailsModal";

interface MeetingsPageProps {
  params: Promise<{ lang: Locale }>;
}

export default function MeetingsPage({ params }: MeetingsPageProps) {
  const [lang, setLang] = useState<Locale>("en");
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [filteredMeetings, setFilteredMeetings] = useState<Meeting[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedMeeting, setSelectedMeeting] = useState<Meeting | null>(null);
  const [deletingMeetingId, setDeletingMeetingId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [statistics, setStatistics] = useState({
    total: 0,
    scheduled: 0,
    completed: 0,
    cancelled: 0,
    thisMonth: 0,
    inProgress: 0,
  });
  const { toast } = useToast();

  // Resolve params
  useEffect(() => {
    params.then((resolvedParams) => {
      setLang(resolvedParams.lang);
    });
  }, [params]);

  const isRTL = lang === "ar";

  // Load meetings
  const loadMeetings = useCallback(async () => {
    try {
      setIsLoading(true);
      const [meetingsData, stats] = await Promise.all([
        MeetingsService.getMeetings(),
        MeetingsService.getMeetingStatistics()
      ]);
      setMeetings(meetingsData);
      setFilteredMeetings(meetingsData);
      setStatistics(stats);
    } catch (error) {
      console.error("Error loading meetings:", error);
      toast({
        title: isRTL ? "خطأ في تحميل الاجتماعات" : "Error loading meetings",
        description: isRTL ? "حدث خطأ أثناء تحميل الاجتماعات" : "An error occurred while loading meetings",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [isRTL, toast]);

  // Load data when component mounts
  useEffect(() => {
    if (lang) {
      loadMeetings();
    }
  }, [lang, loadMeetings]);

  // Filter meetings based on search and filters
  useEffect(() => {
    let filtered = meetings;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(meeting =>
        meeting.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        meeting.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        meeting.attendees.some(attendee => 
          attendee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          attendee.email.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(meeting => meeting.status === statusFilter);
    }

    // Type filter
    if (typeFilter !== "all") {
      filtered = filtered.filter(meeting => meeting.meetingType === typeFilter);
    }

    setFilteredMeetings(filtered);
  }, [meetings, searchTerm, statusFilter, typeFilter]);

  // Handle meeting creation
  const handleCreateMeeting = async (meetingData: Omit<Meeting, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      await MeetingsService.createMeeting(meetingData);
      toast({
        title: isRTL ? "تم إنشاء الاجتماع" : "Meeting Created",
        description: isRTL ? "تم إنشاء الاجتماع بنجاح" : "Meeting has been created successfully",
      });
      loadMeetings();
      setIsAddModalOpen(false);
    } catch (error) {
      console.error("Error creating meeting:", error);
      toast({
        title: isRTL ? "خطأ في إنشاء الاجتماع" : "Error creating meeting",
        description: isRTL ? "حدث خطأ أثناء إنشاء الاجتماع" : "An error occurred while creating the meeting",
        variant: "destructive",
      });
    }
  };

  // Handle meeting details view
  const handleViewMeeting = (meeting: Meeting) => {
    setSelectedMeeting(meeting);
    setIsDetailsModalOpen(true);
  };

  // Handle meeting deletion
  const handleDeleteMeeting = async (meetingId: string, meetingTitle: string) => {
    if (!confirm(isRTL
      ? `هل أنت متأكد من حذف الاجتماع "${meetingTitle}"؟`
      : `Are you sure you want to delete the meeting "${meetingTitle}"?`
    )) {
      return;
    }

    try {
      setDeletingMeetingId(meetingId);
      await MeetingsService.deleteMeeting(meetingId);
      toast({
        title: isRTL ? "تم حذف الاجتماع" : "Meeting Deleted",
        description: isRTL ? "تم حذف الاجتماع بنجاح" : "Meeting has been deleted successfully",
      });
      loadMeetings();
    } catch (error) {
      console.error("Error deleting meeting:", error);
      toast({
        title: isRTL ? "خطأ في الحذف" : "Error Deleting",
        description: isRTL ? "حدث خطأ أثناء حذف الاجتماع" : "An error occurred while deleting the meeting",
        variant: "destructive",
      });
    } finally {
      setDeletingMeetingId(null);
    }
  };

  // Get status color
  const getStatusColor = (status: MeetingStatus) => {
    switch (status) {
      case MeetingStatus.SCHEDULED:
        return "bg-blue-100 text-blue-800 border-blue-200";
      case MeetingStatus.IN_PROGRESS:
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case MeetingStatus.COMPLETED:
        return "bg-green-100 text-green-800 border-green-200";
      case MeetingStatus.CANCELLED:
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  // Get type color
  const getTypeColor = (type: MeetingType) => {
    switch (type) {
      case MeetingType.INTERNAL:
        return "bg-purple-100 text-purple-800 border-purple-200";
      case MeetingType.CLIENT:
        return "bg-blue-100 text-blue-800 border-blue-200";
      case MeetingType.VENDOR:
        return "bg-orange-100 text-orange-800 border-orange-200";
      case MeetingType.STAKEHOLDER:
        return "bg-green-100 text-green-800 border-green-200";
      case MeetingType.REVIEW:
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  if (!lang) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[var(--brand-blue)]"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--brand-blue)]/5 via-white to-[var(--brand-blue)]/10">
      {/* Header */}
      <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 text-white">
        <div className="container mx-auto px-6 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center">
                <CalendarDays className="w-8 h-8" />
              </div>
            </div>
            
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {isRTL ? "تقويم الاجتماعات" : "Meetings Calendar"}
            </h1>
            
            <p className="text-xl text-white/90 max-w-3xl mx-auto mb-8">
              {isRTL 
                ? "إدارة شاملة للاجتماعات مع تتبع نقاط العمل والمهام المرتبطة"
                : "Comprehensive meeting management with action points tracking and linked tasks"
              }
            </p>

            <Button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-white text-[var(--brand-blue)] hover:bg-white/90 font-semibold px-8 py-3 rounded-xl"
            >
              <Plus className="w-5 h-5 mr-2" />
              {isRTL ? "إضافة اجتماع جديد" : "Add New Meeting"}
            </Button>
          </motion.div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="shadow-lg border-0">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <CalendarDays className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    {isRTL ? "إجمالي الاجتماعات" : "Total Meetings"}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">{statistics.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    {isRTL ? "مكتملة" : "Completed"}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">{statistics.completed}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                  <Clock className="w-6 h-6 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    {isRTL ? "هذا الشهر" : "This Month"}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">{statistics.thisMonth}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                  <AlertCircle className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    {isRTL ? "جارية" : "In Progress"}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">{statistics.inProgress}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="shadow-lg border-0 mb-8">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-gray-900 flex items-center gap-2">
              <Filter className="w-5 h-5 text-[var(--brand-blue)]" />
              {isRTL ? "البحث والتصفية" : "Search & Filter"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder={isRTL ? "البحث في الاجتماعات..." : "Search meetings..."}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={isRTL ? "الحالة" : "Status"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{isRTL ? "جميع الحالات" : "All Statuses"}</SelectItem>
                  <SelectItem value="scheduled">{isRTL ? "مجدولة" : "Scheduled"}</SelectItem>
                  <SelectItem value="in_progress">{isRTL ? "جارية" : "In Progress"}</SelectItem>
                  <SelectItem value="completed">{isRTL ? "مكتملة" : "Completed"}</SelectItem>
                  <SelectItem value="cancelled">{isRTL ? "ملغية" : "Cancelled"}</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={isRTL ? "النوع" : "Type"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{isRTL ? "جميع الأنواع" : "All Types"}</SelectItem>
                  <SelectItem value="internal">{isRTL ? "داخلي" : "Internal"}</SelectItem>
                  <SelectItem value="client">{isRTL ? "عميل" : "Client"}</SelectItem>
                  <SelectItem value="vendor">{isRTL ? "مورد" : "Vendor"}</SelectItem>
                  <SelectItem value="stakeholder">{isRTL ? "أصحاب مصلحة" : "Stakeholder"}</SelectItem>
                  <SelectItem value="review">{isRTL ? "مراجعة" : "Review"}</SelectItem>
                  <SelectItem value="other">{isRTL ? "أخرى" : "Other"}</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span>{isRTL ? "النتائج:" : "Results:"}</span>
                <span className="font-semibold">{filteredMeetings.length}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Meetings List */}
        <Card className="shadow-lg border-0">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-gray-900">
              {isRTL ? "الاجتماعات" : "Meetings"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--brand-blue)]"></div>
              </div>
            ) : filteredMeetings.length === 0 ? (
              <div className="text-center py-12">
                <CalendarDays className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {isRTL ? "لا توجد اجتماعات" : "No meetings found"}
                </h3>
                <p className="text-gray-600 mb-4">
                  {isRTL
                    ? "لم يتم العثور على اجتماعات تطابق معايير البحث"
                    : "No meetings match your search criteria"
                  }
                </p>
                <Button onClick={() => setIsAddModalOpen(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  {isRTL ? "إضافة اجتماع جديد" : "Add New Meeting"}
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredMeetings.map((meeting) => (
                  <motion.div
                    key={meeting.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white border border-gray-200 rounded-2xl p-6 hover:border-[var(--brand-blue)]/40 hover:shadow-lg transition-all duration-300 group"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        {/* Header with title and badges */}
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <div className="w-10 h-10 bg-gradient-to-br from-[var(--brand-blue)] to-[var(--brand-blue)]/80 rounded-xl flex items-center justify-center">
                                <CalendarDays className="w-5 h-5 text-white" />
                              </div>
                              <div>
                                <h3 className="text-xl font-bold text-gray-900 group-hover:text-[var(--brand-blue)] transition-colors">
                                  {meeting.title}
                                </h3>
                                <p className="text-sm text-gray-500">
                                  {isRTL ? "منظم بواسطة" : "Organized by"} {meeting.organizer.name}
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className={`${getStatusColor(meeting.status)} font-medium`}>
                              {meeting.status}
                            </Badge>
                            <Badge variant="outline" className={`${getTypeColor(meeting.meetingType)} font-medium`}>
                              {meeting.meetingType}
                            </Badge>
                          </div>
                        </div>

                        {/* Description */}
                        {meeting.description && (
                          <div className="mb-4">
                            <p className="text-gray-600 text-sm leading-relaxed line-clamp-2">
                              {meeting.description}
                            </p>
                          </div>
                        )}

                        {/* Meeting details grid */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                          <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                            <CalendarIcon className="w-4 h-4 text-[var(--brand-blue)]" />
                            <div>
                              <p className="font-medium text-gray-900">
                                {meeting.meetingDate.toDate().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
                                  month: 'short',
                                  day: 'numeric'
                                })}
                              </p>
                              <p className="text-xs text-gray-500">
                                {meeting.meetingDate.toDate().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
                                  weekday: 'short'
                                })}
                              </p>
                            </div>
                          </div>

                          <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                            <Clock className="w-4 h-4 text-[var(--brand-blue)]" />
                            <div>
                              <p className="font-medium text-gray-900">
                                {meeting.meetingDate.toDate().toLocaleTimeString(isRTL ? 'ar-SA' : 'en-US', {
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </p>
                              <p className="text-xs text-gray-500">
                                {meeting.duration} {isRTL ? 'دقيقة' : 'min'}
                              </p>
                            </div>
                          </div>

                          <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                            <Users className="w-4 h-4 text-[var(--brand-blue)]" />
                            <div>
                              <p className="font-medium text-gray-900">{meeting.attendees.length}</p>
                              <p className="text-xs text-gray-500">
                                {isRTL ? 'مشارك' : 'attendees'}
                              </p>
                            </div>
                          </div>

                          {meeting.location ? (
                            <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                              <MapPin className="w-4 h-4 text-[var(--brand-blue)]" />
                              <div>
                                <p className="font-medium text-gray-900 truncate">{meeting.location}</p>
                                <p className="text-xs text-gray-500">{isRTL ? 'الموقع' : 'Location'}</p>
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                              <FileText className="w-4 h-4 text-[var(--brand-blue)]" />
                              <div>
                                <p className="font-medium text-gray-900">{meeting.agenda?.length || 0}</p>
                                <p className="text-xs text-gray-500">
                                  {isRTL ? 'بند جدول أعمال' : 'agenda items'}
                                </p>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Attendees preview */}
                        <div className="flex items-center gap-3 mb-4">
                          <span className="text-sm font-medium text-gray-700">
                            {isRTL ? "المشاركون:" : "Attendees:"}
                          </span>
                          <div className="flex items-center gap-2">
                            {meeting.attendees.slice(0, 3).map((attendee, index) => (
                              <div
                                key={index}
                                className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-medium ${
                                  attendee.isConsultant ? 'bg-[var(--brand-blue)]' : 'bg-gray-500'
                                }`}
                                title={attendee.name}
                              >
                                {attendee.name.charAt(0).toUpperCase()}
                              </div>
                            ))}
                            {meeting.attendees.length > 3 && (
                              <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 text-xs font-medium">
                                +{meeting.attendees.length - 3}
                              </div>
                            )}
                            <div className="ml-2">
                              {meeting.attendees.filter(a => a.isConsultant).length > 0 && (
                                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                                  {meeting.attendees.filter(a => a.isConsultant).length} {isRTL ? 'استشاري' : 'consultants'}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Action buttons */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <span>{isRTL ? "تم الإنشاء في" : "Created"} {meeting.createdAt.toDate().toLocaleDateString()}</span>
                        {meeting.summary && (
                          <>
                            <span>•</span>
                            <span className="text-green-600">{isRTL ? "يحتوي على ملخص" : "Has summary"}</span>
                          </>
                        )}
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewMeeting(meeting)}
                          className="bg-[var(--brand-blue)]/5 text-[var(--brand-blue)] border-[var(--brand-blue)]/20 hover:bg-[var(--brand-blue)]/10"
                        >
                          <Eye className="w-4 h-4 mr-2" />
                          {isRTL ? "عرض التفاصيل" : "View Details"}
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteMeeting(meeting.id!, meeting.title);
                          }}
                          disabled={deletingMeetingId === meeting.id}
                          className="bg-red-50 text-red-600 border-red-200 hover:bg-red-100"
                        >
                          {deletingMeetingId === meeting.id ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600" />
                          ) : (
                            <Trash2 className="w-4 h-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Add Meeting Modal */}
      <AddMeetingModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSave={handleCreateMeeting}
        isRTL={isRTL}
      />

      {/* Meeting Details Modal */}
      {selectedMeeting && (
        <MeetingDetailsModal
          isOpen={isDetailsModalOpen}
          onClose={() => {
            setIsDetailsModalOpen(false);
            setSelectedMeeting(null);
          }}
          meeting={selectedMeeting}
          onUpdate={loadMeetings}
          isRTL={isRTL}
        />
      )}
    </div>
  );
}
