"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, ArrowLef<PERSON>, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { LanguageSelector } from "./LanguageSelector";
import { PrivacyPolicyInput } from "./PrivacyPolicyInput";
import { SystemsService } from "@/Firebase/firestore/SystemsService";

interface PrivacyPolicyModalProps {
  isOpen: boolean;
  onClose: () => void;
  systemId: string;
  systemName: string;
  isRTL: boolean;
}

type ModalStep = 'language' | 'input';

export function PrivacyPolicyModal({ isOpen, onClose, systemId, systemName, isRTL }: PrivacyPolicyModalProps) {
  const [currentStep, setCurrentStep] = useState<ModalStep>('language');
  const [selectedLanguage, setSelectedLanguage] = useState<'en' | 'ar'>('en');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleLanguageSelect = (language: 'en' | 'ar') => {
    setSelectedLanguage(language);
  };

  const handleNext = () => {
    setCurrentStep('input');
  };

  const handleBack = () => {
    setCurrentStep('language');
  };

  const handleSavePrivacyPolicy = async (content: string) => {
    try {
      setIsLoading(true);

      await SystemsService.addSystemPrivacyPolicy(systemId, {
        content,
        language: selectedLanguage,
      });

      toast({
        title: isRTL ? "تم الحفظ بنجاح" : "Successfully Saved",
        description: isRTL 
          ? `تم حفظ سياسة الخصوصية باللغة ${selectedLanguage === 'ar' ? 'العربية' : 'الإنجليزية'}`
          : `Privacy policy saved in ${selectedLanguage === 'ar' ? 'Arabic' : 'English'}`,
      });

      // Reset modal state
      setCurrentStep('language');
      setSelectedLanguage('en');
      onClose();
    } catch (error) {
      console.error('Error saving privacy policy:', error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: isRTL 
          ? "فشل في حفظ سياسة الخصوصية. يرجى المحاولة مرة أخرى."
          : "Failed to save privacy policy. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setCurrentStep('language');
      setSelectedLanguage('en');
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col p-0">
        {/* Header */}
        <DialogHeader className="flex-shrink-0 px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-[var(--brand-blue)]/5 to-[var(--brand-blue)]/10">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl font-bold text-gray-900">
                {isRTL ? "إضافة سياسة الخصوصية" : "Add Privacy Policy"}
              </DialogTitle>
              <p className="text-sm text-gray-600 mt-1">
                {systemName}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              disabled={isLoading}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* Step Indicator */}
          <div className="flex items-center gap-4 mt-4">
            <div className="flex items-center gap-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep === 'language' 
                  ? 'bg-[var(--brand-blue)] text-white' 
                  : 'bg-[var(--brand-blue)]/20 text-[var(--brand-blue)]'
              }`}>
                1
              </div>
              <span className={`text-sm font-medium ${
                currentStep === 'language' ? 'text-[var(--brand-blue)]' : 'text-gray-500'
              }`}>
                {isRTL ? "اختيار اللغة" : "Language"}
              </span>
            </div>
            
            <div className={`w-8 h-0.5 ${
              currentStep === 'input' ? 'bg-[var(--brand-blue)]' : 'bg-gray-300'
            }`}></div>
            
            <div className="flex items-center gap-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep === 'input' 
                  ? 'bg-[var(--brand-blue)] text-white' 
                  : 'bg-gray-200 text-gray-500'
              }`}>
                2
              </div>
              <span className={`text-sm font-medium ${
                currentStep === 'input' ? 'text-[var(--brand-blue)]' : 'text-gray-500'
              }`}>
                {isRTL ? "كتابة المحتوى" : "Content"}
              </span>
            </div>
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-6">
            <AnimatePresence mode="wait">
              {currentStep === 'language' && (
                <motion.div
                  key="language"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <LanguageSelector
                    selectedLanguage={selectedLanguage}
                    onLanguageSelect={handleLanguageSelect}
                    isRTL={isRTL}
                  />
                </motion.div>
              )}

              {currentStep === 'input' && (
                <motion.div
                  key="input"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                >
                  <PrivacyPolicyInput
                    selectedLanguage={selectedLanguage}
                    onSave={handleSavePrivacyPolicy}
                    isLoading={isLoading}
                    isRTL={isRTL}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div>
              {currentStep === 'input' && (
                <Button
                  variant="outline"
                  onClick={handleBack}
                  disabled={isLoading}
                  className="flex items-center gap-2"
                >
                  {isRTL ? (
                    <ArrowRight className="w-4 h-4" />
                  ) : (
                    <ArrowLeft className="w-4 h-4" />
                  )}
                  {isRTL ? "رجوع" : "Back"}
                </Button>
              )}
            </div>
            
            <div>
              {currentStep === 'language' && (
                <Button
                  onClick={handleNext}
                  className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white flex items-center gap-2"
                >
                  {isRTL ? "التالي" : "Next"}
                  {isRTL ? (
                    <ArrowLeft className="w-4 h-4" />
                  ) : (
                    <ArrowRight className="w-4 h-4" />
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
