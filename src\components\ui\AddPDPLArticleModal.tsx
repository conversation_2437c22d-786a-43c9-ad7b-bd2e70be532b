"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Save, Plus, Trash2, Scale, FileText, Gavel, ChevronDown, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { PDPLDocumentType, PDPLPoint, PDPLSubPoint } from "@/Firebase/firestore/services/PDPLService";
import { Locale } from "@/i18n-config";

interface AddPDPLArticleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (articleData: {
    articleNumber: string;
    title: string;
    documentType: PDPLDocumentType;
    points: PDPLPoint[];
    linkedArticles: string[];
  }) => Promise<void>;
  lang: Locale;
  isLoading: boolean;
  documentType: PDPLDocumentType;
  availableArticles?: Array<{id: string; articleNumber: string; title: string;}>;
}

export function AddPDPLArticleModal({
  isOpen,
  onClose,
  onSubmit,
  lang,
  isLoading,
  documentType,
  availableArticles = []
}: AddPDPLArticleModalProps) {
  const isRTL = lang === "ar";
  
  const [formData, setFormData] = useState({
    articleNumber: "",
    title: "",
    points: [] as PDPLPoint[],
    linkedArticles: [] as string[]
  });

  const [expandedPoints, setExpandedPoints] = useState<Set<string>>(new Set());

  const documentTypeLabels = {
    [PDPLDocumentType.PDPL_LAW]: {
      en: "Personal Data Protection Law",
      ar: "قانون حماية البيانات الشخصية",
      icon: Scale
    },
    [PDPLDocumentType.IMPLEMENTING_REGULATION]: {
      en: "Implementing Regulation",
      ar: "اللائحة التنفيذية",
      icon: FileText
    },
    [PDPLDocumentType.TRANSFER_RULES]: {
      en: "Data Transfer Outside Kingdom",
      ar: "نقل البيانات خارج المملكة",
      icon: Gavel
    }
  };

  const currentTypeInfo = documentTypeLabels[documentType];
  const IconComponent = currentTypeInfo.icon;

  const handleClose = () => {
    setFormData({
      articleNumber: "",
      title: "",
      points: [],
      linkedArticles: []
    });
    setExpandedPoints(new Set());
    onClose();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.articleNumber.trim() || !formData.title.trim()) {
      return;
    }

    try {
      await onSubmit({
        ...formData,
        documentType
      });

      // Only close if submission was successful
      handleClose();
    } catch (error) {
      // Don't close modal on error - let the parent handle error display
      console.error('Error submitting article:', error);
    }
  };

  const addPoint = () => {
    const newPoint: PDPLPoint = {
      id: Date.now().toString(),
      content: "",
      order: formData.points.length + 1,
      subPoints: []
    };
    setFormData(prev => ({
      ...prev,
      points: [...prev.points, newPoint]
    }));
  };

  const updatePoint = (pointId: string, content: string) => {
    setFormData(prev => ({
      ...prev,
      points: prev.points.map(point =>
        point.id === pointId ? { ...point, content } : point
      )
    }));
  };

  const removePoint = (pointId: string) => {
    setFormData(prev => ({
      ...prev,
      points: prev.points.filter(point => point.id !== pointId)
    }));
    setExpandedPoints(prev => {
      const newSet = new Set(prev);
      newSet.delete(pointId);
      return newSet;
    });
  };

  const addSubPoint = (pointId: string) => {
    const newSubPoint: PDPLSubPoint = {
      id: Date.now().toString(),
      content: "",
      order: 1
    };

    setFormData(prev => ({
      ...prev,
      points: prev.points.map(point =>
        point.id === pointId
          ? {
              ...point,
              subPoints: [...(point.subPoints || []), newSubPoint]
            }
          : point
      )
    }));

    // Auto-expand the point when adding a sub-point
    setExpandedPoints(prev => new Set([...prev, pointId]));
  };

  const updateSubPoint = (pointId: string, subPointId: string, content: string) => {
    setFormData(prev => ({
      ...prev,
      points: prev.points.map(point =>
        point.id === pointId
          ? {
              ...point,
              subPoints: point.subPoints?.map(subPoint =>
                subPoint.id === subPointId ? { ...subPoint, content } : subPoint
              )
            }
          : point
      )
    }));
  };

  const removeSubPoint = (pointId: string, subPointId: string) => {
    setFormData(prev => ({
      ...prev,
      points: prev.points.map(point =>
        point.id === pointId
          ? {
              ...point,
              subPoints: point.subPoints?.filter(subPoint => subPoint.id !== subPointId)
            }
          : point
      )
    }));
  };

  const togglePointExpansion = (pointId: string) => {
    setExpandedPoints(prev => {
      const newSet = new Set(prev);
      if (newSet.has(pointId)) {
        newSet.delete(pointId);
      } else {
        newSet.add(pointId);
      }
      return newSet;
    });
  };

  const toggleLinkedArticle = (articleId: string) => {
    setFormData(prev => ({
      ...prev,
      linkedArticles: prev.linkedArticles.includes(articleId)
        ? prev.linkedArticles.filter(id => id !== articleId)
        : [...prev.linkedArticles, articleId]
    }));
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/20 backdrop-blur-sm"
          onClick={handleClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.3 }}
          className="relative bg-white rounded-3xl shadow-2xl border border-gray-200 w-full max-w-4xl max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 text-white p-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center">
                  <IconComponent className="w-6 h-6" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold">
                    {isRTL ? "إضافة مادة جديدة" : "Add New Article"}
                  </h2>
                  <p className="text-white/90 text-sm">
                    {isRTL ? currentTypeInfo.ar : currentTypeInfo.en}
                  </p>
                </div>
              </div>
              <Button
                onClick={handleClose}
                variant="ghost"
                size="icon"
                className="text-white hover:bg-white/20 rounded-xl"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="p-8 overflow-y-auto max-h-[calc(90vh-200px)]">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    {isRTL ? "رقم المادة" : "Article Number"}
                    <span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.articleNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, articleNumber: e.target.value }))}
                    placeholder={isRTL ? "مثال: المادة 1" : "e.g., Article 1"}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-[var(--brand-blue)] transition-all duration-200"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    {isRTL ? "عنوان المادة" : "Article Title"}
                    <span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder={isRTL ? "عنوان المادة" : "Article title"}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-[var(--brand-blue)] transition-all duration-200"
                    required
                  />
                </div>
              </div>

              {/* Points */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <label className="block text-sm font-semibold text-gray-700">
                    {isRTL ? "النقاط" : "Points"}
                  </label>
                  <Button
                    type="button"
                    onClick={addPoint}
                    variant="outline"
                    size="sm"
                    className="border-[var(--brand-blue)] text-[var(--brand-blue)] hover:bg-[var(--brand-blue)] hover:text-white"
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    {isRTL ? "إضافة نقطة" : "Add Point"}
                  </Button>
                </div>

                <div className="space-y-4">
                  {formData.points.map((point, index) => (
                    <div key={point.id} className="bg-gray-50 rounded-xl p-4 border border-gray-200">
                      <div className="flex items-start gap-3">
                        <span className="flex-shrink-0 w-6 h-6 bg-[var(--brand-blue)] text-white text-xs font-bold rounded-full flex items-center justify-center mt-2">
                          {index + 1}
                        </span>
                        <div className="flex-1">
                          <input
                            type="text"
                            value={point.content}
                            onChange={(e) => updatePoint(point.id, e.target.value)}
                            placeholder={isRTL ? "محتوى النقطة" : "Point content"}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-[var(--brand-blue)] text-sm"
                          />
                          
                          {/* Sub-points */}
                          {(point.subPoints && point.subPoints.length > 0) && (
                            <div className="mt-3">
                              <button
                                type="button"
                                onClick={() => togglePointExpansion(point.id)}
                                className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 mb-2"
                              >
                                {expandedPoints.has(point.id) ? (
                                  <ChevronDown className="w-4 h-4" />
                                ) : (
                                  <ChevronRight className="w-4 h-4" />
                                )}
                                {isRTL ? `النقاط الفرعية (${point.subPoints.length})` : `Sub-points (${point.subPoints.length})`}
                              </button>
                              
                              {expandedPoints.has(point.id) && (
                                <div className="space-y-2 ml-4 pl-4 border-l-2 border-gray-300">
                                  {point.subPoints.map((subPoint, subIndex) => (
                                    <div key={subPoint.id} className="flex items-center gap-2">
                                      <span className="flex-shrink-0 w-5 h-5 bg-gray-400 text-white text-xs font-bold rounded-full flex items-center justify-center">
                                        {String.fromCharCode(97 + subIndex)}
                                      </span>
                                      <input
                                        type="text"
                                        value={subPoint.content}
                                        onChange={(e) => updateSubPoint(point.id, subPoint.id, e.target.value)}
                                        placeholder={isRTL ? "محتوى النقطة الفرعية" : "Sub-point content"}
                                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-[var(--brand-blue)] text-sm"
                                      />
                                      <Button
                                        type="button"
                                        onClick={() => removeSubPoint(point.id, subPoint.id)}
                                        variant="ghost"
                                        size="sm"
                                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                                      >
                                        <Trash2 className="w-4 h-4" />
                                      </Button>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          )}

                          <div className="flex gap-2 mt-3">
                            <Button
                              type="button"
                              onClick={() => addSubPoint(point.id)}
                              variant="ghost"
                              size="sm"
                              className="text-[var(--brand-blue)] hover:text-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/10"
                            >
                              <Plus className="w-4 h-4 mr-1" />
                              {isRTL ? "نقطة فرعية" : "Sub-point"}
                            </Button>
                          </div>
                        </div>
                        <Button
                          type="button"
                          onClick={() => removePoint(point.id)}
                          variant="ghost"
                          size="sm"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Linked Articles (only for PDPL_LAW and IMPLEMENTING_REGULATION) */}
              {(documentType === PDPLDocumentType.PDPL_LAW || documentType === PDPLDocumentType.IMPLEMENTING_REGULATION) && availableArticles.length > 0 && (
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-4">
                    {isRTL ? "ربط المواد" : "Link Articles"}
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-40 overflow-y-auto">
                    {availableArticles.map((article) => (
                      <label key={article.id} className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.linkedArticles.includes(article.id)}
                          onChange={() => toggleLinkedArticle(article.id)}
                          className="w-4 h-4 text-[var(--brand-blue)] focus:ring-[var(--brand-blue)] border-gray-300 rounded"
                        />
                        <div>
                          <div className="font-medium text-sm">{article.articleNumber}</div>
                          <div className="text-xs text-gray-600 truncate">{article.title}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </form>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-8 py-6 border-t border-gray-200 flex gap-4 justify-end">
            <Button
              type="button"
              onClick={handleClose}
              variant="outline"
              disabled={isLoading}
              className="px-6 py-3 rounded-xl"
            >
              {isRTL ? "إلغاء" : "Cancel"}
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isLoading || !formData.articleNumber.trim() || !formData.title.trim()}
              className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  {isRTL ? "جاري الحفظ..." : "Saving..."}
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Save className="w-4 h-4" />
                  {isRTL ? "حفظ المادة" : "Save Article"}
                </div>
              )}
            </Button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
} 