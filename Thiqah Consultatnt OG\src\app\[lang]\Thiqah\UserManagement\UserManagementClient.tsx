"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  Users,
  Shield,
  Edit,
  Save,
  X,
  Plus,
  Trash2,
  UserCheck,
  UserX,
  Search,
  Filter,
  MoreVertical
} from "lucide-react";
import { auth } from "@/Firebase/Authentication/authConfig";
import {
  UserRole,
  UserProfile
} from "@/Firebase/firestore/services/UserService";
import { useToast } from "@/components/ui/use-toast";
import { Locale } from "@/i18n-config";
import { Dictionary } from "@/dictionaries";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  Di<PERSON><PERSON>ooter,
  Di<PERSON><PERSON>eader,
  Di<PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface UserManagementClientProps {
  lang: Locale;
  dict: Dictionary;
}

interface NewUserForm {
  email: string;
  displayName: string;
  role: UserRole;
  password: string;
}

interface ErrorWithMessage {
  message?: string;
}

export default function UserManagementClient({ lang }: UserManagementClientProps) {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [editingUser, setEditingUser] = useState<string | null>(null);
  const [newRole, setNewRole] = useState<UserRole | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState<string | null>(null);
  const [newUserForm, setNewUserForm] = useState<NewUserForm>({
    email: "",
    displayName: "",
    role: UserRole.CLIENT,
    password: ""
  });
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const { toast } = useToast();
  const router = useRouter();
  const isRTL = lang === 'ar';

  // API Functions - For now, use direct service calls until API auth is properly set up
  const fetchUsers = async () => {
    try {
      // Import the service directly for now
      const { getAllUsers } = await import('@/Firebase/firestore/services/UserService');
      return await getAllUsers();
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  };

  const createUser = async (userData: NewUserForm) => {
    try {
      // Import services directly for now
      const { registerWithEmailAndPassword } = await import('@/Firebase/Authentication/authConfig');
      const { upsertUserProfile } = await import('@/Firebase/firestore/services/UserService');

      // Create user in Firebase Auth
      const newUser = await registerWithEmailAndPassword(userData.email, userData.password);

      // Create user profile in Firestore with specified role
      await upsertUserProfile(newUser.uid, {
        email: userData.email,
        displayName: userData.displayName,
        photoURL: null,
        role: userData.role
      });

      return { user: newUser };
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  };

  const updateUserRoleLocal = async (uid: string, role: UserRole) => {
    try {
      const { updateUserRole } = await import('@/Firebase/firestore/services/UserService');
      await updateUserRole(uid, role);
      return { success: true };
    } catch (error) {
      console.error('Error updating user role:', error);
      throw error;
    }
  };

  const toggleUserStatus = async (uid: string, disabled: boolean) => {
    try {
      const { firestore } = await import('@/Firebase/firestore/firestoreConfig');
      const { doc, updateDoc } = await import('firebase/firestore');

      const userRef = doc(firestore, 'users', uid);
      await updateDoc(userRef, { disabled });

      return { success: true };
    } catch (error) {
      console.error('Error updating user status:', error);
      throw error;
    }
  };

  const deleteUser = async (uid: string) => {
    try {
      const { firestore } = await import('@/Firebase/firestore/firestoreConfig');
      const { doc, deleteDoc } = await import('firebase/firestore');

      const userRef = doc(firestore, 'users', uid);
      await deleteDoc(userRef);

      return { success: true };
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  };

  const loadUsers = useCallback(async () => {
    try {
      const allUsers = await fetchUsers();
      setUsers(allUsers);
      setFilteredUsers(allUsers);
    } catch (error) {
      console.error("Error loading users:", error);
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: isRTL ? "فشل في تحميل المستخدمين" : "Failed to load users",
        variant: "destructive",
      });
    }
  }, [isRTL, toast]);

  useEffect(() => {
    const checkAccess = async () => {
      setLoading(true);
      try {
        const currentUser = auth.currentUser;
        if (!currentUser) {
          router.push(`/${lang}/auth/login`);
          return;
        }

        const { getUserProfile } = await import('@/Firebase/firestore/services/UserService');
        const userProfile = await getUserProfile(currentUser.uid);
        if (!userProfile || userProfile.role !== UserRole.CONSULTANT) {
          toast({
            title: isRTL ? "غير مصرح" : "Unauthorized",
            description: isRTL ? "ليس لديك صلاحية للوصول إلى هذه الصفحة" : "You don't have permission to access this page",
            variant: "destructive",
          });
          router.push(`/${lang}/Thiqah`);
          return;
        }

        await loadUsers();
      } catch (error) {
        console.error("Error checking access:", error);
        toast({
          title: isRTL ? "خطأ" : "Error",
          description: isRTL ? "حدث خطأ أثناء التحقق من الصلاحيات" : "An error occurred while checking permissions",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    checkAccess();
  }, [lang, router, toast, isRTL, loadUsers]);

  // Filter and search functionality
  useEffect(() => {
    let filtered = users;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.displayName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply role filter
    if (roleFilter !== "all") {
      filtered = filtered.filter(user => user.role === roleFilter);
    }

    setFilteredUsers(filtered);
  }, [users, searchTerm, roleFilter]);

  // Handler functions
  const handleRoleUpdate = async (uid: string) => {
    if (!newRole) return;

    try {
      setActionLoading(uid);
      await updateUserRoleLocal(uid, newRole);
      await loadUsers();
      setEditingUser(null);
      setNewRole(null);

      toast({
        title: isRTL ? "تم التحديث" : "Updated",
        description: isRTL ? "تم تحديث دور المستخدم بنجاح" : "User role updated successfully",
        variant: "default",
      });
    } catch (error: unknown) {
      console.error("Error updating role:", error);
      const errorMessage = (error as ErrorWithMessage)?.message || (isRTL ? "فشل في تحديث دور المستخدم" : "Failed to update user role");
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleCreateUser = async () => {
    try {
      setActionLoading('create');
      await createUser(newUserForm);
      await loadUsers();
      setShowAddDialog(false);
      setNewUserForm({
        email: "",
        displayName: "",
        role: UserRole.CLIENT,
        password: ""
      });

      toast({
        title: isRTL ? "تم الإنشاء" : "Created",
        description: isRTL ? "تم إنشاء المستخدم بنجاح" : "User created successfully",
        variant: "default",
      });
    } catch (error: unknown) {
      console.error("Error creating user:", error);
      const errorMessage = (error as ErrorWithMessage)?.message || (isRTL ? "فشل في إنشاء المستخدم" : "Failed to create user");
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleToggleUserStatus = async (uid: string, disabled: boolean) => {
    try {
      setActionLoading(uid);
      await toggleUserStatus(uid, disabled);
      await loadUsers();

      toast({
        title: isRTL ? "تم التحديث" : "Updated",
        description: disabled
          ? (isRTL ? "تم تعطيل المستخدم" : "User disabled")
          : (isRTL ? "تم تفعيل المستخدم" : "User enabled"),
        variant: "default",
      });
    } catch (error: unknown) {
      console.error("Error toggling user status:", error);
      const errorMessage = (error as ErrorWithMessage)?.message || (isRTL ? "فشل في تحديث حالة المستخدم" : "Failed to update user status");
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteUser = async (uid: string) => {
    try {
      setActionLoading(uid);
      await deleteUser(uid);
      await loadUsers();
      setShowDeleteDialog(null);

      toast({
        title: isRTL ? "تم الحذف" : "Deleted",
        description: isRTL ? "تم حذف المستخدم بنجاح" : "User deleted successfully",
        variant: "default",
      });
    } catch (error: unknown) {
      console.error("Error deleting user:", error);
      const errorMessage = (error as ErrorWithMessage)?.message || (isRTL ? "فشل في حذف المستخدم" : "Failed to delete user");
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
    }
  };

  // Utility functions
  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case UserRole.CONSULTANT:
        return "text-purple-200 border-purple-300/50";
      case UserRole.CLIENT:
        return "text-blue-200 border-blue-300/50";
      case UserRole.VIEWER:
        return "text-gray-200 border-gray-300/50";
      default:
        return "text-gray-200 border-gray-300/50";
    }
  };

  const getRoleDisplayName = (role: UserRole) => {
    switch (role) {
      case UserRole.CONSULTANT:
        return isRTL ? "مستشار" : "Consultant";
      case UserRole.CLIENT:
        return isRTL ? "عميل" : "Client";
      case UserRole.VIEWER:
        return isRTL ? "مشاهد" : "Viewer";
      default:
        return role;
    }
  };

  const getStatusBadgeColor = (disabled?: boolean) => {
    return disabled
      ? "text-red-200 border-red-300/50"
      : "text-green-200 border-green-300/50";
  };

  const getStatusDisplayName = (disabled?: boolean) => {
    return disabled
      ? (isRTL ? "معطل" : "Disabled")
      : (isRTL ? "نشط" : "Active");
  };

  if (loading) {
    return (
      <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
        <div className="relative min-h-screen bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
          <div className="relative z-10 flex flex-col justify-center items-center min-h-screen px-8 py-16">
            <div className="animate-pulse text-center">
              <div className="w-24 h-24 bg-white/20 rounded-3xl mx-auto mb-8"></div>
              <div className="h-12 bg-white/20 rounded-lg w-96 mx-auto mb-4"></div>
              <div className="h-6 bg-white/20 rounded-lg w-64 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Enhanced Hero Section with Glassmorphism */}
      <div className="relative min-h-screen bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
          <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-white rounded-full opacity-50"></div>
        </div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, 20, -20],
                opacity: [0.3, 0.8, 0.3],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        <div className="relative z-20 flex flex-col min-h-screen px-8 py-16">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16 pt-16"
          >
            <div className="flex items-center justify-center gap-6 mb-8">
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="w-24 h-24 bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-xl rounded-3xl flex items-center justify-center shadow-2xl border border-white/20"
              >
                <Users className="w-12 h-12 text-white" />
              </motion.div>
              <div className="text-left">
                <motion.h1
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="text-5xl md:text-7xl font-bold text-white mb-2 tracking-tight"
                >
                  {isRTL ? "إدارة المستخدمين" : "User Management"}
                </motion.h1>
                <motion.p
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="text-xl md:text-2xl text-white/90 font-medium"
                >
                  {isRTL ? "إدارة أدوار المستخدمين وصلاحياتهم" : "Manage user roles and permissions"}
                </motion.p>
              </div>
            </div>

            {/* Stats Cards */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto mb-16"
            >
              {[
                {
                  icon: Users,
                  label: isRTL ? "إجمالي المستخدمين" : "Total Users",
                  value: users.length.toString(),
                  color: "bg-blue-500/20"
                },
                {
                  icon: Shield,
                  label: isRTL ? "المستشارين" : "Consultants",
                  value: users.filter(u => u.role === UserRole.CONSULTANT).length.toString(),
                  color: "bg-purple-500/20"
                },
                {
                  icon: UserCheck,
                  label: isRTL ? "العملاء النشطين" : "Active Clients",
                  value: users.filter(u => u.role === UserRole.CLIENT && !u.disabled).length.toString(),
                  color: "bg-green-500/20"
                }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1 + index * 0.1 }}
                  className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300 group"
                >
                  <div className="text-center">
                    <div className="flex justify-center mb-4">
                      <div className={`p-3 rounded-xl ${item.color} text-white group-hover:scale-110 transition-transform duration-300`}>
                        <item.icon className="w-6 h-6" />
                      </div>
                    </div>
                    <h3 className="text-sm font-semibold text-white/80 mb-2">{item.label}</h3>
                    <p className="text-3xl font-bold text-white">{item.value}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Main Content Card */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
            className="flex-1 bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl overflow-hidden"
          >
            {/* Controls Section */}
            <div className="p-8 border-b border-white/10">
              <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between">
                <div className="flex flex-col sm:flex-row gap-4 flex-1">
                  {/* Search */}
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5" />
                    <Input
                      placeholder={isRTL ? "البحث عن المستخدمين..." : "Search users..."}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-12 bg-white/10 border-white/20 text-white placeholder:text-white/60 focus:border-white/40 focus:ring-white/20 rounded-xl h-12"
                    />
                  </div>

                  {/* Role Filter */}
                  <div className="flex items-center gap-3">
                    <Filter className="w-5 h-5 text-white/60" />
                    <Select value={roleFilter} onValueChange={setRoleFilter}>
                      <SelectTrigger className="w-48 bg-white/10 border-white/20 text-white rounded-xl h-12">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">
                          {isRTL ? "جميع الأدوار" : "All Roles"}
                        </SelectItem>
                        {Object.values(UserRole).map((role) => (
                          <SelectItem key={role} value={role}>
                            {getRoleDisplayName(role)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Add User Button */}
                <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
                  <DialogTrigger asChild>
                    <Button className="bg-white/20 hover:bg-white/30 text-white border border-white/30 rounded-xl h-12 px-6 backdrop-blur-sm transition-all duration-300">
                      <Plus className="w-5 h-5 mr-2" />
                      {isRTL ? "إضافة مستخدم" : "Add User"}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>
                        {isRTL ? "إضافة مستخدم جديد" : "Add New User"}
                      </DialogTitle>
                      <DialogDescription>
                        {isRTL ? "أدخل تفاصيل المستخدم الجديد" : "Enter the details for the new user"}
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="email">
                          {isRTL ? "البريد الإلكتروني" : "Email"}
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          value={newUserForm.email}
                          onChange={(e) => setNewUserForm({...newUserForm, email: e.target.value})}
                          placeholder={isRTL ? "أدخل البريد الإلكتروني" : "Enter email"}
                        />
                      </div>
                      <div>
                        <Label htmlFor="displayName">
                          {isRTL ? "الاسم" : "Display Name"}
                        </Label>
                        <Input
                          id="displayName"
                          value={newUserForm.displayName}
                          onChange={(e) => setNewUserForm({...newUserForm, displayName: e.target.value})}
                          placeholder={isRTL ? "أدخل الاسم" : "Enter display name"}
                        />
                      </div>
                      <div>
                        <Label htmlFor="password">
                          {isRTL ? "كلمة المرور" : "Password"}
                        </Label>
                        <Input
                          id="password"
                          type="password"
                          value={newUserForm.password}
                          onChange={(e) => setNewUserForm({...newUserForm, password: e.target.value})}
                          placeholder={isRTL ? "أدخل كلمة المرور" : "Enter password"}
                        />
                      </div>
                      <div>
                        <Label htmlFor="role">
                          {isRTL ? "الدور" : "Role"}
                        </Label>
                        <Select
                          value={newUserForm.role}
                          onValueChange={(value) => setNewUserForm({...newUserForm, role: value as UserRole})}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {Object.values(UserRole).map((role) => (
                              <SelectItem key={role} value={role}>
                                {getRoleDisplayName(role)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => setShowAddDialog(false)}
                        disabled={actionLoading === 'create'}
                      >
                        {isRTL ? "إلغاء" : "Cancel"}
                      </Button>
                      <Button
                        onClick={handleCreateUser}
                        disabled={actionLoading === 'create' || !newUserForm.email || !newUserForm.displayName || !newUserForm.password}
                        className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
                      >
                        {actionLoading === 'create' ? (
                          <div className="flex items-center gap-2">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            {isRTL ? "جاري الإنشاء..." : "Creating..."}
                          </div>
                        ) : (
                          isRTL ? "إنشاء" : "Create"
                        )}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            {/* Users Table */}
            <div className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <Shield className="w-6 h-6 text-white" />
                  <h2 className="text-2xl font-bold text-white">
                    {isRTL ? "قائمة المستخدمين" : "Users List"}
                  </h2>
                  <Badge className="bg-white/20 text-white border-white/30 ml-3">
                    {filteredUsers.length}
                  </Badge>
                </div>
              </div>

              <div className="bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/10 hover:bg-white/5">
                      <TableHead className="font-semibold text-white/90 py-4">
                        {isRTL ? "المستخدم" : "User"}
                      </TableHead>
                      <TableHead className="font-semibold text-white/90 py-4">
                        {isRTL ? "الدور" : "Role"}
                      </TableHead>
                      <TableHead className="font-semibold text-white/90 py-4">
                        {isRTL ? "الحالة" : "Status"}
                      </TableHead>
                      <TableHead className="font-semibold text-white/90 py-4">
                        {isRTL ? "تاريخ الإنشاء" : "Created"}
                      </TableHead>
                      <TableHead className="font-semibold text-white/90 py-4 text-right">
                        {isRTL ? "الإجراءات" : "Actions"}
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map((user) => (
                      <TableRow key={user.uid} className="border-white/10 hover:bg-white/5 transition-colors duration-200">
                        <TableCell className="py-4">
                          <div className="flex items-center gap-4">
                            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-white/20 to-white/10 flex items-center justify-center border border-white/20">
                              <span className="text-white font-semibold text-lg">
                                {user.displayName?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase()}
                              </span>
                            </div>
                            <div>
                              <div className="font-medium text-white text-lg">
                                {user.displayName || user.email}
                              </div>
                              <div className="text-sm text-white/70">{user.email}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          {editingUser === user.uid ? (
                            <div className="flex items-center gap-3">
                              <Select
                                value={newRole || user.role}
                                onValueChange={(value) => setNewRole(value as UserRole)}
                              >
                                <SelectTrigger className="w-40 bg-white/10 border-white/20 text-white">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {Object.values(UserRole).map((role) => (
                                    <SelectItem key={role} value={role}>
                                      {getRoleDisplayName(role)}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <Button
                                size="sm"
                                onClick={() => handleRoleUpdate(user.uid)}
                                disabled={actionLoading === user.uid}
                                className="bg-green-500/20 hover:bg-green-500/30 text-green-300 border border-green-500/30"
                              >
                                {actionLoading === user.uid ? (
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-300"></div>
                                ) : (
                                  <Save className="w-4 h-4" />
                                )}
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  setEditingUser(null);
                                  setNewRole(null);
                                }}
                                disabled={actionLoading === user.uid}
                                className="bg-red-500/20 hover:bg-red-500/30 text-red-300 border border-red-500/30"
                              >
                                <X className="w-4 h-4" />
                              </Button>
                            </div>
                          ) : (
                            <Badge className={`${getRoleBadgeColor(user.role)} bg-white/20 border-white/30`}>
                              {getRoleDisplayName(user.role)}
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="py-4">
                          <Badge className={`${getStatusBadgeColor(user.disabled)} bg-white/20 border-white/30`}>
                            {getStatusDisplayName(user.disabled)}
                          </Badge>
                        </TableCell>
                        <TableCell className="py-4 text-sm text-white/70">
                          {user.createdAt 
                            ? (user.createdAt instanceof Date 
                                ? user.createdAt.toLocaleDateString() 
                                : (user.createdAt as { toDate?: () => Date })?.toDate?.()?.toLocaleDateString() || 'N/A')
                            : 'N/A'}
                        </TableCell>
                        <TableCell className="py-4 text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                disabled={actionLoading === user.uid}
                                className="text-white/70 hover:text-white hover:bg-white/10"
                              >
                                {actionLoading === user.uid ? (
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white/50"></div>
                                ) : (
                                  <MoreVertical className="w-4 h-4" />
                                )}
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>
                                {isRTL ? "الإجراءات" : "Actions"}
                              </DropdownMenuLabel>
                              <DropdownMenuSeparator />

                              {user.uid !== auth.currentUser?.uid && editingUser !== user.uid && (
                                <DropdownMenuItem
                                  onClick={() => {
                                    setEditingUser(user.uid);
                                    setNewRole(user.role);
                                  }}
                                >
                                  <Edit className="w-4 h-4 mr-2" />
                                  {isRTL ? "تعديل الدور" : "Edit Role"}
                                </DropdownMenuItem>
                              )}

                              {user.uid !== auth.currentUser?.uid && (
                                <DropdownMenuItem
                                  onClick={() => handleToggleUserStatus(user.uid, !user.disabled)}
                                >
                                  {user.disabled ? (
                                    <>
                                      <UserCheck className="w-4 h-4 mr-2" />
                                      {isRTL ? "تفعيل" : "Enable"}
                                    </>
                                  ) : (
                                    <>
                                      <UserX className="w-4 h-4 mr-2" />
                                      {isRTL ? "تعطيل" : "Disable"}
                                    </>
                                  )}
                                </DropdownMenuItem>
                              )}

                              {user.uid !== auth.currentUser?.uid && (
                                <DropdownMenuItem
                                  onClick={() => setShowDeleteDialog(user.uid)}
                                  className="text-red-600 focus:text-red-600"
                                >
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  {isRTL ? "حذف" : "Delete"}
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {filteredUsers.length === 0 && (
                  <div className="text-center py-16">
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5 }}
                    >
                      <Users className="w-16 h-16 text-white/40 mx-auto mb-6" />
                      <h3 className="text-xl font-medium text-white mb-3">
                        {isRTL ? "لا توجد مستخدمين" : "No users found"}
                      </h3>
                      <p className="text-white/70">
                        {isRTL ? "لم يتم العثور على مستخدمين مطابقين للبحث" : "No users match your search criteria"}
                      </p>
                    </motion.div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!showDeleteDialog} onOpenChange={() => setShowDeleteDialog(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-red-600">
              {isRTL ? "تأكيد الحذف" : "Confirm Deletion"}
            </DialogTitle>
            <DialogDescription>
              {isRTL
                ? "هل أنت متأكد من حذف هذا المستخدم؟ لا يمكن التراجع عن هذا الإجراء."
                : "Are you sure you want to delete this user? This action cannot be undone."
              }
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(null)}
              disabled={actionLoading === showDeleteDialog}
            >
              {isRTL ? "إلغاء" : "Cancel"}
            </Button>
            <Button
              variant="destructive"
              onClick={() => showDeleteDialog && handleDeleteUser(showDeleteDialog)}
              disabled={actionLoading === showDeleteDialog}
            >
              {actionLoading === showDeleteDialog ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  {isRTL ? "جاري الحذف..." : "Deleting..."}
                </div>
              ) : (
                isRTL ? "حذف" : "Delete"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
