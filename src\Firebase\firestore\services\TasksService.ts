import {
  collection,
  doc,
  addDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  Timestamp,
  where,
  getDoc
} from 'firebase/firestore';
import { firestore as db } from '../firestoreConfig';

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

export interface TaskAssignee {
  uid: string;
  displayName: string;
  email: string;
  role: string;
}

export interface TaskRelatedPerson {
  name: string;
  email: string;
  role?: string;
}

export interface TaskRisk {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  probability: 'low' | 'medium' | 'high';
  mitigation?: string;
  createdBy: string;
  createdByName: string;
  createdAt: Timestamp;
}

export interface TaskFeedbackReply {
  id: string;
  content: string;
  createdBy: string;
  createdByName: string;
  createdAt: Timestamp;
  status?: 'done' | 'not_done';
  statusUpdatedAt?: Timestamp;
  statusUpdatedBy?: string;
  statusUpdatedByName?: string;
}

export interface TaskFeedback {
  id: string;
  content: string;
  createdBy: string;
  createdByName: string;
  createdAt: Timestamp;
  replies: TaskFeedbackReply[];
  isResolved?: boolean;
  resolvedAt?: Timestamp;
  resolvedBy?: string;
  resolvedByName?: string;
}

export interface Task {
  id?: string;
  title: string;
  description: string;
  status: TaskStatus;
  priority: TaskPriority;
  assignees: TaskAssignee[];
  createdBy: string;
  createdByName: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  dueDate?: Timestamp;
  completedAt?: Timestamp;
  tags?: string[];
  progress?: number;
  updates?: TaskUpdate[];
  timeline?: TimelineEntry[];
  relatedPeople?: TaskRelatedPerson[];
  risks?: TaskRisk[];
  outputLink?: string;
  feedback?: TaskFeedback[];
}

export interface TaskUpdate {
  id: string;
  title: string;
  description: string;
  progress?: number;
  createdBy: string;
  createdByName: string;
  createdAt: Timestamp;
}

export interface TimelineEntry {
  id: string;
  type: 'update' | 'status_change' | 'progress' | 'comment';
  title: string;
  description?: string;
  progress?: number;
  oldStatus?: TaskStatus;
  newStatus?: TaskStatus;
  createdBy: string;
  createdByName: string;
  createdAt: Timestamp;
}

const tasksCollection = collection(db, 'tasks');

export class TasksService {
  /**
   * Creates a new task
   */
  static async createTask(taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = Timestamp.now();
    const newTask = {
      ...taskData,
      createdAt: now,
      updatedAt: now,
      updates: [],
      timeline: []
    };

    // Remove undefined fields to prevent Firebase errors
    const cleanedTask = Object.fromEntries(
      Object.entries(newTask).filter(([, value]) => value !== undefined)
    );

    const docRef = await addDoc(tasksCollection, cleanedTask);
    return docRef.id;
  }

  /**
   * Retrieves all tasks ordered by creation date
   */
  static async getTasks(): Promise<Task[]> {
    const q = query(tasksCollection, orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);

    const tasks: Task[] = [];
    querySnapshot.forEach((doc) => {
      tasks.push({
        id: doc.id,
        ...doc.data()
      } as Task);
    });

    return tasks;
  }

  /**
   * Retrieves tasks assigned to a specific user
   */
  static async getTasksForUser(uid: string): Promise<Task[]> {
    const q = query(
      tasksCollection,
      where('assignees', 'array-contains-any', [{ uid }]),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);

    const tasks: Task[] = [];
    querySnapshot.forEach((doc) => {
      tasks.push({
        id: doc.id,
        ...doc.data()
      } as Task);
    });

    return tasks;
  }

  /**
   * Retrieves tasks by status
   */
  static async getTasksByStatus(status: TaskStatus): Promise<Task[]> {
    const q = query(
      tasksCollection,
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);

    const tasks: Task[] = [];
    querySnapshot.forEach((doc) => {
      tasks.push({
        id: doc.id,
        ...doc.data()
      } as Task);
    });

    return tasks;
  }

  /**
   * Updates a task
   */
  static async updateTask(taskId: string, updates: Partial<Task>): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const updateData = {
      ...updates,
      updatedAt: Timestamp.now()
    };

    if (updates.status === TaskStatus.COMPLETED && !updates.completedAt) {
      updateData.completedAt = Timestamp.now();
    }

    // Remove undefined fields to prevent Firebase errors
    const cleanedUpdateData = Object.fromEntries(
      Object.entries(updateData).filter(([, value]) => value !== undefined)
    );

    await updateDoc(taskRef, cleanedUpdateData);
  }

  /**
   * Updates task progress and adds timeline entry
   */
  static async updateTaskProgress(taskId: string, progress: number, createdBy: string, createdByName: string): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);

    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentTimeline = taskData.timeline || [];
    const currentStatus = taskData.status;

    // Determine if status should change based on progress
    let newStatus = currentStatus;
    let statusChanged = false;

    if (progress >= 100 && currentStatus !== TaskStatus.COMPLETED) {
      newStatus = TaskStatus.COMPLETED;
      statusChanged = true;
    } else if (progress > 0 && progress < 100 && currentStatus === TaskStatus.PENDING) {
      newStatus = TaskStatus.IN_PROGRESS;
      statusChanged = true;
    }

    const newTimelineEntry: TimelineEntry = {
      id: Date.now().toString(),
      type: 'progress',
      title: `Progress updated to ${progress}%`,
      progress,
      createdBy,
      createdByName,
      createdAt: Timestamp.now()
    };

    // Add status change timeline entry if status changed
    const timelineEntries = [newTimelineEntry];
    if (statusChanged) {
      const statusChangeEntry: TimelineEntry = {
        id: (Date.now() + 1).toString(),
        type: 'status_change',
        title: `Status changed to ${newStatus}`,
        oldStatus: currentStatus,
        newStatus: newStatus,
        createdBy,
        createdByName,
        createdAt: Timestamp.now()
      };
      timelineEntries.push(statusChangeEntry);
    }

    const updateData: Partial<Task> = {
      progress,
      timeline: [...timelineEntries, ...currentTimeline],
      updatedAt: Timestamp.now()
    };

    // Update status if it changed
    if (statusChanged) {
      updateData.status = newStatus;

      // Set completedAt if task is completed
      if (newStatus === TaskStatus.COMPLETED) {
        updateData.completedAt = Timestamp.now();
      }
    }

    await updateDoc(taskRef, updateData);
  }

  /**
   * Adds an update to a task
   */
  static async addTaskUpdate(taskId: string, update: Omit<TaskUpdate, 'id' | 'createdAt'>): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);

    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentUpdates = taskData.updates || [];
    const currentTimeline = taskData.timeline || [];
    const currentStatus = taskData.status;

    const newUpdate: TaskUpdate = {
      ...update,
      id: Date.now().toString(),
      createdAt: Timestamp.now()
    };

    const newTimelineEntry: TimelineEntry = {
      id: Date.now().toString(),
      type: 'update',
      title: newUpdate.title,
      description: newUpdate.description,
      progress: newUpdate.progress,
      createdBy: newUpdate.createdBy,
      createdByName: newUpdate.createdByName,
      createdAt: newUpdate.createdAt
    };

    const timelineEntries = [newTimelineEntry];
    const updateData: Partial<Task> = {
      updates: [newUpdate, ...currentUpdates],
      updatedAt: Timestamp.now()
    };

    // Handle automatic status change if progress is included
    if (newUpdate.progress !== undefined) {
      updateData.progress = newUpdate.progress;

      let newStatus = currentStatus;
      let statusChanged = false;

      if (newUpdate.progress >= 100 && currentStatus !== TaskStatus.COMPLETED) {
        newStatus = TaskStatus.COMPLETED;
        statusChanged = true;
      } else if (newUpdate.progress > 0 && newUpdate.progress < 100 && currentStatus === TaskStatus.PENDING) {
        newStatus = TaskStatus.IN_PROGRESS;
        statusChanged = true;
      }

      if (statusChanged) {
        updateData.status = newStatus;

        // Set completedAt if task is completed
        if (newStatus === TaskStatus.COMPLETED) {
          updateData.completedAt = Timestamp.now();
        }

        // Add status change timeline entry
        const statusChangeEntry: TimelineEntry = {
          id: (Date.now() + 1).toString(),
          type: 'status_change',
          title: `Status automatically changed to ${newStatus}`,
          oldStatus: currentStatus,
          newStatus: newStatus,
          createdBy: newUpdate.createdBy,
          createdByName: newUpdate.createdByName,
          createdAt: Timestamp.now()
        };
        timelineEntries.push(statusChangeEntry);
      }
    }

    updateData.timeline = [...timelineEntries, ...currentTimeline];
    await updateDoc(taskRef, updateData);
  }

  /**
   * Deletes a task
   */
  static async deleteTask(taskId: string): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    await deleteDoc(taskRef);
  }

  /**
   * Gets a single task by ID
   */
  static async getTask(taskId: string): Promise<Task | null> {
    const taskRef = doc(tasksCollection, taskId);
    const docSnap = await getDoc(taskRef);

    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data()
      } as Task;
    }

    return null;
  }

  /**
   * Assigns users to a task
   */
  static async assignUsersToTask(taskId: string, assignees: TaskAssignee[]): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    await updateDoc(taskRef, {
      assignees,
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Deletes an update from a task
   */
  static async deleteTaskUpdate(taskId: string, updateId: string): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);
    
    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentUpdates = taskData.updates || [];
    const currentTimeline = taskData.timeline || [];
    
    // Remove the update
    const updatedUpdates = currentUpdates.filter(update => update.id !== updateId);
    
    // Remove corresponding timeline entry
    const updatedTimeline = currentTimeline.filter(entry => 
      !(entry.type === 'update' && entry.id === updateId)
    );

    await updateDoc(taskRef, {
      updates: updatedUpdates,
      timeline: updatedTimeline,
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Deletes a progress update from timeline
   */
  static async deleteProgressUpdate(taskId: string, timelineId: string): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);
    
    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentTimeline = taskData.timeline || [];
    
    // Remove the timeline entry
    const updatedTimeline = currentTimeline.filter(entry => entry.id !== timelineId);

    await updateDoc(taskRef, {
      timeline: updatedTimeline,
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Adds a related person to a task
   */
  static async addRelatedPerson(taskId: string, person: TaskRelatedPerson): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);
    
    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentRelatedPeople = taskData.relatedPeople || [];
    
    await updateDoc(taskRef, {
      relatedPeople: [...currentRelatedPeople, person],
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Removes a related person from a task
   */
  static async removeRelatedPerson(taskId: string, email: string): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);
    
    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentRelatedPeople = taskData.relatedPeople || [];
    
    const updatedRelatedPeople = currentRelatedPeople.filter(person => person.email !== email);
    
    await updateDoc(taskRef, {
      relatedPeople: updatedRelatedPeople,
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Adds a risk to a task
   */
  static async addTaskRisk(taskId: string, risk: Omit<TaskRisk, 'id' | 'createdAt'>): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);
    
    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentRisks = taskData.risks || [];
    
    const newRisk: TaskRisk = {
      ...risk,
      id: Date.now().toString(),
      createdAt: Timestamp.now()
    };
    
    await updateDoc(taskRef, {
      risks: [...currentRisks, newRisk],
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Removes a risk from a task
   */
  static async removeTaskRisk(taskId: string, riskId: string): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);
    
    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentRisks = taskData.risks || [];
    
    const updatedRisks = currentRisks.filter(risk => risk.id !== riskId);
    
    await updateDoc(taskRef, {
      risks: updatedRisks,
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Updates the output link for a task
   */
  static async updateOutputLink(taskId: string, outputLink: string): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    await updateDoc(taskRef, {
      outputLink,
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Gets task statistics
   */
  static async getTaskStatistics(): Promise<{
    total: number;
    pending: number;
    inProgress: number;
    completed: number;
    cancelled: number;
  }> {
    const tasks = await this.getTasks();

    return {
      total: tasks.length,
      pending: tasks.filter(t => t.status === TaskStatus.PENDING).length,
      inProgress: tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length,
      completed: tasks.filter(t => t.status === TaskStatus.COMPLETED).length,
      cancelled: tasks.filter(t => t.status === TaskStatus.CANCELLED).length,
    };
  }

  /**
   * Gets tasks within a date range
   */
  static async getTasksByDateRange(startDate: Date, endDate: Date): Promise<Task[]> {
    const startTimestamp = Timestamp.fromDate(startDate);
    const endTimestamp = Timestamp.fromDate(endDate);

    const q = query(
      tasksCollection,
      where('createdAt', '>=', startTimestamp),
      where('createdAt', '<=', endTimestamp),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const tasks: Task[] = [];

    querySnapshot.forEach((doc) => {
      tasks.push({
        id: doc.id,
        ...doc.data()
      } as Task);
    });

    return tasks;
  }

  /**
   * Gets daily task statistics for a month
   */
  static async getDailyTaskStatistics(year: number, month: number): Promise<Map<string, {
    created: number;
    updated: number;
    completed: number;
    statusChanges: number;
    progressUpdates: number;
  }>> {
    const startDate = new Date(year, month, 1);
    const endDate = new Date(year, month + 1, 0, 23, 59, 59);

    const tasks = await this.getTasksByDateRange(startDate, endDate);
    const dailyStats = new Map<string, {
      created: number;
      updated: number;
      completed: number;
      statusChanges: number;
      progressUpdates: number;
    }>();

    // Initialize all days in the month
    for (let day = 1; day <= endDate.getDate(); day++) {
      const dateKey = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      dailyStats.set(dateKey, {
        created: 0,
        updated: 0,
        completed: 0,
        statusChanges: 0,
        progressUpdates: 0
      });
    }

    // Count task creations
    tasks.forEach(task => {
      const createdDate = task.createdAt.toDate();
      const dateKey = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}-${String(createdDate.getDate()).padStart(2, '0')}`;

      const stats = dailyStats.get(dateKey);
      if (stats) {
        stats.created++;

        // Count completions
        if (task.completedAt) {
          const completedDate = task.completedAt.toDate();
          const completedDateKey = `${completedDate.getFullYear()}-${String(completedDate.getMonth() + 1).padStart(2, '0')}-${String(completedDate.getDate()).padStart(2, '0')}`;
          const completedStats = dailyStats.get(completedDateKey);
          if (completedStats) {
            completedStats.completed++;
          }
        }

        // Count timeline activities
        if (task.timeline) {
          task.timeline.forEach(entry => {
            const entryDate = entry.createdAt.toDate();
            const entryDateKey = `${entryDate.getFullYear()}-${String(entryDate.getMonth() + 1).padStart(2, '0')}-${String(entryDate.getDate()).padStart(2, '0')}`;
            const entryStats = dailyStats.get(entryDateKey);

            if (entryStats) {
              entryStats.updated++;

              if (entry.type === 'status_change') {
                entryStats.statusChanges++;
              } else if (entry.type === 'progress') {
                entryStats.progressUpdates++;
              }
            }
          });
        }
      }
    });

    return dailyStats;
  }

  /**
   * Gets detailed task activities for a specific date
   */
  static async getTaskActivitiesForDate(date: Date): Promise<{
    tasksCreated: Task[];
    tasksCompleted: Task[];
    timelineEntries: Array<{
      task: Task;
      entry: TimelineEntry;
    }>;
  }> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    // Get all tasks to check for activities on this date
    const allTasks = await this.getTasks();

    const tasksCreated: Task[] = [];
    const tasksCompleted: Task[] = [];
    const timelineEntries: Array<{ task: Task; entry: TimelineEntry }> = [];

    allTasks.forEach(task => {
      // Check if task was created on this date
      const createdDate = task.createdAt.toDate();
      if (createdDate >= startOfDay && createdDate <= endOfDay) {
        tasksCreated.push(task);
      }

      // Check if task was completed on this date
      if (task.completedAt) {
        const completedDate = task.completedAt.toDate();
        if (completedDate >= startOfDay && completedDate <= endOfDay) {
          tasksCompleted.push(task);
        }
      }

      // Check timeline entries for this date
      if (task.timeline) {
        task.timeline.forEach(entry => {
          const entryDate = entry.createdAt.toDate();
          if (entryDate >= startOfDay && entryDate <= endOfDay) {
            timelineEntries.push({ task, entry });
          }
        });
      }
    });

    // Sort timeline entries by date (most recent first)
    timelineEntries.sort((a, b) => b.entry.createdAt.toMillis() - a.entry.createdAt.toMillis());

    return {
      tasksCreated,
      tasksCompleted,
      timelineEntries
    };
  }

  /**
   * Adds feedback to a task
   */
  static async addTaskFeedback(taskId: string, feedbackData: {
    content: string;
    createdBy: string;
    createdByName: string;
  }): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);

    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentFeedback = taskData.feedback || [];

    const newFeedback: TaskFeedback = {
      id: Date.now().toString(),
      content: feedbackData.content,
      createdBy: feedbackData.createdBy,
      createdByName: feedbackData.createdByName,
      createdAt: Timestamp.now(),
      replies: [],
      isResolved: false
    };

    await updateDoc(taskRef, {
      feedback: [newFeedback, ...currentFeedback],
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Adds a reply to task feedback
   */
  static async addFeedbackReply(taskId: string, feedbackId: string, replyData: {
    content: string;
    createdBy: string;
    createdByName: string;
  }): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);

    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentFeedback = taskData.feedback || [];

    const updatedFeedback = currentFeedback.map(feedback => {
      if (feedback.id === feedbackId) {
        const newReply: TaskFeedbackReply = {
          id: Date.now().toString(),
          content: replyData.content,
          createdBy: replyData.createdBy,
          createdByName: replyData.createdByName,
          createdAt: Timestamp.now()
        };

        return {
          ...feedback,
          replies: [...feedback.replies, newReply]
        };
      }
      return feedback;
    });

    await updateDoc(taskRef, {
      feedback: updatedFeedback,
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Updates the status of a feedback reply (Done/Not Done)
   */
  static async updateReplyStatus(taskId: string, feedbackId: string, replyId: string, status: 'done' | 'not_done', updatedBy: string, updatedByName: string): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);

    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentFeedback = taskData.feedback || [];

    const updatedFeedback = currentFeedback.map(feedback => {
      if (feedback.id === feedbackId) {
        const updatedReplies = feedback.replies.map(reply => {
          if (reply.id === replyId) {
            return {
              ...reply,
              status,
              statusUpdatedAt: Timestamp.now(),
              statusUpdatedBy: updatedBy,
              statusUpdatedByName: updatedByName
            };
          }
          return reply;
        });

        return {
          ...feedback,
          replies: updatedReplies
        };
      }
      return feedback;
    });

    await updateDoc(taskRef, {
      feedback: updatedFeedback,
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Marks feedback as resolved
   */
  static async resolveFeedback(taskId: string, feedbackId: string, resolvedBy: string, resolvedByName: string): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);

    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentFeedback = taskData.feedback || [];

    const updatedFeedback = currentFeedback.map(feedback => {
      if (feedback.id === feedbackId) {
        return {
          ...feedback,
          isResolved: true,
          resolvedAt: Timestamp.now(),
          resolvedBy,
          resolvedByName
        };
      }
      return feedback;
    });

    await updateDoc(taskRef, {
      feedback: updatedFeedback,
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Deletes feedback from a task
   */
  static async deleteFeedback(taskId: string, feedbackId: string): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);

    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentFeedback = taskData.feedback || [];

    const updatedFeedback = currentFeedback.filter(feedback => feedback.id !== feedbackId);

    await updateDoc(taskRef, {
      feedback: updatedFeedback,
      updatedAt: Timestamp.now()
    });
  }

  /**
   * Deletes a reply from feedback
   */
  static async deleteFeedbackReply(taskId: string, feedbackId: string, replyId: string): Promise<void> {
    const taskRef = doc(tasksCollection, taskId);
    const taskDoc = await getDoc(taskRef);

    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data() as Task;
    const currentFeedback = taskData.feedback || [];

    const updatedFeedback = currentFeedback.map(feedback => {
      if (feedback.id === feedbackId) {
        return {
          ...feedback,
          replies: feedback.replies.filter(reply => reply.id !== replyId)
        };
      }
      return feedback;
    });

    await updateDoc(taskRef, {
      feedback: updatedFeedback,
      updatedAt: Timestamp.now()
    });
  }
}