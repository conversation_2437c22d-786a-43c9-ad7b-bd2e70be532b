"use client";

import React, { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { z } from "zod";
import { AuthForm } from "@/components/ui/authUI/AuthForm";
import { FormField } from "@/components/ui/authUI/FormField";
import { resetPassword } from "@/Firebase/Authentication/authConfig";
import { handleAuthError } from "@/Firebase/Authentication/errorHandler";
import { useToast } from "@/components/ui/use-toast";
import { Dictionary } from "@/dictionaries";
import { i18n, type Locale } from '@/i18n-config';
import { Button } from "@/components/ui/button";

// Language switcher component
const LanguageSwitcher = () => {
  const pathname = usePathname();

  const getPathWithLocale = (locale: Locale) => {
    if (!pathname) return '/';
    const segments = pathname.split('/');
    segments[1] = locale;
    return segments.join('/');
  };

  return (
    <div className="flex gap-2">
      {i18n.locales.map((locale) => {
        const isActive = pathname.split('/')[1] === locale;
        return (
          <Link key={locale} href={getPathWithLocale(locale)}>
            <Button variant={isActive ? "secondary" : "ghost"} size="sm">
              {locale.toUpperCase()}
            </Button>
          </Link>
        );
      })}
    </div>
  );
};

// Animation background component
const AnimatedBackground = () => {
  // Predefined values to avoid hydration mismatch
  const circleData = [
    { width: 275, height: 120, top: 15, left: 85, opacity: 0.3, duration: 18, delay: 2 },
    { width: 150, height: 200, top: 65, left: 25, opacity: 0.2, duration: 15, delay: 0 },
    { width: 320, height: 180, top: 45, left: 60, opacity: 0.25, duration: 20, delay: 1 },
    { width: 100, height: 250, top: 80, left: 10, opacity: 0.15, duration: 16, delay: 3 },
    { width: 240, height: 160, top: 20, left: 40, opacity: 0.35, duration: 14, delay: 4 },
    { width: 180, height: 220, top: 55, left: 75, opacity: 0.25, duration: 19, delay: 1.5 },
    { width: 120, height: 140, top: 30, left: 90, opacity: 0.2, duration: 17, delay: 2.5 },
    { width: 300, height: 100, top: 70, left: 5, opacity: 0.3, duration: 13, delay: 0.5 },
    { width: 90, height: 190, top: 10, left: 70, opacity: 0.15, duration: 21, delay: 3.5 },
    { width: 260, height: 170, top: 85, left: 50, opacity: 0.28, duration: 12, delay: 1.8 }
  ];

  return (
    <div className="relative w-full h-full overflow-hidden bg-gradient-to-br from-[#003366] via-[#004080] to-[#0066b3]">
      {/* Animated circles */}
      <div className="absolute top-0 left-0 w-full h-full">
        {circleData.map((circle, i) => (
          <div 
            key={i}
            className="absolute rounded-full opacity-20 animate-float"
            style={{
              width: `${circle.width}px`,
              height: `${circle.height}px`,
              top: `${circle.top}%`,
              left: `${circle.left}%`,
              backgroundColor: `rgba(255, 255, 255, ${circle.opacity})`,
              animationDuration: `${circle.duration}s`,
              animationDelay: `${circle.delay}s`
            }}
          />
        ))}
      </div>
      
      {/* Branding */}
      <div className="absolute inset-0 flex flex-col items-center justify-center text-white z-10">
        <h1 className="text-4xl md:text-5xl font-bold mb-4 text-center">THIqah COnsultant Helper</h1>
        <p className="text-xl md:text-2xl text-center max-w-lg opacity-80">Your trusted consultant helper for all your business needs</p>
      </div>
    </div>
  );
};

export default function ResetClient({
  dict
}: {
  dict: Dictionary
}) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  
  // Add animation CSS
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes float {
        0% { transform: translate(0, 0) rotate(0deg); opacity: 0.2; }
        50% { transform: translate(30px, 30px) rotate(180deg); opacity: 0.5; }
        100% { transform: translate(0, 0) rotate(360deg); opacity: 0.2; }
      }
      .animate-float {
        animation: float 15s infinite ease-in-out;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Form schema
  const formSchema = z.object({
    email: z.string().email({
      message: dict.auth.common.invalidEmail,
    }),
  });

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      await resetPassword(data.email);
      toast({
        title: dict.auth.reset.success,
        variant: "default",
      });
      setEmailSent(true);
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: dict.auth.reset.error,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex h-screen w-full overflow-hidden">
      {/* 70% Animated Background */}
      <div className="hidden md:block w-[70%] h-full">
        <AnimatedBackground />
      </div>
      
      {/* 30% Reset Password Form */}
      <div className="w-full md:w-[30%] h-full bg-background flex flex-col justify-start items-center p-6 overflow-y-auto">
        {/* Language switcher at the top */}
        <div className="w-full flex justify-end mb-8">
          <LanguageSwitcher />
        </div>
        
        {/* Logo */}
        <div className="mb-8 flex justify-center">
          <Image 
            src="/image.png" 
            alt="THIqah Logo" 
            width={150} 
            height={150} 
            className="mx-auto"
            priority
          />
        </div>
        
        <div className="w-full max-w-md">
          <h2 className="text-2xl font-bold mb-2 text-center text-[#003366]">{dict.auth.reset.title}</h2>
          <p className="text-muted-foreground text-center mb-6">{dict.auth.reset.subtitle}</p>
          
          {emailSent ? (
            <div className="text-center space-y-4">
              <div className="bg-[#f0f5fa] border border-[#003366]/20 p-4 rounded-md">
                <p className="text-sm text-[#003366]">{dict.auth.reset.success}</p>
              </div>
              <Link
                href="signin"
                className="text-[#003366] underline underline-offset-4 hover:text-[#0066b3] mt-4 inline-block"
              >
                {dict.auth.reset.backToSignIn}
              </Link>
            </div>
          ) : (
            <>
              <AuthForm
                schema={formSchema}
                onSubmit={onSubmit}
                submitText={dict.auth.reset.button}
                isLoading={isLoading}
              >
                <FormField
                  name="email"
                  label={dict.auth.common.email}
                  placeholder="<EMAIL>"
                  type="email"
                  required
                  autoComplete="email"
                />
              </AuthForm>
              
              <div className="text-center mt-6">
                <Link
                  href="signin"
                  className="text-[#003366] underline underline-offset-4 hover:text-[#0066b3]"
                >
                  {dict.auth.reset.backToSignIn}
                </Link>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
