"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";
import { useRouter } from "next/navigation";
import { ClipboardList, Calendar, Users, BarChart3, Settings, CheckSquare, Plus, Edit, Trash2, X, FileText, AlertCircle, Tag, User, Clock, Save, TrendingUp, Target, AlertTriangle, Shield, Activity, Zap, Timer, Award, BookOpen, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { TasksService, TaskPriority, TaskStatus, TaskAssignee, Task, TimelineEntry } from "@/Firebase/firestore/services/TasksService";
import { SystemsService, System, SystemTaskStatus } from "@/Firebase/firestore/SystemsService";
import { Timestamp } from 'firebase/firestore';
import { getAllUsers, UserRole, UserProfile } from "@/Firebase/firestore/services/UserService";
import { auth } from "@/Firebase/Authentication/authConfig";
import { AddTaskModal } from "@/components/ui/AddTaskModal";
import ExcelJS from 'exceljs';

// Type guard to check if a value is a valid SystemTaskStatus
function isValidSystemTaskStatus(value: unknown): value is SystemTaskStatus {
  return Object.values(SystemTaskStatus).includes(value as SystemTaskStatus);
}

// Helper function to safely get system task status
function getSystemTaskStatus(system: System, taskKey: string): SystemTaskStatus {
  const status = system[taskKey as keyof System] as SystemTaskStatus;
  return isValidSystemTaskStatus(status) ? status : SystemTaskStatus.NOT_STARTED;
}

// Helper function to check if status is pending (not started and not done)
function isStatusPending(status: SystemTaskStatus): boolean {
  return status !== SystemTaskStatus.NOT_STARTED && status !== SystemTaskStatus.DONE;
}

interface TasksPageProps {
  params: Promise<{ lang: Locale }>;
}

interface EditTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (taskData: {
    title: string;
    description: string;
    priority: TaskPriority;
    assignees: TaskAssignee[];
    dueDate?: Date;
    tags?: string[];
  }) => Promise<void>;
  isRTL: boolean;
  isLoading?: boolean;
  task: Task | null;
}

interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  taskTitle: string;
  isRTL: boolean;
  isLoading?: boolean;
}

function DeleteConfirmModal({ isOpen, onClose, onConfirm, taskTitle, isRTL, isLoading }: DeleteConfirmModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className={`bg-white rounded-2xl shadow-2xl border border-gray-200 w-full max-w-md ${isRTL ? "rtl" : "ltr"}`}
      >
        <div className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <Trash2 className="w-6 h-6 text-red-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {isRTL ? "حذف المهمة" : "Delete Task"}
              </h3>
              <p className="text-sm text-gray-500">
                {isRTL ? "هذا الإجراء لا يمكن التراجع عنه" : "This action cannot be undone"}
              </p>
            </div>
          </div>

          <p className="text-gray-700 mb-6">
            {isRTL 
              ? `هل أنت متأكد من أنك تريد حذف المهمة "${taskTitle}"؟`
              : `Are you sure you want to delete the task "${taskTitle}"?`
            }
          </p>

          <div className="flex gap-3">
            <Button
              onClick={onClose}
              variant="outline"
              className="flex-1"
              disabled={isLoading}
            >
              {isRTL ? "إلغاء" : "Cancel"}
            </Button>
            <Button
              onClick={onConfirm}
              className="flex-1 bg-red-600 hover:bg-red-700 text-white"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  {isRTL ? "جاري الحذف..." : "Deleting..."}
                </div>
              ) : (
                <>
                  <Trash2 className="w-4 h-4 mr-2" />
                  {isRTL ? "حذف" : "Delete"}
                </>
              )}
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

function EditTaskModal({ isOpen, onClose, onSubmit, isRTL, isLoading = false, task }: EditTaskModalProps) {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    priority: TaskPriority.MEDIUM,
    dueDate: "",
    tags: ""
  });
  
  const [errors, setErrors] = useState<{title?: string; description?: string}>({});
  const [selectedAssignees, setSelectedAssignees] = useState<TaskAssignee[]>([]);
  const [availableUsers, setAvailableUsers] = useState<UserProfile[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [showUserDropdown, setShowUserDropdown] = useState(false);

  // Initialize form data when task changes
  useEffect(() => {
    if (task && isOpen) {
      setFormData({
        title: task.title || "",
        description: task.description || "",
        priority: task.priority || TaskPriority.MEDIUM,
        dueDate: task.dueDate ? new Date(task.dueDate.toDate()).toISOString().slice(0, 16) : "",
        tags: task.tags ? task.tags.join(', ') : ""
      });
      setSelectedAssignees(task.assignees || []);
      loadUsers();
    }
  }, [task, isOpen]);

  const loadUsers = async () => {
    try {
      setIsLoadingUsers(true);
      const users = await getAllUsers();
      setAvailableUsers(users);
    } catch (error) {
      console.error('Error loading users:', error);
    } finally {
      setIsLoadingUsers(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleAddAssignee = (user: UserProfile) => {
    const assignee: TaskAssignee = {
      uid: user.uid,
      displayName: user.displayName || user.email || "Unknown",
      email: user.email || "",
      role: user.role
    };

    if (!selectedAssignees.find(a => a.uid === user.uid)) {
      setSelectedAssignees(prev => [...prev, assignee]);
    }
    setShowUserDropdown(false);
  };

  const handleRemoveAssignee = (uid: string) => {
    setSelectedAssignees(prev => prev.filter(a => a.uid !== uid));
  };

  const validateForm = (): boolean => {
    const newErrors: {title?: string; description?: string} = {};

    if (!formData.title.trim()) {
      newErrors.title = isRTL ? "عنوان المهمة مطلوب" : "Task title is required";
    } else if (formData.title.length < 3) {
      newErrors.title = isRTL ? "يجب أن يكون العنوان 3 أحرف على الأقل" : "Title must be at least 3 characters";
    }

    if (!formData.description.trim()) {
      newErrors.description = isRTL ? "وصف المهمة مطلوب" : "Task description is required";
    } else if (formData.description.length < 10) {
      newErrors.description = isRTL ? "يجب أن يكون الوصف 10 أحرف على الأقل" : "Description must be at least 10 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const taskData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        priority: formData.priority,
        assignees: selectedAssignees,
        dueDate: formData.dueDate ? new Date(formData.dueDate) : undefined,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean) : undefined
      };

      await onSubmit(taskData);
      handleClose();
    } catch (error) {
      console.error('Error updating task:', error);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        title: "",
        description: "",
        priority: TaskPriority.MEDIUM,
        dueDate: "",
        tags: ""
      });
      setSelectedAssignees([]);
      setErrors({});
      setShowUserDropdown(false);
      onClose();
    }
  };

  const priorityOptions = [
    { value: TaskPriority.LOW, label: isRTL ? "منخفضة" : "Low", color: "text-green-600 bg-green-50" },
    { value: TaskPriority.MEDIUM, label: isRTL ? "متوسطة" : "Medium", color: "text-yellow-600 bg-yellow-50" },
    { value: TaskPriority.HIGH, label: isRTL ? "عالية" : "High", color: "text-orange-600 bg-orange-50" },
    { value: TaskPriority.URGENT, label: isRTL ? "عاجلة" : "Urgent", color: "text-red-600 bg-red-50" }
  ];

  const availableUsersForSelection = availableUsers.filter(
    user => !selectedAssignees.find(a => a.uid === user.uid)
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-gray-900/20 backdrop-blur-sm"
        onClick={handleClose}
      />

      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        transition={{ duration: 0.2 }}
        className={`relative bg-white rounded-3xl shadow-2xl border border-gray-100 w-full max-w-2xl max-h-[90vh] overflow-hidden ${isRTL ? "rtl" : "ltr"}`}
      >
        <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center shadow-lg border border-white/20">
                <Edit className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">
                  {isRTL ? "تعديل المهمة" : "Edit Task"}
                </h2>
                <p className="text-white/80 text-sm">
                  {isRTL ? "تعديل تفاصيل المهمة" : "Modify task details"}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="text-white hover:bg-white/20 rounded-xl"
              disabled={isLoading}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        <div className="overflow-y-auto max-h-[calc(90vh-160px)]">
          <form onSubmit={handleSubmit} className="p-8 space-y-6">
            <div className="space-y-2">
              <label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                <CheckSquare className="w-4 h-4 text-[var(--brand-blue)]" />
                {isRTL ? "عنوان المهمة" : "Task Title"}
              </label>
              <Input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                className={`text-lg font-medium ${errors.title ? "border-red-500 focus:border-red-500" : "border-gray-200 focus:border-[var(--brand-blue)]"}`}
                placeholder={isRTL ? "أدخل عنوان المهمة" : "Enter task title"}
                disabled={isLoading}
              />
              {errors.title && (
                <p className="text-sm text-red-600 flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" />
                  {errors.title}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                <FileText className="w-4 h-4 text-[var(--brand-blue)]" />
                {isRTL ? "وصف المهمة" : "Task Description"}
              </label>
              <Textarea
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                className={`min-h-32 resize-none ${errors.description ? "border-red-500 focus:border-red-500" : "border-gray-200 focus:border-[var(--brand-blue)]"}`}
                placeholder={isRTL ? "اكتب وصفاً مفصلاً للمهمة" : "Write a detailed description of the task"}
                disabled={isLoading}
              />
              {errors.description && (
                <p className="text-sm text-red-600 flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" />
                  {errors.description}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                  <AlertCircle className="w-4 h-4 text-[var(--brand-blue)]" />
                  {isRTL ? "الأولوية" : "Priority"}
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {priorityOptions.map((option) => (
                    <button
                      key={option.value}
                      type="button"
                      onClick={() => handleInputChange("priority", option.value)}
                      className={`p-3 rounded-xl border-2 transition-all duration-200 text-sm font-medium ${
                        formData.priority === option.value
                          ? `${option.color} border-current`
                          : 'border-gray-200 text-gray-600 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                      disabled={isLoading}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                  <Calendar className="w-4 h-4 text-[var(--brand-blue)]" />
                  {isRTL ? "تاريخ الاستحقاق" : "Due Date"}
                </label>
                <Input
                  type="datetime-local"
                  value={formData.dueDate}
                  onChange={(e) => handleInputChange("dueDate", e.target.value)}
                  className="border-gray-200 focus:border-[var(--brand-blue)]"
                  disabled={isLoading}
                />
              </div>
            </div>

            <div className="space-y-3">
              <label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                <Users className="w-4 h-4 text-[var(--brand-blue)]" />
                {isRTL ? "المُكلفون" : "Assignees"}
              </label>
              
              {selectedAssignees.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-3">
                  {selectedAssignees.map((assignee) => (
                    <div
                      key={assignee.uid}
                      className="bg-[var(--brand-blue)]/10 border border-[var(--brand-blue)]/20 rounded-xl px-3 py-2 flex items-center gap-2"
                    >
                      <User className="w-4 h-4 text-[var(--brand-blue)]" />
                      <span className="text-sm font-medium text-gray-800">
                        {assignee.displayName || assignee.email || "Unknown"}
                      </span>
                      <button
                        type="button"
                        onClick={() => handleRemoveAssignee(assignee.uid)}
                        className="text-red-500 hover:text-red-700 ml-1"
                        disabled={isLoading}
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}

              <div className="relative">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowUserDropdown(!showUserDropdown)}
                  className="w-full justify-start border-gray-200 hover:border-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/5"
                  disabled={isLoading || isLoadingUsers}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  {isRTL ? "إضافة مُكلف" : "Add Assignee"}
                </Button>

                {showUserDropdown && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="absolute top-full left-0 right-0 z-10 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg max-h-48 overflow-y-auto"
                  >
                    {isLoadingUsers ? (
                      <div className="p-4 text-center text-gray-500">
                        <Clock className="w-4 h-4 animate-spin mx-auto mb-2" />
                        {isRTL ? "جاري التحميل..." : "Loading..."}
                      </div>
                    ) : availableUsersForSelection.length === 0 ? (
                      <div className="p-4 text-center text-gray-500">
                        {isRTL ? "لا توجد مستخدمون متاحون" : "No available users"}
                      </div>
                    ) : (
                      availableUsersForSelection.map((user) => (
                        <button
                          key={user.uid}
                          type="button"
                          onClick={() => handleAddAssignee(user)}
                          className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center gap-3 border-b border-gray-100 last:border-b-0"
                        >
                          <div className="w-8 h-8 bg-[var(--brand-blue)]/10 rounded-full flex items-center justify-center">
                            <User className="w-4 h-4 text-[var(--brand-blue)]" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-gray-900 truncate">
                              {user.displayName || user.email}
                            </div>
                          </div>
                        </button>
                      ))
                    )}
                  </motion.div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                <Tag className="w-4 h-4 text-[var(--brand-blue)]" />
                {isRTL ? "العلامات" : "Tags"}
              </label>
              <Input
                type="text"
                value={formData.tags}
                onChange={(e) => handleInputChange("tags", e.target.value)}
                className="border-gray-200 focus:border-[var(--brand-blue)]"
                placeholder={isRTL ? "أدخل العلامات مفصولة بفواصل" : "Enter tags separated by commas"}
                disabled={isLoading}
              />
              <p className="text-xs text-gray-500">
                {isRTL ? "مثال: تطوير، مراجعة، تحليل" : "Example: development, review, analysis"}
              </p>
            </div>

            <div className="flex gap-4 pt-6 border-t border-gray-100">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                className="flex-1 border-gray-200 hover:bg-gray-50"
                disabled={isLoading}
              >
                {isRTL ? "إلغاء" : "Cancel"}
              </Button>
              <Button
                type="submit"
                className="flex-1 bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 hover:from-[var(--brand-blue)]/90 hover:to-[var(--brand-blue)]/70 text-white shadow-lg"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    {isRTL ? "جاري الحفظ..." : "Saving..."}
                  </div>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    {isRTL ? "حفظ التغييرات" : "Save Changes"}
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
}

export default function TasksPage({ params }: TasksPageProps) {
  const [lang, setLang] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'overview' | 'management'>('overview');
  const [tasks, setTasks] = useState<Task[]>([]);
  const [consultantsCount, setConsultantsCount] = useState(0);
  const [taskStats, setTaskStats] = useState({
    total: 0,
    pending: 0,
    inProgress: 0,
    completed: 0,
    cancelled: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isAddTaskModalOpen, setIsAddTaskModalOpen] = useState(false);
  const [isCreatingTask, setIsCreatingTask] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isEditingTask, setIsEditingTask] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState<Task | null>(null);
  const [isExportingReport, setIsExportingReport] = useState(false);
  
  // Dashboard data state
  const [overdueTasks, setOverdueTasks] = useState<Task[]>([]);
  const [recentUpdates, setRecentUpdates] = useState<{task: Task, update: TimelineEntry}[]>([]);
  const [progressData, setProgressData] = useState<{taskId: string, title: string, progress: number}[]>([]);
  const [priorityData, setPriorityData] = useState<{[key: string]: number}>({});
  const [assigneeData, setAssigneeData] = useState<{name: string, count: number}[]>([]);
  const [riskData, setRiskData] = useState<{total: number, high: number, critical: number}>({total: 0, high: 0, critical: 0});
  
  const router = useRouter();
  const { toast } = useToast();

  // Helper function to safely convert Timestamp to Date
  const timestampToDate = (timestamp: Timestamp | undefined): Date | null => {
    if (!timestamp) return null;
    return timestamp.toDate ? timestamp.toDate() : new Date(timestamp as unknown as string | number | Date);
  };

  // Helper function to format dates
  const formatDate = (timestamp: Timestamp, isRTL: boolean) => {
    const date = timestamp.toDate();
    return date.toLocaleDateString(isRTL ? "ar-SA" : "en-US", {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatRelativeTime = (timestamp: Timestamp, isRTL: boolean) => {
    const date = timestamp.toDate();
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) {
      return isRTL ? "منذ دقائق" : "Minutes ago";
    } else if (diffHours < 24) {
      return isRTL ? `منذ ${diffHours} ساعة` : `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return isRTL ? `منذ ${diffDays} يوم` : `${diffDays}d ago`;
    } else {
      return formatDate(timestamp, isRTL);
    }
  };

  // Initialize params
  useEffect(() => {
    params.then(({ lang }) => {
      setLang(lang);
    });
  }, [params]);

  // Load data functions
  const loadTasks = useCallback(async () => {
    try {
      const [tasksData, stats] = await Promise.all([
        TasksService.getTasks(),
        TasksService.getTaskStatistics()
      ]);
      setTasks(tasksData);
      setTaskStats(stats);
    } catch (error) {
      console.error('Error loading tasks:', error);
    }
  }, []);

  const loadConsultantsCount = useCallback(async () => {
    try {
      const users = await getAllUsers();
      const consultants = users.filter(user => user.role === UserRole.CONSULTANT);
      setConsultantsCount(consultants.length);
    } catch (error) {
      console.error('Error loading consultants count:', error);
    }
  }, []);

  const loadDashboardData = useCallback(async () => {
    try {
      const allTasks = await TasksService.getTasks();
      
      // Calculate overdue tasks
      const now = new Date();
      const overdue = allTasks.filter(task => 
        task.dueDate && 
        task.dueDate.toDate() < now && 
        task.status !== TaskStatus.COMPLETED
      );
      setOverdueTasks(overdue);

      // Get recent updates (tasks with timeline entries in last 7 days)
      const recentUpdatesData: {task: Task, update: TimelineEntry}[] = [];
      allTasks.forEach(task => {
        if (task.timeline && task.timeline.length > 0) {
          const recentTimeline = task.timeline.filter(entry => {
            const entryDate = entry.createdAt.toDate();
            const daysDiff = (now.getTime() - entryDate.getTime()) / (1000 * 3600 * 24);
            return daysDiff <= 7;
          });
          recentTimeline.forEach(entry => {
            recentUpdatesData.push({task, update: entry});
          });
        }
      });
      recentUpdatesData.sort((a, b) => b.update.createdAt.toDate().getTime() - a.update.createdAt.toDate().getTime());
      setRecentUpdates(recentUpdatesData.slice(0, 5)); // Latest 5 updates

      // Calculate progress data
      const progressTasks = allTasks
        .filter(task => task.progress !== undefined && task.status !== TaskStatus.COMPLETED)
        .map(task => ({
          taskId: task.id!,
          title: task.title,
          progress: task.progress || 0
        }))
        .sort((a, b) => b.progress - a.progress)
        .slice(0, 5);
      setProgressData(progressTasks);

      // Calculate priority distribution
      const priorities = {
        [TaskPriority.LOW]: 0,
        [TaskPriority.MEDIUM]: 0,
        [TaskPriority.HIGH]: 0,
        [TaskPriority.URGENT]: 0
      };
      allTasks.forEach(task => {
        priorities[task.priority]++;
      });
      setPriorityData(priorities);

      // Calculate assignee workload
      const assigneeMap = new Map<string, number>();
      allTasks.forEach(task => {
        task.assignees.forEach(assignee => {
          const current = assigneeMap.get(assignee.displayName || assignee.email) || 0;
          assigneeMap.set(assignee.displayName || assignee.email, current + 1);
        });
      });
      const assigneeArray = Array.from(assigneeMap.entries())
        .map(([name, count]) => ({name, count}))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);
      setAssigneeData(assigneeArray);

      // Calculate risk data
      let totalRisks = 0;
      let highRisks = 0;
      let criticalRisks = 0;
      allTasks.forEach(task => {
        if (task.risks && task.risks.length > 0) {
          totalRisks += task.risks.length;
          highRisks += task.risks.filter(r => r.severity === 'high').length;
          criticalRisks += task.risks.filter(r => r.severity === 'critical').length;
        }
      });
      setRiskData({total: totalRisks, high: highRisks, critical: criticalRisks});

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  }, []);

  // Load data when component mounts and lang is available
  useEffect(() => {
    if (lang) {
      const loadData = async () => {
        setIsLoading(true);
        await Promise.all([
          loadTasks(),
          loadConsultantsCount(),
          loadDashboardData()
        ]);
        setIsLoading(false);
      };
      loadData();
    }
  }, [lang, loadTasks, loadConsultantsCount, loadDashboardData]);

  const handleCreateTask = async (taskData: {
    title: string;
    description: string;
    priority: TaskPriority;
    assignees: TaskAssignee[];
    dueDate?: Date;
    tags?: string[];
  }) => {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    try {
      setIsCreatingTask(true);
      
      const newTask = {
        ...taskData,
        status: TaskStatus.PENDING,
        createdBy: currentUser.uid,
        createdByName: currentUser.displayName || currentUser.email || 'Unknown User',
        dueDate: taskData.dueDate ? Timestamp.fromDate(taskData.dueDate) : undefined
      };

      await TasksService.createTask(newTask);
      await Promise.all([loadTasks(), loadDashboardData()]); // Refresh tasks list and dashboard data
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم إنشاء المهمة بنجاح" : "Task created successfully",
        description: isRTL ? `تم إنشاء المهمة "${taskData.title}"` : `Task "${taskData.title}" has been created`,
      });
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    } finally {
      setIsCreatingTask(false);
    }
  };

  const handleEditTask = (task: Task, event: React.MouseEvent) => {
    event.stopPropagation();
    setSelectedTask(task);
    setIsEditModalOpen(true);
  };

  const handleDeleteTask = (task: Task, event: React.MouseEvent) => {
    event.stopPropagation();
    setTaskToDelete(task);
    setIsDeleteModalOpen(true);
  };

  const confirmDeleteTask = async () => {
    if (!taskToDelete) return;

    try {
      setIsDeleting(true);
      await TasksService.deleteTask(taskToDelete.id!);
      await Promise.all([loadTasks(), loadDashboardData()]); // Refresh tasks list and dashboard data
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم حذف المهمة بنجاح" : "Task deleted successfully",
        description: isRTL ? `تم حذف المهمة "${taskToDelete.title}"` : `Task "${taskToDelete.title}" has been deleted`,
      });
      
      setIsDeleteModalOpen(false);
      setTaskToDelete(null);
    } catch (error) {
      console.error('Error deleting task:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في حذف المهمة" : "Error deleting task",
        description: isRTL ? "فشل في حذف المهمة" : "Failed to delete task",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleEditTaskSubmit = async (taskData: {
    title: string;
    description: string;
    priority: TaskPriority;
    assignees: TaskAssignee[];
    dueDate?: Date;
    tags?: string[];
  }) => {
    if (!selectedTask) return;

    try {
      setIsEditingTask(true);

      const updates: Partial<Task> = {
        title: taskData.title,
        description: taskData.description,
        priority: taskData.priority,
        assignees: taskData.assignees,
        dueDate: taskData.dueDate ? Timestamp.fromDate(taskData.dueDate) : undefined,
        tags: taskData.tags
      };

      await TasksService.updateTask(selectedTask.id!, updates);
      await Promise.all([loadTasks(), loadDashboardData()]); // Refresh tasks list and dashboard data

      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم تحديث المهمة بنجاح" : "Task updated successfully",
        description: isRTL ? `تم تحديث المهمة "${taskData.title}"` : `Task "${taskData.title}" has been updated`,
      });

      setIsEditModalOpen(false);
      setSelectedTask(null);
    } catch (error) {
      console.error('Error updating task:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في التحديث" : "Update error",
        description: isRTL ? "فشل في تحديث المهمة" : "Failed to update task",
        variant: "destructive",
      });
    } finally {
      setIsEditingTask(false);
    }
  };

  // Export Comprehensive Report with Gantt-style visualization
  const exportComprehensiveReport = async () => {
    try {
      setIsExportingReport(true);
      const isRTL = lang === "ar";

      // Fetch all systems data
      const allSystems = await SystemsService.getSystems();

      // Create workbook
      const workbook = new ExcelJS.Workbook();
      workbook.creator = 'Thiqah Platform';
      workbook.created = new Date();

      // Define Thiqah brand colors
      const thiqahBlue = '23A9DB';
      const thiqahDarkGray = '3D3D45';
      const thiqahWhite = 'FFFFFF';

      // Welcome Sheet
      const welcomeSheet = workbook.addWorksheet(isRTL ? 'مرحباً' : 'Welcome');

      // Welcome sheet styling
      welcomeSheet.mergeCells('A1:H12');
      const welcomeCell = welcomeSheet.getCell('A1');
      welcomeCell.value = isRTL
        ? `تقرير شامل لإدارة المهام والأنظمة
منصة ثقة للاستشارات

📊 هذا التقرير الشامل يحتوي على:
• جميع المهام المسجلة في النظام مع تفاصيلها الكاملة
• حالة مهام الأنظمة مع مصفوفة التقدم
• مخطط جانت للمتابعة الزمنية
• إحصائيات مفصلة للأداء

تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}
الوقت: ${new Date().toLocaleTimeString('ar-SA')}

© منصة ثقة للاستشارات - جميع الحقوق محفوظة`
        : `Comprehensive Task & System Management Report
Thiqah Consultancy Platform

📊 This comprehensive report contains:
• All registered tasks in the system with complete details
• System task status with progress matrix
• Gantt chart for timeline tracking
• Detailed performance analytics

Export Date: ${new Date().toLocaleDateString('en-US')}
Time: ${new Date().toLocaleTimeString('en-US')}

© Thiqah Consultancy Platform - All Rights Reserved`;

      welcomeCell.font = { size: 14, bold: true, color: { argb: thiqahDarkGray } };
      welcomeCell.alignment = { vertical: 'middle', horizontal: 'center', wrapText: true };
      welcomeCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'F0F9FF' } };
      welcomeCell.border = {
        top: { style: 'thick', color: { argb: thiqahBlue } },
        left: { style: 'thick', color: { argb: thiqahBlue } },
        bottom: { style: 'thick', color: { argb: thiqahBlue } },
        right: { style: 'thick', color: { argb: thiqahBlue } }
      };

      // Task types for system tracking - Only include task types that are defined in the System interface
      const taskTypes = [
        { key: 'initialClassificationStatus', label: { en: 'Initial Classification', ar: 'التصنيف الأولي' } },
        { key: 'furtherClassificationStatus', label: { en: 'Further Classification', ar: 'التصنيف المتقدم' } },
        { key: 'classificationReviewStatus', label: { en: 'Classification Review', ar: 'مراجعة التصنيف' } }
        // Note: ropaStatus, dpiaStatus, tiaStatus, and privacyPolicyStatus are not yet defined in the System interface
        // { key: 'ropaStatus', label: { en: 'RoPA', ar: 'سجل أنشطة المعالجة' } },
        // { key: 'dpiaStatus', label: { en: 'DPIA', ar: 'تقييم أثر حماية البيانات' } },
        // { key: 'tiaStatus', label: { en: 'TIA', ar: 'تقييم الأثر التقني' } },
        // { key: 'privacyPolicyStatus', label: { en: 'Privacy Policy', ar: 'سياسة الخصوصية' } }
      ];

      // Gantt Chart Sheet
      const ganttSheet = workbook.addWorksheet(isRTL ? 'مخطط جانت' : 'Gantt Chart');

      // Create timeline headers (next 12 months)
      const today = new Date();
      const timelineMonths: { label: string; date: Date }[] = [];
      for (let i = 0; i < 12; i++) {
        const month = new Date(today.getFullYear(), today.getMonth() + i, 1);
        timelineMonths.push({
          label: month.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', { month: 'short', year: 'numeric' }),
          date: month
        });
      }

      // Gantt headers
      const ganttHeaders = [
        isRTL ? 'النظام' : 'System',
        isRTL ? 'المهمة' : 'Task',
        isRTL ? 'الحالة' : 'Status',
        isRTL ? 'التقدم %' : 'Progress %',
        isRTL ? 'التاريخ المتوقع' : 'Expected Date',
        isRTL ? 'تاريخ البدء' : 'Start Date',
        isRTL ? 'تاريخ الانتهاء' : 'End Date',
        isRTL ? 'التأخير (أيام)' : 'Delay (Days)',
        isRTL ? 'حالة التوقيت' : 'Timeline Status',
        ...timelineMonths.map(m => m.label)
      ];

      ganttSheet.addRow(ganttHeaders);

      // Style Gantt headers
      const ganttHeaderRow = ganttSheet.getRow(1);
      ganttHeaderRow.font = { bold: true, color: { argb: thiqahWhite }, size: 11 };
      ganttHeaderRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahBlue } };
      ganttHeaderRow.alignment = { vertical: 'middle', horizontal: 'center' };
      ganttHeaderRow.height = 35;

      // Add Gantt data for each system and task
      let rowIndex = 2;
      allSystems.forEach((system) => {
        taskTypes.forEach((taskType) => {
          const baseFieldName = taskType.key.replace('Status', '');
          const status = getSystemTaskStatus(system, taskType.key);
          const progress = (system[`${baseFieldName}Progress` as keyof System] as number) ?? 0;
          const startDate = system[`${baseFieldName}StartDate` as keyof System] as Timestamp | undefined;
          const endDate = system[`${baseFieldName}EndDate` as keyof System] as Timestamp | undefined;
          const expectedDate = system[`${baseFieldName}ExpectedDate` as keyof System] as Timestamp | undefined;

          const statusText = status === SystemTaskStatus.DONE ? (isRTL ? 'مكتمل' : 'Done') :
                            isStatusPending(status) ? (isRTL ? 'معلق' : 'Pending') :
                            (isRTL ? 'لم يبدأ' : 'Not Started');

          const expectedDateText = expectedDate ? timestampToDate(expectedDate)?.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') || '' : '';
          const startDateText = startDate ? timestampToDate(startDate)?.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') || '' : '';
          const endDateText = endDate ? timestampToDate(endDate)?.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') || '' : '';

          // Calculate delay and timeline status
          let delayDays = '';
          let timelineStatus = '';

          if (expectedDate) {
            const expectedDateObj = timestampToDate(expectedDate);
            const today = new Date();

            if (status === SystemTaskStatus.DONE && endDate && expectedDateObj) {
              const endDateObj = timestampToDate(endDate);
              if (endDateObj) {
                const diffTime = endDateObj.getTime() - expectedDateObj.getTime();
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                if (diffDays > 0) {
                  delayDays = `+${diffDays}`;
                  timelineStatus = isRTL ? 'متأخر' : 'Delayed';
                } else if (diffDays < 0) {
                  delayDays = `${diffDays}`;
                  timelineStatus = isRTL ? 'مبكر' : 'Early';
                } else {
                  delayDays = '0';
                  timelineStatus = isRTL ? 'في الوقت المحدد' : 'On Time';
                }
              }
            } else if ((status !== SystemTaskStatus.NOT_STARTED && status !== SystemTaskStatus.DONE) && expectedDateObj) {
              const diffTime = today.getTime() - expectedDateObj.getTime();
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

              if (diffDays > 0) {
                delayDays = `+${diffDays}`;
                timelineStatus = isRTL ? 'متأخر' : 'Overdue';
              } else if (diffDays >= -7) {
                delayDays = `${diffDays}`;
                timelineStatus = isRTL ? 'قريب من الموعد' : 'Due Soon';
              } else {
                delayDays = `${diffDays}`;
                timelineStatus = isRTL ? 'في الوقت المحدد' : 'On Track';
              }
            } else {
              timelineStatus = isRTL ? 'لم يبدأ' : 'Not Started';
            }
          }

          // Create timeline visualization
          const timelineData = timelineMonths.map(month => {
            if (!startDate) return '';

            const taskStart = timestampToDate(startDate);
            const taskEnd = endDate ? timestampToDate(endDate) : (taskStart ? new Date(taskStart.getTime() + 30 * 24 * 60 * 60 * 1000) : null); // Default 30 days

            if (!taskStart) return '';
            const monthStart = month.date;
            const monthEnd = new Date(monthStart.getFullYear(), monthStart.getMonth() + 1, 0);

            // Check if task overlaps with this month
            if (taskStart <= monthEnd && taskEnd && taskEnd >= monthStart) {
              if (status === SystemTaskStatus.DONE) return '█████'; // Full block for completed
              if (isStatusPending(status)) {
                const progressBlocks = Math.floor(progress / 20); // 5 blocks = 100%
                return '█'.repeat(progressBlocks) + '░'.repeat(5 - progressBlocks);
              }
              return '░░░░░'; // Light blocks for not started
            }
            return '';
          });

          ganttSheet.addRow([
            system.name,
            taskType.label[isRTL ? 'ar' : 'en'],
            statusText,
            progress,
            expectedDateText,
            startDateText,
            endDateText,
            delayDays,
            timelineStatus,
            ...timelineData
          ]);

          // Style the row
          const row = ganttSheet.getRow(rowIndex);
          row.alignment = { vertical: 'middle', wrapText: true };
          row.height = 25;

          // Color code based on status
          if (status === SystemTaskStatus.DONE) {
            row.getCell(3).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'DCFCE7' } };
            row.getCell(3).font = { color: { argb: '166534' }, bold: true };
          } else if (isStatusPending(status)) {
            row.getCell(3).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEF3C7' } };
            row.getCell(3).font = { color: { argb: 'A16207' }, bold: true };
          } else {
            row.getCell(3).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'F3F4F6' } };
            row.getCell(3).font = { color: { argb: '6B7280' }, bold: true };
          }

          // Color code timeline status
          const timelineStatusCell = row.getCell(9);
          if (timelineStatus.includes(isRTL ? 'متأخر' : 'Delayed') || timelineStatus.includes(isRTL ? 'متأخر' : 'Overdue')) {
            timelineStatusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEE2E2' } };
            timelineStatusCell.font = { color: { argb: 'DC2626' }, bold: true };
          } else if (timelineStatus.includes(isRTL ? 'قريب من الموعد' : 'Due Soon')) {
            timelineStatusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEF3C7' } };
            timelineStatusCell.font = { color: { argb: 'A16207' }, bold: true };
          } else if (timelineStatus.includes(isRTL ? 'في الوقت المحدد' : 'On Time') || timelineStatus.includes(isRTL ? 'مبكر' : 'Early')) {
            timelineStatusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'DCFCE7' } };
            timelineStatusCell.font = { color: { argb: '166534' }, bold: true };
          }

          // Color code delay days
          const delayCell = row.getCell(8);
          if (delayDays.startsWith('+')) {
            delayCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEE2E2' } };
            delayCell.font = { color: { argb: 'DC2626' }, bold: true };
          } else if (delayDays.startsWith('-')) {
            delayCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'DCFCE7' } };
            delayCell.font = { color: { argb: '166534' }, bold: true };
          }

          // Color timeline cells
          timelineData.forEach((data, index) => {
            const cell = row.getCell(7 + index);
            cell.font = { name: 'Courier New', size: 8 };
            if (data.includes('█')) {
              if (status === SystemTaskStatus.DONE) {
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'DCFCE7' } };
                cell.font = { ...cell.font, color: { argb: '166534' } };
              } else {
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'DBEAFE' } };
                cell.font = { ...cell.font, color: { argb: '1E40AF' } };
              }
            }
          });

          rowIndex++;
        });
      });

      // Auto-fit Gantt columns
      ganttSheet.columns.forEach((column, index) => {
        if (index === 0) column.width = 25; // System name
        else if (index === 1) column.width = 20; // Task name
        else if (index === 2) column.width = 12; // Status
        else if (index === 3) column.width = 10; // Progress
        else if (index === 4) column.width = 15; // Expected date
        else if (index === 5 || index === 6) column.width = 15; // Start/End dates
        else if (index === 7) column.width = 12; // Delay days
        else if (index === 8) column.width = 15; // Timeline status
        else column.width = 12; // Timeline months
      });

      // System Status Matrix Sheet
      const statusSheet = workbook.addWorksheet(isRTL ? 'مصفوفة الحالة' : 'Status Matrix');

      // Status matrix headers
      const statusHeaders = [
        isRTL ? 'اسم النظام' : 'System Name',
        isRTL ? 'المسؤول' : 'Owner',
        isRTL ? 'البريد الإلكتروني' : 'Email',
        ...taskTypes.map(t => t.label[isRTL ? 'ar' : 'en'])
      ];

      statusSheet.addRow(statusHeaders);

      // Style status headers
      const statusHeaderRow = statusSheet.getRow(1);
      statusHeaderRow.font = { bold: true, color: { argb: thiqahWhite }, size: 11 };
      statusHeaderRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahBlue } };
      statusHeaderRow.alignment = { vertical: 'middle', horizontal: 'center' };
      statusHeaderRow.height = 30;

      // Add status matrix data
      allSystems.forEach((system, systemIndex) => {
        const statusValues = taskTypes.map(taskType => {
          const status = getSystemTaskStatus(system, taskType.key);
          return status === SystemTaskStatus.DONE ? (isRTL ? '✅ مكتمل' : '✅ Done') :
                 isStatusPending(status) ? (isRTL ? '🟡 معلق' : '🟡 Pending') :
                 (isRTL ? '⭕ لم يبدأ' : '⭕ Not Started');
        });

        statusSheet.addRow([
          system.name,
          system.responsibleOwner,
          system.email,
          ...statusValues
        ]);

        // Style status row
        const row = statusSheet.getRow(systemIndex + 2);
        row.alignment = { vertical: 'middle', wrapText: true };
        row.height = 25;

        // Color code status cells
        taskTypes.forEach((taskType, taskIndex) => {
          const cellIndex = taskIndex + 4;
          const cell = row.getCell(cellIndex);
          const status = getSystemTaskStatus(system, taskType.key);

          if (status === SystemTaskStatus.DONE) {
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'DCFCE7' } };
            cell.font = { color: { argb: '166534' }, bold: true };
          } else if (isStatusPending(status)) {
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEF3C7' } };
            cell.font = { color: { argb: 'A16207' }, bold: true };
          } else {
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'F3F4F6' } };
            cell.font = { color: { argb: '6B7280' }, bold: true };
          }
        });
      });

      // Auto-fit status columns
      statusSheet.columns.forEach((column, index) => {
        if (index === 0) column.width = 30; // System name
        else if (index === 1) column.width = 25; // Owner
        else if (index === 2) column.width = 30; // Email
        else column.width = 18; // Status columns
      });

      // Progress Details Sheet
      const progressSheet = workbook.addWorksheet(isRTL ? 'تفاصيل التقدم' : 'Progress Details');

      // Progress headers
      const progressHeaders = [
        isRTL ? 'النظام' : 'System',
        isRTL ? 'المهمة' : 'Task',
        isRTL ? 'التقدم %' : 'Progress %',
        isRTL ? 'التاريخ المتوقع' : 'Expected Date',
        isRTL ? 'تاريخ البدء' : 'Start Date',
        isRTL ? 'تاريخ الانتهاء' : 'End Date',
        isRTL ? 'المدة (أيام)' : 'Duration (Days)',
        isRTL ? 'التأخير (أيام)' : 'Delay (Days)',
        isRTL ? 'حالة التوقيت' : 'Timeline Status',
        isRTL ? 'الحالة' : 'Status'
      ];

      progressSheet.addRow(progressHeaders);

      // Style progress headers
      const progressHeaderRow = progressSheet.getRow(1);
      progressHeaderRow.font = { bold: true, color: { argb: thiqahWhite }, size: 11 };
      progressHeaderRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahBlue } };
      progressHeaderRow.alignment = { vertical: 'middle', horizontal: 'center' };
      progressHeaderRow.height = 30;

      // Add progress details
      let progressRowIndex = 2;
      allSystems.forEach((system) => {
        taskTypes.forEach((taskType) => {
          const baseFieldName = taskType.key.replace('Status', '');
          const status = system[taskType.key as keyof System] as SystemTaskStatus || SystemTaskStatus.NOT_STARTED;
          const progress = (system[`${baseFieldName}Progress` as keyof System] as number) ?? 0;
          const startDate = system[`${baseFieldName}StartDate` as keyof System] as Timestamp | undefined;
          const endDate = system[`${baseFieldName}EndDate` as keyof System] as Timestamp | undefined;
          const expectedDate = system[`${baseFieldName}ExpectedDate` as keyof System] as Timestamp | undefined;

          const expectedDateText = expectedDate ? timestampToDate(expectedDate)?.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') || '' : '';
          const startDateText = startDate ? timestampToDate(startDate)?.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') || '' : '';
          const endDateText = endDate ? timestampToDate(endDate)?.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') || '' : '';

          // Calculate duration
          let duration = '';
          if (startDate && endDate) {
            const start = timestampToDate(startDate);
            const end = timestampToDate(endDate);
            if (start && end) {
              const diffTime = Math.abs(end.getTime() - start.getTime());
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
              duration = diffDays.toString();
            }
          }

          // Calculate delay and timeline status (same logic as Gantt)
          let delayDays = '';
          let timelineStatus = '';

          if (expectedDate) {
            const expectedDateObj = timestampToDate(expectedDate);
            const today = new Date();

            if (status === SystemTaskStatus.DONE && endDate && expectedDateObj) {
              const endDateObj = timestampToDate(endDate);
              if (endDateObj) {
                const diffTime = endDateObj.getTime() - expectedDateObj.getTime();
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                if (diffDays > 0) {
                  delayDays = `+${diffDays}`;
                  timelineStatus = isRTL ? 'متأخر' : 'Delayed';
                } else if (diffDays < 0) {
                  delayDays = `${diffDays}`;
                  timelineStatus = isRTL ? 'مبكر' : 'Early';
                } else {
                  delayDays = '0';
                  timelineStatus = isRTL ? 'في الوقت المحدد' : 'On Time';
                }
              }
            } else if (isStatusPending(status) && expectedDateObj) {
              const diffTime = today.getTime() - expectedDateObj.getTime();
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

              if (diffDays > 0) {
                delayDays = `+${diffDays}`;
                timelineStatus = isRTL ? 'متأخر' : 'Overdue';
              } else if (diffDays >= -7) {
                delayDays = `${diffDays}`;
                timelineStatus = isRTL ? 'قريب من الموعد' : 'Due Soon';
              } else {
                delayDays = `${diffDays}`;
                timelineStatus = isRTL ? 'في الوقت المحدد' : 'On Track';
              }
            } else {
              timelineStatus = isRTL ? 'لم يبدأ' : 'Not Started';
            }
          }

          const statusText = status === SystemTaskStatus.DONE ? (isRTL ? 'مكتمل' : 'Done') :
                            isStatusPending(status) ? (isRTL ? 'معلق' : 'Pending') :
                            (isRTL ? 'لم يبدأ' : 'Not Started');

          progressSheet.addRow([
            system.name,
            taskType.label[isRTL ? 'ar' : 'en'],
            progress,
            expectedDateText,
            startDateText,
            endDateText,
            duration,
            delayDays,
            timelineStatus,
            statusText
          ]);

          // Style progress row
          const row = progressSheet.getRow(progressRowIndex);
          row.alignment = { vertical: 'middle', wrapText: true };
          row.height = 25;

          // Color code progress cell
          const progressCell = row.getCell(3);
          if (progress >= 100) {
            progressCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'DCFCE7' } };
            progressCell.font = { color: { argb: '166534' }, bold: true };
          } else if (progress >= 75) {
            progressCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'DBEAFE' } };
            progressCell.font = { color: { argb: '1E40AF' }, bold: true };
          } else if (progress >= 50) {
            progressCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEF3C7' } };
            progressCell.font = { color: { argb: 'A16207' }, bold: true };
          } else if (progress >= 25) {
            progressCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FED7AA' } };
            progressCell.font = { color: { argb: 'C2410C' }, bold: true };
          } else {
            progressCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEE2E2' } };
            progressCell.font = { color: { argb: 'DC2626' }, bold: true };
          }

          // Color code timeline status
          const timelineStatusCell = row.getCell(9);
          if (timelineStatus.includes(isRTL ? 'متأخر' : 'Delayed') || timelineStatus.includes(isRTL ? 'متأخر' : 'Overdue')) {
            timelineStatusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEE2E2' } };
            timelineStatusCell.font = { color: { argb: 'DC2626' }, bold: true };
          } else if (timelineStatus.includes(isRTL ? 'قريب من الموعد' : 'Due Soon')) {
            timelineStatusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEF3C7' } };
            timelineStatusCell.font = { color: { argb: 'A16207' }, bold: true };
          } else if (timelineStatus.includes(isRTL ? 'في الوقت المحدد' : 'On Time') || timelineStatus.includes(isRTL ? 'مبكر' : 'Early')) {
            timelineStatusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'DCFCE7' } };
            timelineStatusCell.font = { color: { argb: '166534' }, bold: true };
          }

          // Color code delay days
          const delayCell = row.getCell(8);
          if (delayDays.startsWith('+')) {
            delayCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEE2E2' } };
            delayCell.font = { color: { argb: 'DC2626' }, bold: true };
          } else if (delayDays.startsWith('-')) {
            delayCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'DCFCE7' } };
            delayCell.font = { color: { argb: '166534' }, bold: true };
          }

          progressRowIndex++;
        });
      });

      // Auto-fit progress columns
      progressSheet.columns.forEach((column, index) => {
        if (index === 0) column.width = 25; // System
        else if (index === 1) column.width = 20; // Task
        else if (index === 2) column.width = 12; // Progress
        else if (index === 3) column.width = 15; // Expected date
        else if (index === 4 || index === 5) column.width = 15; // Start/End dates
        else if (index === 6) column.width = 12; // Duration
        else if (index === 7) column.width = 12; // Delay days
        else if (index === 8) column.width = 15; // Timeline status
        else column.width = 15; // Status
      });

      // Tasks Sheet
      const tasksSheet = workbook.addWorksheet(isRTL ? 'المهام' : 'Tasks');

      // Task headers
      const taskHeaders = isRTL
        ? ['العنوان', 'الوصف', 'الحالة', 'الأولوية', 'المكلفون', 'تاريخ الإنشاء', 'تاريخ الاستحقاق', 'التقدم %', 'العلامات']
        : ['Title', 'Description', 'Status', 'Priority', 'Assignees', 'Created Date', 'Due Date', 'Progress %', 'Tags'];

      tasksSheet.addRow(taskHeaders);

      // Style task headers
      const taskHeaderRow = tasksSheet.getRow(1);
      taskHeaderRow.font = { bold: true, color: { argb: thiqahWhite }, size: 11 };
      taskHeaderRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahBlue } };
      taskHeaderRow.alignment = { vertical: 'middle', horizontal: 'center' };
      taskHeaderRow.height = 30;

      // Add task data
      tasks.forEach((task, taskIndex) => {
        const statusText = task.status === TaskStatus.PENDING ? (isRTL ? "⏳ معلقة" : "⏳ Pending") :
                          task.status === TaskStatus.IN_PROGRESS ? (isRTL ? "🔄 قيد التنفيذ" : "🔄 In Progress") :
                          task.status === TaskStatus.COMPLETED ? (isRTL ? "✅ مكتملة" : "✅ Completed") :
                          (isRTL ? "❌ ملغية" : "❌ Cancelled");

        const priorityText = task.priority === TaskPriority.LOW ? (isRTL ? "🟢 منخفضة" : "🟢 Low") :
                            task.priority === TaskPriority.MEDIUM ? (isRTL ? "🟡 متوسطة" : "🟡 Medium") :
                            task.priority === TaskPriority.HIGH ? (isRTL ? "🟠 عالية" : "🟠 High") :
                            (isRTL ? "🔴 عاجلة" : "🔴 Urgent");

        const assigneesText = task.assignees.map(a => a.displayName || a.email).join(', ');
        const createdDate = task.createdAt.toDate().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US');
        const dueDate = task.dueDate ? task.dueDate.toDate().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') : '';
        const tagsText = task.tags ? task.tags.join(', ') : '';

        tasksSheet.addRow([
          task.title,
          task.description,
          statusText,
          priorityText,
          assigneesText,
          createdDate,
          dueDate,
          task.progress || 0,
          tagsText
        ]);

        // Style task row
        const row = tasksSheet.getRow(taskIndex + 2);
        row.alignment = { vertical: 'middle', wrapText: true };
        row.height = 25;

        // Color code status and priority
        const statusCell = row.getCell(3);
        const priorityCell = row.getCell(4);

        // Status colors
        if (task.status === TaskStatus.COMPLETED) {
          statusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'DCFCE7' } };
          statusCell.font = { color: { argb: '166534' }, bold: true };
        } else if (task.status === TaskStatus.IN_PROGRESS) {
          statusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'DBEAFE' } };
          statusCell.font = { color: { argb: '1E40AF' }, bold: true };
        } else if (task.status === TaskStatus.PENDING) {
          statusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEF3C7' } };
          statusCell.font = { color: { argb: 'A16207' }, bold: true };
        }

        // Priority colors
        if (task.priority === TaskPriority.URGENT) {
          priorityCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEE2E2' } };
          priorityCell.font = { color: { argb: 'DC2626' }, bold: true };
        } else if (task.priority === TaskPriority.HIGH) {
          priorityCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FED7AA' } };
          priorityCell.font = { color: { argb: 'C2410C' }, bold: true };
        } else if (task.priority === TaskPriority.MEDIUM) {
          priorityCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEF3C7' } };
          priorityCell.font = { color: { argb: 'A16207' }, bold: true };
        }
      });

      // Auto-fit task columns
      tasksSheet.columns.forEach((column, index) => {
        if (index === 0) column.width = 30; // Title
        else if (index === 1) column.width = 40; // Description
        else if (index === 4) column.width = 25; // Assignees
        else if (index === 8) column.width = 20; // Tags
        else column.width = 15;
      });

      // Analytics Sheet
      const analyticsSheet = workbook.addWorksheet(isRTL ? 'التحليلات' : 'Analytics');

      // Calculate analytics
      const totalSystems = allSystems.length;
      const totalTasks = tasks.length;

      // System analytics
      const systemStats = {
        completed: 0,
        pending: 0,
        notStarted: 0,
        totalProgress: 0
      };

      allSystems.forEach(system => {
        taskTypes.forEach(taskType => {
          const status = getSystemTaskStatus(system, taskType.key);
          const baseFieldName = taskType.key.replace('Status', '');
          const progress = (system[`${baseFieldName}Progress` as keyof System] as number) ?? 0;

          if (status === SystemTaskStatus.DONE) systemStats.completed++;
          else if (isStatusPending(status)) systemStats.pending++;
          else systemStats.notStarted++;

          systemStats.totalProgress += progress;
        });
      });

      const totalSystemTasks = totalSystems * taskTypes.length;
      const avgProgress = totalSystemTasks > 0 ? systemStats.totalProgress / totalSystemTasks : 0;

      // Task analytics
      const taskStats = {
        completed: tasks.filter(t => t.status === TaskStatus.COMPLETED).length,
        inProgress: tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length,
        pending: tasks.filter(t => t.status === TaskStatus.PENDING).length,
        cancelled: tasks.filter(t => t.status === TaskStatus.CANCELLED).length
      };

      // Add analytics data
      analyticsSheet.mergeCells('A1:D1');
      const analyticsTitle = analyticsSheet.getCell('A1');
      analyticsTitle.value = isRTL ? '📊 تحليلات شاملة للأداء' : '📊 Comprehensive Performance Analytics';
      analyticsTitle.font = { size: 16, bold: true, color: { argb: thiqahDarkGray } };
      analyticsTitle.alignment = { horizontal: 'center', vertical: 'middle' };
      analyticsTitle.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'F0F9FF' } };

      // System analytics section
      analyticsSheet.getCell('A3').value = isRTL ? 'إحصائيات الأنظمة:' : 'System Statistics:';
      analyticsSheet.getCell('A3').font = { bold: true, size: 12, color: { argb: thiqahBlue } };

      analyticsSheet.getCell('A4').value = isRTL ? 'إجمالي الأنظمة:' : 'Total Systems:';
      analyticsSheet.getCell('B4').value = totalSystems;
      analyticsSheet.getCell('A5').value = isRTL ? 'إجمالي مهام الأنظمة:' : 'Total System Tasks:';
      analyticsSheet.getCell('B5').value = totalSystemTasks;
      analyticsSheet.getCell('A6').value = isRTL ? 'المهام المكتملة:' : 'Completed Tasks:';
      analyticsSheet.getCell('B6').value = systemStats.completed;
      analyticsSheet.getCell('A7').value = isRTL ? 'المهام المعلقة:' : 'Pending Tasks:';
      analyticsSheet.getCell('B7').value = systemStats.pending;
      analyticsSheet.getCell('A8').value = isRTL ? 'المهام غير المبدوءة:' : 'Not Started Tasks:';
      analyticsSheet.getCell('B8').value = systemStats.notStarted;
      analyticsSheet.getCell('A9').value = isRTL ? 'متوسط التقدم:' : 'Average Progress:';
      analyticsSheet.getCell('B9').value = `${avgProgress.toFixed(1)}%`;

      // Task analytics section
      analyticsSheet.getCell('A11').value = isRTL ? 'إحصائيات المهام العامة:' : 'General Task Statistics:';
      analyticsSheet.getCell('A11').font = { bold: true, size: 12, color: { argb: thiqahBlue } };

      analyticsSheet.getCell('A12').value = isRTL ? 'إجمالي المهام:' : 'Total Tasks:';
      analyticsSheet.getCell('B12').value = totalTasks;
      analyticsSheet.getCell('A13').value = isRTL ? 'المهام المكتملة:' : 'Completed:';
      analyticsSheet.getCell('B13').value = taskStats.completed;
      analyticsSheet.getCell('A14').value = isRTL ? 'قيد التنفيذ:' : 'In Progress:';
      analyticsSheet.getCell('B14').value = taskStats.inProgress;
      analyticsSheet.getCell('A15').value = isRTL ? 'معلقة:' : 'Pending:';
      analyticsSheet.getCell('B15').value = taskStats.pending;
      analyticsSheet.getCell('A16').value = isRTL ? 'ملغية:' : 'Cancelled:';
      analyticsSheet.getCell('B16').value = taskStats.cancelled;

      // Style analytics
      for (let i = 4; i <= 16; i++) {
        const labelCell = analyticsSheet.getCell(`A${i}`);
        const valueCell = analyticsSheet.getCell(`B${i}`);

        labelCell.font = { bold: true };
        valueCell.font = { bold: true, color: { argb: thiqahBlue } };

        if (i % 2 === 0) {
          labelCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'F8FAFC' } };
          valueCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'F8FAFC' } };
        }
      }

      // Auto-fit analytics columns
      analyticsSheet.getColumn('A').width = 25;
      analyticsSheet.getColumn('B').width = 15;

      // Generate and download
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Thiqah_Comprehensive_Report_${new Date().toISOString().split('T')[0]}.xlsx`;
      link.click();
      window.URL.revokeObjectURL(url);

      toast({
        title: isRTL ? "تم تصدير التقرير الشامل بنجاح" : "Comprehensive report exported successfully",
        description: isRTL ? "تم تحميل ملف Excel مع جميع التفاصيل" : "Excel file with all details downloaded",
      });

    } catch (error) {
      console.error('Error exporting comprehensive report:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في التصدير" : "Export error",
        description: isRTL ? "فشل في تصدير التقرير الشامل" : "Failed to export comprehensive report",
        variant: "destructive",
      });
    } finally {
      setIsExportingReport(false);
    }
  };



  // Don't render until params are loaded
  if (!lang) {
    return <div>Loading...</div>;
  }

  const isRTL = lang === "ar";

  const tabs = [
    {
      id: 'overview' as const,
      label: isRTL ? "نظرة عامة" : "Overview",
      subtitle: isRTL ? "الإحصائيات والملخص" : "Statistics & Summary",
      icon: BarChart3
    },
    {
      id: 'management' as const,
      label: isRTL ? "إدارة المهام" : "Task Management",
      subtitle: isRTL ? "إنشاء وإدارة المهام" : "Create & Manage Tasks",
      icon: Settings
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
        </div>

        <div className="relative z-10 px-8 py-8">
          {/* System Header */}
          <div className="flex items-center gap-6 mb-6">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center shadow-lg border border-white/20">
              <ClipboardList className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-1 tracking-tight">
                {isRTL ? "إدارة المهام" : "Task Management"}
              </h1>
              <p className="text-white/90 text-lg">
                {isRTL ? "إدارة وتتبع المهام بكفاءة" : "Efficient Task Management & Tracking"}
              </p>
            </div>
          </div>

          {/* Task Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              { 
                icon: CheckSquare, 
                label: isRTL ? "المهام النشطة" : "Active Tasks", 
                value: isLoading ? "..." : taskStats.pending.toString(),
                subValue: isRTL ? `${taskStats.inProgress} قيد التنفيذ` : `${taskStats.inProgress} in progress`
              },
              { 
                icon: Calendar, 
                label: isRTL ? "إجمالي المهام" : "Total Tasks", 
                value: isLoading ? "..." : taskStats.total.toString(),
                subValue: isRTL ? `${taskStats.completed} مكتملة` : `${taskStats.completed} completed`
              },
              { 
                icon: Users, 
                label: isRTL ? "الاستشاريون" : "Consultants", 
                value: isLoading ? "..." : consultantsCount.toString(),
                subValue: isRTL ? "أعضاء الفريق" : "Team Members"
              }
            ].map((item, index) => (
              <div
                key={index}
                className="bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-300"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-white/20 text-white">
                    <item.icon className="w-4 h-4" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="text-xs font-semibold text-white/80 uppercase tracking-wider">{item.label}</div>
                    <div className="text-sm font-bold text-white truncate" title={item.value}>{item.value}</div>
                    <div className="text-xs text-white/70">{item.subValue}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-8 py-8">
        {/* System Tasks Status Button */}
        <div className="flex justify-center mb-6">
          <Button
            onClick={() => router.push(`/${lang}/Thiqah/Tasks/Systems`)}
            className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 hover:from-[var(--brand-blue)]/90 hover:to-[var(--brand-blue)]/70 text-white shadow-lg px-8 py-3 rounded-xl"
          >
            <Activity className="w-5 h-5 mr-2" />
            {isRTL ? "حالة مهام الأنظمة" : "System Tasks Status"}
          </Button>
        </div>

        {/* Comprehensive Export Button */}
        <div className="flex justify-center mb-6">
          <Button
            onClick={exportComprehensiveReport}
            disabled={isExportingReport}
            className="bg-gradient-to-r from-emerald-600 to-[var(--brand-blue)] hover:from-emerald-700 hover:to-[var(--brand-blue)]/90 text-white shadow-lg px-8 py-3 rounded-xl"
          >
            {isExportingReport ? (
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {isRTL ? "جاري إنشاء التقرير..." : "Generating Report..."}
              </div>
            ) : (
              <>
                <Download className="w-5 h-5 mr-2" />
                {isRTL ? "تصدير التقرير الشامل" : "Export Comprehensive Report"}
              </>
            )}
          </Button>
        </div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-2xl p-1 shadow-lg border border-gray-200">
            <div className="flex gap-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-6 py-3 rounded-xl transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-[var(--brand-blue)] text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  <span className="font-medium">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="max-w-7xl mx-auto">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              {isLoading ? (
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
              <div className="text-center py-16">
                    <div className="w-12 h-12 border-4 border-[var(--brand-blue)] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-gray-600">
                      {isRTL ? "جاري تحميل البيانات..." : "Loading dashboard data..."}
                    </p>
                </div>
                </div>
              ) : (
                <>
                  {/* Quick Stats Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {/* Total Tasks */}
                    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300">
                      <div className="flex items-center justify-between mb-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 rounded-xl flex items-center justify-center">
                          <CheckSquare className="w-6 h-6 text-white" />
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900">{taskStats.total}</div>
                          <div className="text-sm text-gray-500">
                            {isRTL ? "إجمالي المهام" : "Total Tasks"}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-green-600 font-medium">
                          {taskStats.completed} {isRTL ? "مكتملة" : "completed"}
                        </span>
                        <span className="text-blue-600 font-medium">
                          {taskStats.inProgress} {isRTL ? "قيد التنفيذ" : "in progress"}
                        </span>
                      </div>
                    </div>

                    {/* Overdue Tasks */}
                    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300">
                      <div className="flex items-center justify-between mb-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-xl flex items-center justify-center">
                          <AlertTriangle className="w-6 h-6 text-white" />
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900">{overdueTasks.length}</div>
                          <div className="text-sm text-gray-500">
                            {isRTL ? "مهام متأخرة" : "Overdue Tasks"}
                          </div>
                        </div>
                      </div>
                      <div className="text-sm text-red-600 font-medium">
                        {isRTL ? "تحتاج اهتمام فوري" : "Need immediate attention"}
                      </div>
                    </div>

                    {/* Team Members */}
                    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300">
                      <div className="flex items-center justify-between mb-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center">
                          <Users className="w-6 h-6 text-white" />
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900">{consultantsCount}</div>
                          <div className="text-sm text-gray-500">
                            {isRTL ? "أعضاء الفريق" : "Team Members"}
                          </div>
                        </div>
                      </div>
                      <div className="text-sm text-emerald-600 font-medium">
                        {isRTL ? "استشاريون نشطون" : "Active consultants"}
                      </div>
                    </div>

                    {/* Total Risks */}
                    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300">
                      <div className="flex items-center justify-between mb-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center">
                          <Shield className="w-6 h-6 text-white" />
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900">{riskData.total}</div>
                          <div className="text-sm text-gray-500">
                            {isRTL ? "إجمالي المخاطر" : "Total Risks"}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-red-600 font-medium">
                          {riskData.critical} {isRTL ? "حرجة" : "critical"}
                        </span>
                        <span className="text-orange-600 font-medium">
                          {riskData.high} {isRTL ? "عالية" : "high"}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Charts and Analytics Section */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Priority Distribution */}
                    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                          <Target className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-gray-900">
                            {isRTL ? "توزيع الأولويات" : "Priority Distribution"}
                </h3>
                          <p className="text-sm text-gray-500">
                            {isRTL ? "تصنيف المهام حسب الأولوية" : "Task classification by priority"}
                          </p>
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        {[
                          { key: TaskPriority.URGENT, label: isRTL ? "عاجلة" : "Urgent", color: "bg-red-500", count: priorityData[TaskPriority.URGENT] || 0 },
                          { key: TaskPriority.HIGH, label: isRTL ? "عالية" : "High", color: "bg-orange-500", count: priorityData[TaskPriority.HIGH] || 0 },
                          { key: TaskPriority.MEDIUM, label: isRTL ? "متوسطة" : "Medium", color: "bg-yellow-500", count: priorityData[TaskPriority.MEDIUM] || 0 },
                          { key: TaskPriority.LOW, label: isRTL ? "منخفضة" : "Low", color: "bg-green-500", count: priorityData[TaskPriority.LOW] || 0 }
                        ].map((priority) => {
                          const percentage = taskStats.total > 0 ? Math.round((priority.count / taskStats.total) * 100) : 0;
                          return (
                            <div key={priority.key} className="flex items-center gap-3">
                              <div className="flex-1">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="text-sm font-medium text-gray-700">{priority.label}</span>
                                  <span className="text-sm text-gray-500">{priority.count} ({percentage}%)</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div 
                                    className={`h-2 rounded-full ${priority.color} transition-all duration-500`}
                                    style={{ width: `${percentage}%` }}
                                  ></div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>

                    {/* Top Assignees */}
                    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center">
                          <Award className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-gray-900">
                            {isRTL ? "أعلى المكلفين" : "Top Assignees"}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {isRTL ? "توزيع عبء العمل" : "Workload distribution"}
                          </p>
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        {assigneeData.length > 0 ? assigneeData.map((assignee, index) => (
                          <div key={assignee.name} className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                            <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                              <span className="text-indigo-600 font-bold text-sm">{index + 1}</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-gray-900 truncate">{assignee.name}</div>
                              <div className="text-sm text-gray-500">
                                {assignee.count} {isRTL ? "مهمة" : "tasks"}
                              </div>
                            </div>
                            <div className="text-lg font-bold text-indigo-600">{assignee.count}</div>
                          </div>
                        )) : (
                          <div className="text-center py-4 text-gray-500">
                            {isRTL ? "لا توجد بيانات" : "No data available"}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Recent Activity and Progress */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Recent Updates */}
                    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                          <Activity className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-gray-900">
                            {isRTL ? "التحديثات الأخيرة" : "Recent Activity"}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {isRTL ? "آخر 5 تحديثات" : "Last 5 updates"}
                          </p>
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        {recentUpdates.length > 0 ? recentUpdates.map((item, index) => (
                          <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors cursor-pointer"
                               onClick={() => router.push(`/${lang}/Thiqah/Tasks/${item.task.id}`)}>
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              item.update.type === 'progress' ? 'bg-blue-100 text-blue-600' :
                              item.update.type === 'update' ? 'bg-green-100 text-green-600' :
                              'bg-gray-100 text-gray-600'
                            }`}>
                              {item.update.type === 'progress' ? <TrendingUp className="w-4 h-4" /> :
                               item.update.type === 'update' ? <FileText className="w-4 h-4" /> :
                               <Activity className="w-4 h-4" />}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-gray-900 truncate">{item.update.title}</div>
                              <div className="text-sm text-gray-500 truncate">{item.task.title}</div>
                              <div className="text-xs text-gray-400">
                                {formatRelativeTime(item.update.createdAt, isRTL)}
                              </div>
                            </div>
                          </div>
                        )) : (
                          <div className="text-center py-4 text-gray-500">
                            <BookOpen className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                            <p>{isRTL ? "لا توجد تحديثات حديثة" : "No recent updates"}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Task Progress */}
                    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                          <Zap className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-gray-900">
                            {isRTL ? "تقدم المهام" : "Task Progress"}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {isRTL ? "أعلى المهام تقدماً" : "Top progressing tasks"}
                          </p>
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        {progressData.length > 0 ? progressData.map((task) => (
                          <div key={task.taskId} className="cursor-pointer hover:bg-gray-50 p-3 rounded-xl transition-colors"
                               onClick={() => router.push(`/${lang}/Thiqah/Tasks/${task.taskId}`)}>
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium text-gray-900 truncate">{task.title}</span>
                              <span className="text-sm font-bold text-green-600">{task.progress}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-500"
                                style={{ width: `${task.progress}%` }}
                              ></div>
                            </div>
                          </div>
                        )) : (
                          <div className="text-center py-4 text-gray-500">
                            <Timer className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                            <p>{isRTL ? "لا توجد مهام بتقدم" : "No tasks with progress"}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Overdue Tasks Alert */}
                  {overdueTasks.length > 0 && (
                    <div className="bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-2xl p-6">
                      <div className="flex items-start gap-4">
                        <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                          <AlertTriangle className="w-6 h-6 text-red-600" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-bold text-red-900 mb-2">
                            {isRTL ? "تنبيه: مهام متأخرة!" : "Alert: Overdue Tasks!"}
                          </h3>
                          <p className="text-red-700 mb-4">
                  {isRTL 
                              ? `يوجد ${overdueTasks.length} مهمة متأخرة تحتاج اهتمام فوري`
                              : `There are ${overdueTasks.length} overdue tasks that need immediate attention`
                  }
                </p>
                          <div className="space-y-2">
                            {overdueTasks.slice(0, 3).map((task) => (
                              <div key={task.id} className="bg-white/50 rounded-lg p-3 cursor-pointer hover:bg-white/80 transition-colors"
                                   onClick={() => router.push(`/${lang}/Thiqah/Tasks/${task.id}`)}>
                                <div className="flex items-center justify-between">
                                  <span className="font-medium text-red-900">{task.title}</span>
                                  <span className="text-sm text-red-600">
                                    {isRTL ? "مستحقة:" : "Due:"} {formatDate(task.dueDate!, isRTL)}
                                  </span>
              </div>
                              </div>
                            ))}
                            {overdueTasks.length > 3 && (
                              <div className="text-sm text-red-600 font-medium">
                                {isRTL 
                                  ? `و ${overdueTasks.length - 3} مهام أخرى...`
                                  : `and ${overdueTasks.length - 3} more tasks...`
                                }
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}
            </motion.div>
          )}

          {/* Task Management Tab */}
          {activeTab === 'management' && (
            <motion.div
              key="management"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              {/* Task Actions Header */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {isRTL ? "إدارة المهام" : "Task Management"}
                    </h3>
                    <p className="text-gray-600">
                      {isRTL ? "إنشاء وتخصيص المهام الجديدة" : "Create and assign new tasks"}
                    </p>
                  </div>
                  <Button
                    onClick={() => setIsAddTaskModalOpen(true)}
                    className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 hover:from-[var(--brand-blue)]/90 hover:to-[var(--brand-blue)]/70 text-white shadow-lg"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {isRTL ? "إضافة مهمة" : "Add Task"}
                  </Button>
                </div>
              </div>

              {/* Tasks List */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                {isLoading ? (
                  <div className="text-center py-16">
                    <div className="w-12 h-12 border-4 border-[var(--brand-blue)] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-gray-600">
                      {isRTL ? "جاري تحميل المهام..." : "Loading tasks..."}
                    </p>
                  </div>
                ) : tasks.length === 0 ? (
                  <div className="text-center py-16">
                    <div className="w-24 h-24 bg-[var(--brand-blue)]/10 rounded-3xl flex items-center justify-center mx-auto mb-6">
                      <CheckSquare className="w-12 h-12 text-[var(--brand-blue)]" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">
                      {isRTL ? "لا توجد مهام بعد" : "No tasks yet"}
                    </h3>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-6">
                      {isRTL 
                        ? "ابدأ بإنشاء أول مهمة لفريقك"
                        : "Get started by creating your first task for the team"
                      }
                    </p>
                    <Button
                      onClick={() => setIsAddTaskModalOpen(true)}
                      className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 hover:from-[var(--brand-blue)]/90 hover:to-[var(--brand-blue)]/70 text-white shadow-lg"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      {isRTL ? "إضافة مهمة جديدة" : "Add New Task"}
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <h4 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                      <CheckSquare className="w-5 h-5 text-[var(--brand-blue)]" />
                      {isRTL ? "المهام الحالية" : "Current Tasks"}
                      <span className="bg-[var(--brand-blue)]/10 text-[var(--brand-blue)] px-2 py-1 rounded-full text-sm">
                        {tasks.length}
                      </span>
                    </h4>
                    
                    <div className="grid gap-4">
                      {tasks.map((task) => (
                        <div
                          key={task.id}
                          className="border border-gray-200 rounded-xl p-4 hover:border-[var(--brand-blue)]/30 hover:bg-[var(--brand-blue)]/5 transition-all duration-200 group"
                        >
                          <div className="flex items-start justify-between gap-4">
                            <div 
                              className="flex-1 cursor-pointer"
                              onClick={() => router.push(`/${lang}/Thiqah/Tasks/${task.id}`)}
                            >
                              <h5 className="font-semibold text-gray-900 mb-1">{task.title}</h5>
                              <p className="text-gray-600 text-sm mb-2 line-clamp-2">{task.description}</p>
                              
                              <div className="flex items-center gap-4 text-xs text-gray-500">
                                <span className={`px-2 py-1 rounded-full font-medium ${
                                  task.status === TaskStatus.PENDING ? 'bg-yellow-100 text-yellow-700' :
                                  task.status === TaskStatus.IN_PROGRESS ? 'bg-blue-100 text-blue-700' :
                                  task.status === TaskStatus.COMPLETED ? 'bg-green-100 text-green-700' :
                                  'bg-gray-100 text-gray-700'
                                }`}>
                                  {task.status === TaskStatus.PENDING ? (isRTL ? "معلقة" : "Pending") :
                                   task.status === TaskStatus.IN_PROGRESS ? (isRTL ? "قيد التنفيذ" : "In Progress") :
                                   task.status === TaskStatus.COMPLETED ? (isRTL ? "مكتملة" : "Completed") :
                                   (isRTL ? "ملغية" : "Cancelled")}
                                </span>
                                
                                <span className={`px-2 py-1 rounded-full font-medium ${
                                  task.priority === TaskPriority.LOW ? 'bg-green-50 text-green-600' :
                                  task.priority === TaskPriority.MEDIUM ? 'bg-yellow-50 text-yellow-600' :
                                  task.priority === TaskPriority.HIGH ? 'bg-orange-50 text-orange-600' :
                                  'bg-red-50 text-red-600'
                                }`}>
                                  {task.priority === TaskPriority.LOW ? (isRTL ? "منخفضة" : "Low") :
                                   task.priority === TaskPriority.MEDIUM ? (isRTL ? "متوسطة" : "Medium") :
                                   task.priority === TaskPriority.HIGH ? (isRTL ? "عالية" : "High") :
                                   (isRTL ? "عاجلة" : "Urgent")}
                                </span>

                                {task.assignees.length > 0 && (
                                  <span className="flex items-center gap-1">
                                    <Users className="w-3 h-3" />
                                    {task.assignees.length}
                                  </span>
                                )}

                                <span>{isRTL ? "بواسطة" : "by"} {task.createdByName}</span>
                              </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <Button
                                onClick={(e) => handleEditTask(task, e)}
                                variant="ghost"
                                size="sm"
                                className="text-gray-500 hover:text-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/10"
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                onClick={(e) => handleDeleteTask(task, e)}
                                variant="ghost"
                                size="sm"
                                className="text-gray-500 hover:text-red-600 hover:bg-red-50"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Add Task Modal */}
      <AddTaskModal
        isOpen={isAddTaskModalOpen}
        onClose={() => setIsAddTaskModalOpen(false)}
        onSubmit={handleCreateTask}
        isRTL={isRTL}
        isLoading={isCreatingTask}
      />

      {/* Edit Task Modal */}
      <EditTaskModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedTask(null);
        }}
        onSubmit={handleEditTaskSubmit}
        task={selectedTask}
        isRTL={isRTL}
        isLoading={isEditingTask}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setTaskToDelete(null);
        }}
        onConfirm={confirmDeleteTask}
        taskTitle={taskToDelete?.title || ''}
        isRTL={isRTL}
        isLoading={isDeleting}
      />
    </div>
  );
} 