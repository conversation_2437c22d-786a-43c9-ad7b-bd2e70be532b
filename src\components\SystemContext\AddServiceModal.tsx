"use client";

import React, { useState, useEffect } from "react";
import { X, Settings, Plus, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { SystemsService, SystemService, ServiceStep, ServiceRequirement } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";

interface AddServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  systemId: string;
  lang: string;
  editingService?: SystemService | null;
  onSuccess: () => void;
}

export function AddServiceModal({ 
  isOpen, 
  onClose, 
  systemId, 
  lang, 
  editingService, 
  onSuccess 
}: AddServiceModalProps) {
  const [formData, setFormData] = useState({
    serviceName: "",
    serviceDescription: "",
    serviceBeneficiaries: "",
    serviceAvailability: "",
    serviceTime: "",
    serviceFees: 0
  });
  const [serviceSteps, setServiceSteps] = useState<ServiceStep[]>([]);
  const [serviceRequirements, setServiceRequirements] = useState<ServiceRequirement[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { toast } = useToast();
  const isRTL = lang === "ar";

  // Dropdown options
  const beneficiariesOptions = [
    { value: "Citizens", label: isRTL ? "المواطنون" : "Citizens" },
    { value: "Businesses", label: isRTL ? "الشركات" : "Businesses" },
    { value: "Government", label: isRTL ? "الحكومة" : "Government" },
    { value: "All", label: isRTL ? "الجميع" : "All" }
  ];

  const availabilityOptions = [
    { value: "24/7", label: isRTL ? "24/7" : "24/7" },
    { value: "Business Hours", label: isRTL ? "ساعات العمل" : "Business Hours" },
    { value: "Scheduled", label: isRTL ? "مجدولة" : "Scheduled" },
    { value: "On-Demand", label: isRTL ? "عند الطلب" : "On-Demand" }
  ];

  const timeOptions = [
    { value: "Instant", label: isRTL ? "فوري" : "Instant" },
    { value: "Minutes", label: isRTL ? "دقائق" : "Minutes" },
    { value: "Hours", label: isRTL ? "ساعات" : "Hours" },
    { value: "Days", label: isRTL ? "أيام" : "Days" },
    { value: "Weeks", label: isRTL ? "أسابيع" : "Weeks" }
  ];

  const requirementTypes = [
    { value: "Document", label: isRTL ? "وثيقة" : "Document" },
    { value: "Information", label: isRTL ? "معلومات" : "Information" },
    { value: "Payment", label: isRTL ? "دفع" : "Payment" },
    { value: "Authentication", label: isRTL ? "مصادقة" : "Authentication" }
  ];

  // Initialize form when editing
  useEffect(() => {
    if (editingService) {
      setFormData({
        serviceName: editingService.serviceName,
        serviceDescription: editingService.serviceDescription,
        serviceBeneficiaries: editingService.serviceBeneficiaries,
        serviceAvailability: editingService.serviceAvailability,
        serviceTime: editingService.serviceTime,
        serviceFees: editingService.serviceFees
      });
      setServiceSteps(editingService.serviceSteps || []);
      setServiceRequirements(editingService.serviceRequirements || []);
    } else {
      setFormData({
        serviceName: "",
        serviceDescription: "",
        serviceBeneficiaries: "",
        serviceAvailability: "",
        serviceTime: "",
        serviceFees: 0
      });
      setServiceSteps([]);
      setServiceRequirements([]);
    }
  }, [editingService, isOpen]);

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addStep = () => {
    const newStep: ServiceStep = {
      id: Date.now().toString(),
      stepNumber: serviceSteps.length + 1,
      title: "",
      description: "",
      estimatedTime: "",
      isRequired: true
    };
    setServiceSteps([...serviceSteps, newStep]);
  };

  const updateStep = (id: string, field: string, value: string | boolean) => {
    setServiceSteps(steps => 
      steps.map(step => 
        step.id === id ? { ...step, [field]: value } : step
      )
    );
  };

  const removeStep = (id: string) => {
    setServiceSteps(steps => {
      const filtered = steps.filter(step => step.id !== id);
      // Renumber steps
      return filtered.map((step, index) => ({ ...step, stepNumber: index + 1 }));
    });
  };

  const addRequirement = () => {
    const newRequirement: ServiceRequirement = {
      id: Date.now().toString(),
      title: "",
      description: "",
      type: "",
      isRequired: true
    };
    setServiceRequirements([...serviceRequirements, newRequirement]);
  };

  const updateRequirement = (id: string, field: string, value: string | boolean) => {
    setServiceRequirements(requirements => 
      requirements.map(req => 
        req.id === id ? { ...req, [field]: value } : req
      )
    );
  };

  const removeRequirement = (id: string) => {
    setServiceRequirements(requirements => 
      requirements.filter(req => req.id !== id)
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.serviceName.trim() || !formData.serviceDescription.trim() || 
        !formData.serviceBeneficiaries || !formData.serviceAvailability || !formData.serviceTime) {
      toast({
        title: isRTL ? "خطأ في التحقق" : "Validation Error",
        description: isRTL ? "يرجى ملء جميع الحقول المطلوبة" : "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSubmitting(true);
      
      // Prepare service data with all required fields
      const serviceData: Omit<SystemService, 'id' | 'createdAt' | 'updatedAt' | 'systemId'> = {
        serviceName: formData.serviceName,
        serviceDescription: formData.serviceDescription,
        serviceBeneficiaries: formData.serviceBeneficiaries,
        serviceAvailability: formData.serviceAvailability,
        serviceTime: formData.serviceTime,
        serviceFees: formData.serviceFees,
        // Add optional fields if they have content
        ...(serviceSteps.length > 0 && { serviceSteps }),
        ...(serviceRequirements.length > 0 && { serviceRequirements })
      };

      if (editingService?.id) {
        // Update existing service - use Partial for updates
        const updateData: Partial<Omit<SystemService, 'id' | 'createdAt' | 'systemId'>> = {
          serviceName: formData.serviceName,
          serviceDescription: formData.serviceDescription,
          serviceBeneficiaries: formData.serviceBeneficiaries,
          serviceAvailability: formData.serviceAvailability,
          serviceTime: formData.serviceTime,
          serviceFees: formData.serviceFees,
          ...(serviceSteps.length > 0 && { serviceSteps }),
          ...(serviceRequirements.length > 0 && { serviceRequirements })
        };

        await SystemsService.updateSystemService(systemId, editingService.id, updateData);

        toast({
          title: isRTL ? "تم التحديث" : "Updated",
          description: isRTL ? "تم تحديث الخدمة بنجاح" : "Service updated successfully",
        });
      } else {
        // Add new service
        await SystemsService.addSystemService(systemId, serviceData);
        
        toast({
          title: isRTL ? "تم الإضافة" : "Added",
          description: isRTL ? "تم إضافة الخدمة بنجاح" : "Service added successfully",
        });
      }
      
      onSuccess();
    } catch (error) {
      console.error('Error saving service:', error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: isRTL ? "فشل في حفظ الخدمة" : "Failed to save service",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-[var(--brand-blue)]/10">
              <Settings className="w-5 h-5 text-[var(--brand-blue)]" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {editingService 
                  ? (isRTL ? "تعديل الخدمة" : "Edit Service")
                  : (isRTL ? "إضافة خدمة جديدة" : "Add New Service")
                }
              </h3>
              <p className="text-sm text-gray-600">
                {isRTL ? "أدخل تفاصيل الخدمة" : "Enter service details"}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0 hover:bg-gray-100"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="serviceName">
                {isRTL ? "اسم الخدمة" : "Service Name"} <span className="text-red-500">*</span>
              </Label>
              <Input
                id="serviceName"
                value={formData.serviceName}
                onChange={(e) => handleInputChange('serviceName', e.target.value)}
                placeholder={isRTL ? "أدخل اسم الخدمة..." : "Enter service name..."}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="serviceFees">
                {isRTL ? "رسوم الخدمة (ر.س)" : "Service Fees (SAR)"} <span className="text-red-500">*</span>
              </Label>
              <Input
                id="serviceFees"
                type="number"
                min="0"
                value={formData.serviceFees}
                onChange={(e) => handleInputChange('serviceFees', parseFloat(e.target.value) || 0)}
                placeholder="0"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="serviceDescription">
              {isRTL ? "وصف الخدمة" : "Service Description"} <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="serviceDescription"
              value={formData.serviceDescription}
              onChange={(e) => handleInputChange('serviceDescription', e.target.value)}
              placeholder={isRTL ? "اكتب وصف الخدمة..." : "Write service description..."}
              className="min-h-[80px]"
              required
            />
          </div>

          {/* Dropdown Fields */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="serviceBeneficiaries">
                {isRTL ? "المستفيدون" : "Beneficiaries"} <span className="text-red-500">*</span>
              </Label>
              <select
                id="serviceBeneficiaries"
                value={formData.serviceBeneficiaries}
                onChange={(e) => handleInputChange('serviceBeneficiaries', e.target.value)}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
                required
              >
                <option value="">{isRTL ? "اختر المستفيدين..." : "Select beneficiaries..."}</option>
                {beneficiariesOptions.map((option) => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="serviceAvailability">
                {isRTL ? "التوفر" : "Availability"} <span className="text-red-500">*</span>
              </Label>
              <select
                id="serviceAvailability"
                value={formData.serviceAvailability}
                onChange={(e) => handleInputChange('serviceAvailability', e.target.value)}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
                required
              >
                <option value="">{isRTL ? "اختر التوفر..." : "Select availability..."}</option>
                {availabilityOptions.map((option) => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="serviceTime">
                {isRTL ? "وقت الخدمة" : "Service Time"} <span className="text-red-500">*</span>
              </Label>
              <select
                id="serviceTime"
                value={formData.serviceTime}
                onChange={(e) => handleInputChange('serviceTime', e.target.value)}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
                required
              >
                <option value="">{isRTL ? "اختر الوقت..." : "Select time..."}</option>
                {timeOptions.map((option) => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Service Steps (Optional) */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">
                {isRTL ? "خطوات الخدمة (اختياري)" : "Service Steps (Optional)"}
              </Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addStep}
                className="text-xs"
              >
                <Plus className="w-3 h-3 mr-1" />
                {isRTL ? "إضافة خطوة" : "Add Step"}
              </Button>
            </div>

            {serviceSteps.map((step) => (
              <div key={step.id} className="p-4 border border-gray-200 rounded-lg space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">
                    {isRTL ? "الخطوة" : "Step"} {step.stepNumber}
                  </span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeStep(step.id)}
                    className="text-red-600 hover:text-red-700 h-6 w-6 p-0"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <Input
                    placeholder={isRTL ? "عنوان الخطوة..." : "Step title..."}
                    value={step.title}
                    onChange={(e) => updateStep(step.id, 'title', e.target.value)}
                  />
                  <Input
                    placeholder={isRTL ? "الوقت المقدر..." : "Estimated time..."}
                    value={step.estimatedTime || ''}
                    onChange={(e) => updateStep(step.id, 'estimatedTime', e.target.value)}
                  />
                </div>

                <Textarea
                  placeholder={isRTL ? "وصف الخطوة..." : "Step description..."}
                  value={step.description}
                  onChange={(e) => updateStep(step.id, 'description', e.target.value)}
                  className="min-h-[60px]"
                />

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id={`step-required-${step.id}`}
                    checked={step.isRequired}
                    onChange={(e) => updateStep(step.id, 'isRequired', e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <Label htmlFor={`step-required-${step.id}`} className="text-sm">
                    {isRTL ? "خطوة مطلوبة" : "Required step"}
                  </Label>
                </div>
              </div>
            ))}
          </div>

          {/* Service Requirements (Optional) */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">
                {isRTL ? "متطلبات الخدمة (اختياري)" : "Service Requirements (Optional)"}
              </Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addRequirement}
                className="text-xs"
              >
                <Plus className="w-3 h-3 mr-1" />
                {isRTL ? "إضافة متطلب" : "Add Requirement"}
              </Button>
            </div>

            {serviceRequirements.map((requirement) => (
              <div key={requirement.id} className="p-4 border border-gray-200 rounded-lg space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">
                    {isRTL ? "متطلب" : "Requirement"}
                  </span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeRequirement(requirement.id)}
                    className="text-red-600 hover:text-red-700 h-6 w-6 p-0"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <Input
                    placeholder={isRTL ? "عنوان المتطلب..." : "Requirement title..."}
                    value={requirement.title}
                    onChange={(e) => updateRequirement(requirement.id, 'title', e.target.value)}
                  />
                  <select
                    value={requirement.type}
                    onChange={(e) => updateRequirement(requirement.id, 'type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
                  >
                    <option value="">{isRTL ? "اختر النوع..." : "Select type..."}</option>
                    {requirementTypes.map((option) => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>

                <Textarea
                  placeholder={isRTL ? "وصف المتطلب..." : "Requirement description..."}
                  value={requirement.description}
                  onChange={(e) => updateRequirement(requirement.id, 'description', e.target.value)}
                  className="min-h-[60px]"
                />

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id={`req-required-${requirement.id}`}
                    checked={requirement.isRequired}
                    onChange={(e) => updateRequirement(requirement.id, 'isRequired', e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <Label htmlFor={`req-required-${requirement.id}`} className="text-sm">
                    {isRTL ? "متطلب إجباري" : "Required"}
                  </Label>
                </div>
              </div>
            ))}
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isSubmitting}
            >
              {isRTL ? "إلغاء" : "Cancel"}
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
              disabled={isSubmitting}
            >
              {isSubmitting
                ? (isRTL ? "جاري الحفظ..." : "Saving...")
                : editingService
                  ? (isRTL ? "تحديث الخدمة" : "Update Service")
                  : (isRTL ? "إضافة الخدمة" : "Add Service")
              }
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
