"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";
import { useRouter } from "next/navigation";
import { FileSearch, BarChart3, Settings, Plus, Edit, Trash2, Calendar, Users, Eye, Shield, AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { AuditService, ObservationRating, Observation } from "@/Firebase/firestore/services/AuditService";
import { Timestamp } from 'firebase/firestore';
import { auth } from "@/Firebase/Authentication/authConfig";
import { getUserProfile } from "@/Firebase/firestore/services/UserService";
import { AddObservationModal } from "@/components/ui/AddObservationModal";

interface AuditFindingsPageProps {
  params: Promise<{ lang: Locale }>;
}

interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  observationName: string;
  isRTL: boolean;
  isLoading?: boolean;
}

function DeleteConfirmModal({ isOpen, onClose, onConfirm, observationName, isRTL, isLoading }: DeleteConfirmModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className={`bg-white rounded-2xl shadow-2xl border border-gray-200 w-full max-w-md ${isRTL ? "rtl" : "ltr"}`}
      >
        <div className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <Trash2 className="w-6 h-6 text-red-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {isRTL ? "حذف الملاحظة" : "Delete Observation"}
              </h3>
              <p className="text-sm text-gray-500">
                {isRTL ? "هذا الإجراء لا يمكن التراجع عنه" : "This action cannot be undone"}
              </p>
            </div>
          </div>

          <p className="text-gray-700 mb-6">
            {isRTL 
              ? `هل أنت متأكد من أنك تريد حذف الملاحظة "${observationName}"؟`
              : `Are you sure you want to delete the observation "${observationName}"?`
            }
          </p>

          <div className="flex gap-3">
            <Button
              onClick={onClose}
              variant="outline"
              className="flex-1"
              disabled={isLoading}
            >
              {isRTL ? "إلغاء" : "Cancel"}
            </Button>
            <Button
              onClick={onConfirm}
              className="flex-1 bg-red-600 hover:bg-red-700 text-white"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  {isRTL ? "جاري الحذف..." : "Deleting..."}
                </div>
              ) : (
                <>
                  <Trash2 className="w-4 h-4 mr-2" />
                  {isRTL ? "حذف" : "Delete"}
                </>
              )}
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

export default function AuditFindingsPage({ params }: AuditFindingsPageProps) {
  const [lang, setLang] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'overview' | 'findings'>('overview');
  const [observations, setObservations] = useState<Observation[]>([]);
  const [observationStats, setObservationStats] = useState({
    total: 0,
    open: 0,
    inProgress: 0,
    resolved: 0,
    closed: 0,
    byRating: {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0
    }
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isAddObservationModalOpen, setIsAddObservationModalOpen] = useState(false);
  const [isCreatingObservation, setIsCreatingObservation] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [observationToDelete, setObservationToDelete] = useState<Observation | null>(null);
  
  const router = useRouter();
  const { toast } = useToast();

  // Helper function to format dates
  const formatDate = (timestamp: Timestamp, isRTL: boolean) => {
    const date = timestamp.toDate();
    return date.toLocaleDateString(isRTL ? "ar-SA" : "en-US", {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };



  // Initialize params
  useEffect(() => {
    params.then(({ lang }) => {
      setLang(lang);
    });
  }, [params]);

  // Load data functions
  const loadObservations = useCallback(async () => {
    try {
      const [observationsData, stats] = await Promise.all([
        AuditService.getObservations(),
        AuditService.getObservationStatistics()
      ]);
      setObservations(observationsData);
      setObservationStats(stats);
    } catch (error) {
      console.error('Error loading observations:', error);
    }
  }, []);

  // Load data when component mounts and lang is available
  useEffect(() => {
    if (lang) {
      const loadData = async () => {
        setIsLoading(true);
        await loadObservations();
        setIsLoading(false);
      };
      loadData();
    }
  }, [lang, loadObservations]);

  const handleCreateObservation = async (observationData: {
    name: string;
    observationNumber: string;
    rating: ObservationRating;
    description?: string;
  }) => {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    try {
      setIsCreatingObservation(true);
      
      const userProfile = await getUserProfile(currentUser.uid);
      const createdByName = userProfile?.displayName || currentUser.displayName || currentUser.email || 'Unknown User';
      
      const newObservation = {
        ...observationData,
        createdBy: currentUser.uid,
        createdByName: createdByName
      };

      await AuditService.createObservation(newObservation);
      await loadObservations(); // Refresh observations list
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم إنشاء الملاحظة بنجاح" : "Observation created successfully",
        description: isRTL ? `تم إنشاء الملاحظة "${observationData.name}"` : `Observation "${observationData.name}" has been created`,
      });
    } catch (error) {
      console.error('Error creating observation:', error);
      throw error;
    } finally {
      setIsCreatingObservation(false);
    }
  };

  const handleDeleteObservation = (observation: Observation, event: React.MouseEvent) => {
    event.stopPropagation();
    setObservationToDelete(observation);
    setIsDeleteModalOpen(true);
  };

  const confirmDeleteObservation = async () => {
    if (!observationToDelete) return;

    try {
      setIsDeleting(true);
      await AuditService.deleteObservation(observationToDelete.id!);
      await loadObservations(); // Refresh observations list
      
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم حذف الملاحظة بنجاح" : "Observation deleted successfully",
        description: isRTL ? `تم حذف الملاحظة "${observationToDelete.name}"` : `Observation "${observationToDelete.name}" has been deleted`,
      });
      
      setIsDeleteModalOpen(false);
      setObservationToDelete(null);
    } catch (error) {
      console.error('Error deleting observation:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في حذف الملاحظة" : "Error deleting observation",
        description: isRTL ? "فشل في حذف الملاحظة" : "Failed to delete observation",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const getRatingColor = (rating: ObservationRating) => {
    switch (rating) {
      case ObservationRating.LOW: return 'text-green-600 bg-green-50 border-green-200';
      case ObservationRating.MEDIUM: return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case ObservationRating.HIGH: return 'text-orange-600 bg-orange-50 border-orange-200';
      case ObservationRating.CRITICAL: return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'open': return 'text-blue-700 bg-blue-100 border-blue-200';
      case 'in_progress': return 'text-purple-700 bg-purple-100 border-purple-200';
      case 'resolved': return 'text-green-700 bg-green-100 border-green-200';
      case 'closed': return 'text-gray-700 bg-gray-100 border-gray-200';
      default: return 'text-blue-700 bg-blue-100 border-blue-200';
    }
  };

  // Don't render until params are loaded
  if (!lang) {
    return <div>Loading...</div>;
  }

  const isRTL = lang === "ar";

  const tabs = [
    {
      id: 'overview' as const,
      label: isRTL ? "نظرة عامة" : "Overview",
      subtitle: isRTL ? "الإحصائيات والملخص" : "Statistics & Summary",
      icon: BarChart3
    },
    {
      id: 'findings' as const,
      label: isRTL ? "إدارة النتائج" : "Findings Management",
      subtitle: isRTL ? "إنشاء وإدارة الملاحظات" : "Create & Manage Observations",
      icon: Settings
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
        </div>

        <div className="relative z-10 px-8 py-8">
          {/* System Header */}
          <div className="flex items-center gap-6 mb-6">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center shadow-lg border border-white/20">
              <FileSearch className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-1 tracking-tight">
                {isRTL ? "النتائج الرقابية" : "Audit Findings"}
              </h1>
              <p className="text-white/90 text-lg">
                {isRTL ? "إدارة وتتبع الملاحظات الرقابية" : "Manage and Track Audit Observations"}
              </p>
            </div>
          </div>

          {/* Observation Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              { 
                icon: Eye, 
                label: isRTL ? "الملاحظات المفتوحة" : "Open Observations", 
                value: isLoading ? "..." : observationStats.open.toString(),
                subValue: isRTL ? `${observationStats.inProgress} قيد المعالجة` : `${observationStats.inProgress} in progress`
              },
              { 
                icon: Shield, 
                label: isRTL ? "إجمالي الملاحظات" : "Total Observations", 
                value: isLoading ? "..." : observationStats.total.toString(),
                subValue: isRTL ? `${observationStats.resolved} محلولة` : `${observationStats.resolved} resolved`
              },
              { 
                icon: AlertTriangle, 
                label: isRTL ? "ملاحظات حرجة" : "Critical Observations", 
                value: isLoading ? "..." : observationStats.byRating.critical.toString(),
                subValue: isRTL ? "تحتاج اهتمام فوري" : "Need immediate attention"
              }
            ].map((item, index) => (
              <div
                key={index}
                className="bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-300"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-white/20 text-white">
                    <item.icon className="w-4 h-4" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="text-xs font-semibold text-white/80 uppercase tracking-wider">{item.label}</div>
                    <div className="text-sm font-bold text-white truncate" title={item.value}>{item.value}</div>
                    <div className="text-xs text-white/70">{item.subValue}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-8 py-8">
        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-2xl p-1 shadow-lg border border-gray-200">
            <div className="flex gap-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-6 py-3 rounded-xl transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-[var(--brand-blue)] text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  <span className="font-medium">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="max-w-7xl mx-auto">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              {isLoading ? (
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                  <div className="text-center py-16">
                    <div className="w-12 h-12 border-4 border-[var(--brand-blue)] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-gray-600">
                      {isRTL ? "جاري تحميل البيانات..." : "Loading dashboard data..."}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                  <div className="text-center py-16">
                    <div className="w-24 h-24 bg-[var(--brand-blue)]/10 rounded-3xl flex items-center justify-center mx-auto mb-6">
                      <BarChart3 className="w-12 h-12 text-[var(--brand-blue)]" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">
                      {isRTL ? "نظرة عامة على النتائج الرقابية" : "Audit Findings Overview"}
                    </h3>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                      {isRTL 
                        ? "ستظهر هنا الإحصائيات والتحليلات المفصلة للملاحظات الرقابية"
                        : "Detailed statistics and analytics for audit observations will appear here"
                      }
                    </p>
                  </div>
                </div>
              )}
            </motion.div>
          )}

          {/* Findings Management Tab */}
          {activeTab === 'findings' && (
            <motion.div
              key="findings"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              {/* Observation Actions Header */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {isRTL ? "إدارة الملاحظات" : "Observations Management"}
                    </h3>
                    <p className="text-gray-600">
                      {isRTL ? "إنشاء وتسجيل الملاحظات الرقابية الجديدة" : "Create and record new audit observations"}
                    </p>
                  </div>
                  <Button
                    onClick={() => setIsAddObservationModalOpen(true)}
                    className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 hover:from-[var(--brand-blue)]/90 hover:to-[var(--brand-blue)]/70 text-white shadow-lg"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {isRTL ? "إضافة ملاحظة" : "Add Observation"}
                  </Button>
                </div>
              </div>

              {/* Observations List */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                {isLoading ? (
                  <div className="text-center py-16">
                    <div className="w-12 h-12 border-4 border-[var(--brand-blue)] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-gray-600">
                      {isRTL ? "جاري تحميل الملاحظات..." : "Loading observations..."}
                    </p>
                  </div>
                ) : observations.length === 0 ? (
                  <div className="text-center py-16">
                    <div className="w-24 h-24 bg-[var(--brand-blue)]/10 rounded-3xl flex items-center justify-center mx-auto mb-6">
                      <FileSearch className="w-12 h-12 text-[var(--brand-blue)]" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">
                      {isRTL ? "لا توجد ملاحظات بعد" : "No observations yet"}
                    </h3>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-6">
                      {isRTL 
                        ? "ابدأ بإنشاء أول ملاحظة رقابية"
                        : "Get started by creating your first audit observation"
                      }
                    </p>
                    <Button
                      onClick={() => setIsAddObservationModalOpen(true)}
                      className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 hover:from-[var(--brand-blue)]/90 hover:to-[var(--brand-blue)]/70 text-white shadow-lg"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      {isRTL ? "إضافة ملاحظة جديدة" : "Add New Observation"}
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <h4 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                      <FileSearch className="w-5 h-5 text-[var(--brand-blue)]" />
                      {isRTL ? "الملاحظات الحالية" : "Current Observations"}
                      <span className="bg-[var(--brand-blue)]/10 text-[var(--brand-blue)] px-2 py-1 rounded-full text-sm">
                        {observations.length}
                      </span>
                    </h4>
                    
                    <div className="grid gap-6">
                      {observations.map((observation) => (
                        <motion.div
                          key={observation.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          onClick={() => router.push(`/${lang}/Thiqah/AuditFindings/${observation.id}`)}
                          className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 group cursor-pointer"
                        >
                          {/* Header with Rating and Status */}
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center gap-3">
                              <div className="w-12 h-12 bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                                <FileSearch className="w-6 h-6 text-white" />
                              </div>
                              <div>
                                <h5 className="font-bold text-xl text-gray-900 mb-1">{observation.name}</h5>
                                <div className="flex items-center gap-2">
                                  <span className="text-sm font-medium text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                                    #{observation.observationNumber}
                                  </span>
                                  <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getRatingColor(observation.rating)}`}>
                                    {observation.rating === ObservationRating.LOW ? (isRTL ? "منخفضة" : "Low") :
                                     observation.rating === ObservationRating.MEDIUM ? (isRTL ? "متوسطة" : "Medium") :
                                     observation.rating === ObservationRating.HIGH ? (isRTL ? "عالية" : "High") :
                                     (isRTL ? "حرجة" : "Critical")}
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                              <Button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // Edit functionality would go here
                                }}
                                variant="ghost"
                                size="sm"
                                className="text-gray-400 hover:text-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/10 rounded-xl"
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                onClick={(e) => handleDeleteObservation(observation, e)}
                                variant="ghost"
                                size="sm"
                                className="text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-xl"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>

                          {/* Description */}
                          {observation.description && (
                            <div className="bg-gray-50 rounded-xl p-4 mb-4">
                              <p className="text-gray-700 text-sm leading-relaxed">{observation.description}</p>
                            </div>
                          )}

                          {/* Status and Metadata */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <span className={`px-3 py-1.5 rounded-xl text-xs font-semibold ${getStatusColor(observation.status)}`}>
                                {observation.status === 'open' ? (isRTL ? "مفتوحة" : "Open") :
                                 observation.status === 'in_progress' ? (isRTL ? "قيد المعالجة" : "In Progress") :
                                 observation.status === 'resolved' ? (isRTL ? "محلولة" : "Resolved") :
                                 observation.status === 'closed' ? (isRTL ? "مغلقة" : "Closed") :
                                 (isRTL ? "مفتوحة" : "Open")}
                              </span>
                              
                              {observation.risks && observation.risks.length > 0 && (
                                <div className="flex items-center gap-1 text-orange-600">
                                  <AlertTriangle className="w-4 h-4" />
                                  <span className="text-sm font-medium">
                                    {observation.risks.length} {isRTL ? "مخاطر" : "risks"}
                                  </span>
                                </div>
                              )}
                            </div>

                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                {formatDate(observation.createdAt, isRTL)}
                              </span>
                              <span className="flex items-center gap-1">
                                <Users className="w-3 h-3" />
                                {observation.createdByName}
                              </span>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Add Observation Modal */}
      <AddObservationModal
        isOpen={isAddObservationModalOpen}
        onClose={() => setIsAddObservationModalOpen(false)}
        onSubmit={handleCreateObservation}
        isRTL={isRTL}
        isLoading={isCreatingObservation}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setObservationToDelete(null);
        }}
        onConfirm={confirmDeleteObservation}
        observationName={observationToDelete?.name || ''}
        isRTL={isRTL}
        isLoading={isDeleting}
      />
    </div>
  );
} 