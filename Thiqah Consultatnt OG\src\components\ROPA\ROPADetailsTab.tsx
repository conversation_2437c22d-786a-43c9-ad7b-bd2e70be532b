"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Database, Download, FileText, User, Building, Shield, Globe, Clock, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { System } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";

interface ROPARecord {
  id: string;
  systemName: string;
  controller: {
    name: string;
    contact: string;
    address: string;
  };
  processor?: {
    name: string;
    contact: string;
    address: string;
  };
  dpo?: {
    name: string;
    contact: string;
  };
  purposes: string[];
  legalBasis: string[];
  dataCategories: string[];
  dataSubjects: string[];
  recipients: string[];
  thirdCountryTransfers: {
    countries: string[];
    safeguards: string[];
  };
  retentionPeriods: Record<string, string>;
  securityMeasures: string[];
  lastUpdated: Date;
  status: 'draft' | 'completed' | 'approved';
}

interface ROPADetailsTabProps {
  systemId: string;
  lang: string;
  system: System | null;
}

export function ROPADetailsTab({ systemId, lang }: ROPADetailsTabProps) {
  const [ropaRecord, setRopaRecord] = useState<ROPARecord | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);

  const { toast } = useToast();
  const isRTL = lang === "ar";

  const loadROPARecord = useCallback(async () => {
    try {
      setIsLoading(true);
      // TODO: Implement API call to load ROPA record
      // For now, no mock data - empty state
      setRopaRecord(null);
    } catch (error) {
      console.error('Error loading ROPA record:', error);
      toast({
        title: isRTL ? "خطأ في التحميل" : "Loading Error",
        description: isRTL ? "فشل في تحميل سجل ROPA" : "Failed to load ROPA record",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast, isRTL]);

  useEffect(() => {
    loadROPARecord();
  }, [systemId, loadROPARecord]);

  const handleExportROPA = async () => {
    try {
      setIsExporting(true);
      // TODO: Implement ROPA export functionality
      await new Promise(resolve => setTimeout(resolve, 2000)); // Mock delay
      
      toast({
        title: isRTL ? "تم التصدير" : "Export Successful",
        description: isRTL ? "تم تصدير سجل ROPA بنجاح" : "ROPA record exported successfully",
      });
    } catch (error) {
      console.error('Error exporting ROPA:', error);
      toast({
        title: isRTL ? "خطأ في التصدير" : "Export Error",
        description: isRTL ? "فشل في تصدير سجل ROPA" : "Failed to export ROPA record",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  };

  const getStatusColor = (status: ROPARecord['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'approved':
        return 'bg-blue-100 text-blue-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: ROPARecord['status']) => {
    switch (status) {
      case 'completed':
        return isRTL ? "مكتمل" : "Completed";
      case 'approved':
        return isRTL ? "معتمد" : "Approved";
      case 'draft':
        return isRTL ? "مسودة" : "Draft";
      default:
        return status;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!ropaRecord) {
    return (
      <div className="text-center py-12">
        <Database className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {isRTL ? "لا يوجد سجل ROPA" : "No ROPA Record"}
        </h3>
        <p className="text-gray-600">
          {isRTL 
            ? "لم يتم إنشاء سجل أنشطة المعالجة بعد" 
            : "Record of Processing Activities has not been created yet"
          }
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {isRTL ? "تفاصيل سجل أنشطة المعالجة" : "Record of Processing Activities Details"}
          </h2>
          <p className="text-gray-600">
            {isRTL 
              ? "عرض شامل لسجل أنشطة معالجة البيانات الشخصية" 
              : "Comprehensive view of personal data processing activities record"
            }
          </p>
        </div>
        <Button
          onClick={handleExportROPA}
          disabled={isExporting}
          className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
        >
          <Download className="w-4 h-4 mr-2" />
          {isRTL ? "تصدير ROPA" : "Export ROPA"}
        </Button>
      </div>

      {/* Status Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-3 rounded-lg bg-[var(--brand-blue)]/10">
                <FileText className="w-6 h-6 text-[var(--brand-blue)]" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {ropaRecord.systemName}
                </h3>
                <p className="text-sm text-gray-600">
                  {isRTL ? "آخر تحديث:" : "Last updated:"} {ropaRecord.lastUpdated.toLocaleDateString()}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <Badge className={getStatusColor(ropaRecord.status)}>
                {getStatusLabel(ropaRecord.status)}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Controller Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="w-5 h-5" />
              {isRTL ? "معلومات المتحكم" : "Controller Information"}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="text-sm font-medium text-gray-700">
                {isRTL ? "الاسم" : "Name"}
              </div>
              <div className="text-gray-900">{ropaRecord.controller.name}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-700">
                {isRTL ? "جهة الاتصال" : "Contact"}
              </div>
              <div className="text-gray-900">{ropaRecord.controller.contact}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-700">
                {isRTL ? "العنوان" : "Address"}
              </div>
              <div className="text-gray-900">{ropaRecord.controller.address}</div>
            </div>
          </CardContent>
        </Card>

        {/* DPO Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              {isRTL ? "مسؤول حماية البيانات" : "Data Protection Officer"}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="text-sm font-medium text-gray-700">
                {isRTL ? "الاسم" : "Name"}
              </div>
              <div className="text-gray-900">{ropaRecord.dpo?.name || "N/A"}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-700">
                {isRTL ? "جهة الاتصال" : "Contact"}
              </div>
              <div className="text-gray-900">{ropaRecord.dpo?.contact || "N/A"}</div>
            </div>
          </CardContent>
        </Card>

        {/* Processing Purposes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              {isRTL ? "أغراض المعالجة" : "Processing Purposes"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {ropaRecord.purposes.map((purpose, index) => (
                <Badge key={index} variant="secondary">
                  {purpose}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Legal Basis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              {isRTL ? "الأساس القانوني" : "Legal Basis"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {ropaRecord.legalBasis.map((basis, index) => (
                <Badge key={index} variant="outline">
                  {basis}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Data Categories */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5" />
              {isRTL ? "فئات البيانات" : "Data Categories"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {ropaRecord.dataCategories.map((category, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[var(--brand-blue)] rounded-full"></div>
                  <span className="text-sm text-gray-900">{category}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Data Subjects */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              {isRTL ? "أصحاب البيانات" : "Data Subjects"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {ropaRecord.dataSubjects.map((subject, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-gray-900">{subject}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Third Country Transfers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="w-5 h-5" />
            {isRTL ? "النقل إلى دول ثالثة" : "Third Country Transfers"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="text-sm font-medium text-gray-700 mb-2">
              {isRTL ? "الدول" : "Countries"}
            </div>
            <div className="flex flex-wrap gap-2">
              {ropaRecord.thirdCountryTransfers.countries.map((country, index) => (
                <Badge key={index} variant="secondary">
                  {country}
                </Badge>
              ))}
            </div>
          </div>
          <Separator />
          <div>
            <div className="text-sm font-medium text-gray-700 mb-2">
              {isRTL ? "الضمانات" : "Safeguards"}
            </div>
            <div className="flex flex-wrap gap-2">
              {ropaRecord.thirdCountryTransfers.safeguards.map((safeguard, index) => (
                <Badge key={index} variant="outline">
                  {safeguard}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Retention Periods */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            {isRTL ? "فترات الاحتفاظ" : "Retention Periods"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(ropaRecord.retentionPeriods).map(([dataType, period], index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium text-gray-900">{dataType}</span>
                <Badge variant="secondary">{period}</Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Security Measures */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            {isRTL ? "التدابير الأمنية" : "Security Measures"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {ropaRecord.securityMeasures.map((measure, index) => (
              <div key={index} className="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm text-gray-900">{measure}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
