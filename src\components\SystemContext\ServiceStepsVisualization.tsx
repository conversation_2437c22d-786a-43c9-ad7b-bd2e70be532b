"use client";

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Clock, ArrowRight, ArrowLeft } from "lucide-react";
import { ServiceStep } from "@/Firebase/firestore/SystemsService";

interface ServiceStepsVisualizationProps {
  steps: ServiceStep[];
  lang: string;
}

export function ServiceStepsVisualization({ steps, lang }: ServiceStepsVisualizationProps) {
  const isRTL = lang === "ar";

  if (!steps || steps.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        {isRTL ? "لا توجد خطوات محددة" : "No steps defined"}
      </div>
    );
  }

  // Sort steps by step number
  const sortedSteps = [...steps].sort((a, b) => a.stepNumber - b.stepNumber);

  return (
    <div className="space-y-4">
      {sortedSteps.map((step, index) => (
        <div key={step.id} className="relative">
          {/* Step Card */}
          <div className="flex items-start gap-4 p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-200 hover:shadow-md transition-shadow duration-300">
            {/* Step Number Circle */}
            <div className="flex-shrink-0">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                step.isRequired 
                  ? 'bg-[var(--brand-blue)]' 
                  : 'bg-gray-400'
              }`}>
                {step.stepNumber}
              </div>
            </div>

            {/* Step Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-semibold text-gray-900 text-sm">
                  {step.title}
                </h4>
                {step.estimatedTime && (
                  <div className="flex items-center gap-1 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    <Clock className="w-3 h-3" />
                    {step.estimatedTime}
                  </div>
                )}
              </div>
              
              <p className="text-gray-600 text-sm leading-relaxed mb-2">
                {step.description}
              </p>
              
              <div className="flex items-center gap-2">
                <div className={`flex items-center gap-1 text-xs px-2 py-1 rounded-full ${
                  step.isRequired 
                    ? 'bg-red-100 text-red-700' 
                    : 'bg-green-100 text-green-700'
                }`}>
                  <CheckCircle className="w-3 h-3" />
                  {step.isRequired 
                    ? (isRTL ? "مطلوب" : "Required")
                    : (isRTL ? "اختياري" : "Optional")
                  }
                </div>
              </div>
            </div>
          </div>

          {/* Arrow Connector */}
          {index < sortedSteps.length - 1 && (
            <div className="flex justify-center py-2">
              <div className="flex items-center gap-2 text-gray-400">
                {isRTL ? (
                  <ArrowLeft className="w-4 h-4" />
                ) : (
                  <ArrowRight className="w-4 h-4" />
                )}
              </div>
            </div>
          )}
        </div>
      ))}

      {/* Summary */}
      <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4 text-blue-600" />
            <span className="font-medium text-blue-900">
              {isRTL ? "ملخص الخطوات" : "Steps Summary"}
            </span>
          </div>
          <div className="flex items-center gap-4 text-blue-700">
            <span>
              {isRTL ? "إجمالي الخطوات:" : "Total Steps:"} {sortedSteps.length}
            </span>
            <span>
              {isRTL ? "مطلوبة:" : "Required:"} {sortedSteps.filter(s => s.isRequired).length}
            </span>
            <span>
              {isRTL ? "اختيارية:" : "Optional:"} {sortedSteps.filter(s => !s.isRequired).length}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
