"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { z } from "zod";
import { AuthForm } from "@/components/ui/authUI/AuthForm";
import { FormField } from "@/components/ui/authUI/FormField";
import { loginWithEmailAndPassword } from "@/Firebase/Authentication/authConfig";
import { upsertUserProfile } from "@/Firebase/firestore/services/UserService";
import { handleAuthError } from "@/Firebase/Authentication/errorHandler";
import { useToast } from "@/components/ui/use-toast";
import { Dictionary } from "@/dictionaries";
import { i18n, type Locale } from '@/i18n-config';
import { Button } from "@/components/ui/button";

// Language switcher component
const LanguageSwitcher = () => {
  const pathname = usePathname();

  const getPathWithLocale = (locale: Locale) => {
    if (!pathname) return '/';
    const segments = pathname.split('/');
    segments[1] = locale;
    return segments.join('/');
  };

  return (
    <div className="flex gap-2">
      {i18n.locales.map((locale) => {
        const isActive = pathname.split('/')[1] === locale;
        return (
          <Link key={locale} href={getPathWithLocale(locale)}>
            <Button
              variant={isActive ? "default" : "ghost"}
              size="sm"
              className={isActive
                ? "bg-[#23A9DB] hover:bg-[#23A9DB]/80 text-[#FFFFFF]"
                : "text-[#3D3D45] hover:bg-[#23A9DB]/10 hover:text-[#23A9DB]"
              }
            >
              {locale.toUpperCase()}
            </Button>
          </Link>
        );
      })}
    </div>
  );
};

// Animation background component
const AnimatedBackground = () => {
  // Predefined values to avoid hydration mismatch
  const circleData = [
    { width: 275, height: 120, top: 15, left: 85, opacity: 0.3, duration: 18, delay: 2 },
    { width: 150, height: 200, top: 65, left: 25, opacity: 0.2, duration: 15, delay: 0 },
    { width: 320, height: 180, top: 45, left: 60, opacity: 0.25, duration: 20, delay: 1 },
    { width: 100, height: 250, top: 80, left: 10, opacity: 0.15, duration: 16, delay: 3 },
    { width: 240, height: 160, top: 20, left: 40, opacity: 0.35, duration: 14, delay: 4 },
    { width: 180, height: 220, top: 55, left: 75, opacity: 0.25, duration: 19, delay: 1.5 },
    { width: 120, height: 140, top: 30, left: 90, opacity: 0.2, duration: 17, delay: 2.5 },
    { width: 300, height: 100, top: 70, left: 5, opacity: 0.3, duration: 13, delay: 0.5 },
    { width: 90, height: 190, top: 10, left: 70, opacity: 0.15, duration: 21, delay: 3.5 },
    { width: 260, height: 170, top: 85, left: 50, opacity: 0.28, duration: 12, delay: 1.8 }
  ];

  return (
    <div className="relative w-full h-full overflow-hidden bg-[#23A9DB]">
      {/* Animated circles */}
      <div className="absolute top-0 left-0 w-full h-full">
        {circleData.map((circle, i) => (
          <div
            key={i}
            className="absolute rounded-full opacity-20 animate-float"
            style={{
              width: `${circle.width}px`,
              height: `${circle.height}px`,
              top: `${circle.top}%`,
              left: `${circle.left}%`,
              backgroundColor: `rgba(255, 255, 255, ${circle.opacity})`,
              animationDuration: `${circle.duration}s`,
              animationDelay: `${circle.delay}s`
            }}
          />
        ))}
      </div>

      {/* Branding */}
      <div className="absolute inset-0 flex flex-col items-center justify-center text-[#FFFFFF] z-10">
        <h1 className="text-4xl md:text-5xl font-bold mb-4 text-center">Portal Thiqah DMO</h1>
        <p className="text-xl md:text-2xl text-center max-w-lg opacity-90">Your trusted data management and optimization portal</p>
      </div>
    </div>
  );
};

export default function SignInClient({
  dict
}: {
  dict: Dictionary
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Get current language from pathname
  const currentLang = pathname.split('/')[1] || 'en';

  // Add animation CSS
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes float {
        0% { transform: translate(0, 0) rotate(0deg); opacity: 0.2; }
        50% { transform: translate(30px, 30px) rotate(180deg); opacity: 0.5; }
        100% { transform: translate(0, 0) rotate(360deg); opacity: 0.2; }
      }
      .animate-float {
        animation: float 15s infinite ease-in-out;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Form schema
  const formSchema = z.object({
    email: z.string().email({
      message: dict.auth.common.invalidEmail,
    }),
    password: z.string().min(1, {
      message: dict.auth.common.requiredField,
    }),
    rememberMe: z.boolean().optional(),
  });

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      // Sign in with email and password
      const user = await loginWithEmailAndPassword(data.email, data.password);

      // Create or update user profile in Firestore
      await upsertUserProfile(user.uid, {
        email: user.email || "",
        displayName: user.displayName || "",
        photoURL: user.photoURL,
      });

      toast({
        title: dict.auth.signin.success,
        variant: "default",
      });
      router.push(`/${currentLang}/Thiqah/Home`);
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: dict.auth.signin.error,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="flex h-screen w-full overflow-hidden">
      {/* 70% Animated Background */}
      <div className="hidden md:block w-[70%] h-full">
        <AnimatedBackground />
      </div>
      
      {/* 30% Sign In Form */}
      <div className="w-full md:w-[30%] h-full bg-[#FFFFFF] flex flex-col justify-start items-center p-6 overflow-y-auto">
        {/* Language switcher at the top */}
        <div className="w-full flex justify-end mb-8">
          <LanguageSwitcher />
        </div>

        {/* Logo */}
        <div className="mb-8 flex justify-center">
          <Image
            src="/image.png"
            alt="THIqah Logo"
            width={150}
            height={150}
            className="mx-auto"
            priority
          />
        </div>

        <div className="w-full max-w-md">
          <h2 className="text-3xl font-bold mb-3 text-center text-[#23A9DB]">{dict.auth.signin.title}</h2>
          <p className="text-[#3D3D45] text-center mb-8 text-sm">{dict.auth.signin.subtitle}</p>

          <AuthForm
            schema={formSchema}
            onSubmit={onSubmit}
            submitText={dict.auth.signin.button}
            isLoading={isLoading}
          >
            <div className="space-y-4">
              <FormField
                name="email"
                label={dict.auth.common.email}
                placeholder="<EMAIL>"
                type="email"
                required
                autoComplete="email"
                className="[&_label]:text-[#23A9DB] [&_label]:font-medium [&_input]:border-[#23A9DB]/30 [&_input]:focus:border-[#23A9DB] [&_input]:focus:ring-[#23A9DB]/20"
              />
              <FormField
                name="password"
                label={dict.auth.common.password}
                placeholder="••••••••"
                type="password"
                required
                autoComplete="current-password"
                className="[&_label]:text-[#23A9DB] [&_label]:font-medium [&_input]:border-[#23A9DB]/30 [&_input]:focus:border-[#23A9DB] [&_input]:focus:ring-[#23A9DB]/20"
              />
            </div>
            <div className="flex items-center justify-between mt-6">
              <FormField
                name="rememberMe"
                isCheckbox
                checkboxLabel={dict.auth.common.rememberMe}
                className="[&_label]:text-[#23A9DB] [&_label]:text-sm [&_button]:border-[#23A9DB]/50 [&_button]:data-[state=checked]:bg-[#23A9DB] [&_button]:data-[state=checked]:border-[#23A9DB]"
              />
              <Link
                href="reset"
                className="text-sm font-medium text-[#23A9DB] hover:text-[#23A9DB]/80 hover:underline transition-colors"
              >
                {dict.auth.common.forgotPassword}
              </Link>
            </div>
          </AuthForm>
        </div>
      </div>
    </div>
  );
}
