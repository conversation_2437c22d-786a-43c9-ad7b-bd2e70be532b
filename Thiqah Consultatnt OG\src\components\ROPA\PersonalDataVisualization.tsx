"use client";

import React, { useState } from "react";
import { Database, Table, Search, Filter, X } from "lucide-react";
import { SystemData } from "@/Firebase/firestore/SystemsService";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface PersonalDataVisualizationProps {
  personalData: SystemData[];
  isLoading: boolean;
  lang: string;
  systemId: string;
}

export function PersonalDataVisualization({
  personalData,
  isLoading,
  lang
}: PersonalDataVisualizationProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTable, setSelectedTable] = useState<string>("");
  
  const isRTL = lang === "ar";

  // Filter data based on search and table selection
  const filteredData = personalData.filter(item => {
    const matchesSearch = !searchTerm || 
      item.tableName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.columnName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesTable = !selectedTable || item.tableName === selectedTable;
    
    return matchesSearch && matchesTable;
  });

  // Group data by table
  const groupedData = filteredData.reduce((acc, item) => {
    if (!acc[item.tableName]) {
      acc[item.tableName] = [];
    }
    acc[item.tableName].push(item);
    return acc;
  }, {} as Record<string, SystemData[]>);

  // Get unique tables for filter
  const uniqueTables = Array.from(new Set(personalData.map(item => item.tableName))).sort();

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
        <div className="space-y-2">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-16 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  if (personalData.length === 0) {
    return (
      <div className="text-center py-8">
        <Database className="w-12 h-12 text-gray-300 mx-auto mb-4" />
        <h4 className="text-md font-medium text-gray-900 mb-2">
          {isRTL ? "لا توجد بيانات شخصية" : "No Personal Data"}
        </h4>
        <p className="text-sm text-gray-600 mb-2">
          {isRTL
            ? "لا توجد بيانات شخصية بدون مسار مراجعة"
            : "No personal data without audit trail found"
          }
        </p>
        <p className="text-xs text-gray-500">
          {isRTL
            ? "يتم عرض البيانات الشخصية التي تحتوي على auditTrail = 'No' فقط"
            : "Only personal data with auditTrail = 'No' is shown here"
          }
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter */}
      <div className="space-y-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder={isRTL ? "البحث في الجداول والأعمدة..." : "Search tables and columns..."}
            className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <select
            value={selectedTable}
            onChange={(e) => setSelectedTable(e.target.value)}
            className="flex-1 px-3 py-2 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
          >
            <option value="">{isRTL ? "جميع الجداول" : "All Tables"}</option>
            {uniqueTables.map(table => (
              <option key={table} value={table}>{table}</option>
            ))}
          </select>
          {(searchTerm || selectedTable) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSearchTerm("");
                setSelectedTable("");
              }}
              className="h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-3">
        <div className="bg-[var(--brand-blue)]/5 rounded-lg p-3">
          <div className="text-lg font-bold text-[var(--brand-blue)]">
            {Object.keys(groupedData).length}
          </div>
          <div className="text-xs text-gray-600">
            {isRTL ? "الجداول" : "Tables"}
          </div>
        </div>
        <div className="bg-green-50 rounded-lg p-3">
          <div className="text-lg font-bold text-green-600">
            {filteredData.length}
          </div>
          <div className="text-xs text-gray-600">
            {isRTL ? "الأعمدة" : "Columns"}
          </div>
        </div>
      </div>

      {/* Data Visualization */}
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {Object.entries(groupedData).map(([tableName, columns]) => (
          <div key={tableName} className="border border-gray-200 rounded-lg overflow-hidden">
            {/* Table Header */}
            <div className="bg-gray-50 px-3 py-2 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Table className="w-4 h-4 text-gray-500" />
                <span className="font-medium text-gray-900 text-sm">{tableName}</span>
                <Badge variant="secondary" className="text-xs">
                  {columns.length} {isRTL ? "عمود" : "columns"}
                </Badge>
              </div>
            </div>
            
            {/* Columns */}
            <div className="p-3">
              <div className="grid grid-cols-1 gap-2">
                {columns.map((column, index) => (
                  <div 
                    key={`${column.id}-${index}`}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm text-gray-900 truncate">
                        {column.columnName}
                      </div>
                      <div className="text-xs text-gray-500">
                        {column.dataType}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {/* Personal Data Type */}
                      {column.personalDataType && (
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${
                            column.personalDataType === 'direct' ? 'border-blue-200 text-blue-700' :
                            column.personalDataType === 'indirect' ? 'border-purple-200 text-purple-700' :
                            column.personalDataType === 'pseudonymous' ? 'border-orange-200 text-orange-700' :
                            'border-gray-200 text-gray-700'
                          }`}
                        >
                          {column.personalDataType === 'direct' ? (isRTL ? "مباشر" : "Direct") :
                           column.personalDataType === 'indirect' ? (isRTL ? "غير مباشر" : "Indirect") :
                           column.personalDataType === 'pseudonymous' ? (isRTL ? "مستعار" : "Pseudonymous") :
                           (isRTL ? "مجهول" : "Anonymous")}
                        </Badge>
                      )}
                      
                      {/* Special Category */}
                      {column.specialCategoryType && column.specialCategoryType !== 'none' && (
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${
                            column.specialCategoryType === 'PII' ? 'border-green-200 text-green-700' :
                            column.specialCategoryType === 'PHI' ? 'border-red-200 text-red-700' :
                            column.specialCategoryType === 'PCI' ? 'border-yellow-200 text-yellow-700' :
                            column.specialCategoryType === 'Genetic' ? 'border-pink-200 text-pink-700' :
                            column.specialCategoryType === 'Biometric' ? 'border-indigo-200 text-indigo-700' :
                            'border-gray-200 text-gray-700'
                          }`}
                        >
                          {column.specialCategoryType}
                        </Badge>
                      )}
                      
                      {/* Confidentiality Level */}
                      {column.confidentialityLevel && (
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${
                            column.confidentialityLevel === 'Public' ? 'border-green-200 text-green-700' :
                            column.confidentialityLevel === 'Confidential' ? 'border-yellow-200 text-yellow-700' :
                            column.confidentialityLevel === 'Secret' ? 'border-orange-200 text-orange-700' :
                            'border-red-200 text-red-700'
                          }`}
                        >
                          {isRTL ? (
                            column.confidentialityLevel === 'Public' ? 'عام' :
                            column.confidentialityLevel === 'Confidential' ? 'سري' :
                            column.confidentialityLevel === 'Secret' ? 'سري جداً' : 'سري للغاية'
                          ) : column.confidentialityLevel}
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredData.length === 0 && personalData.length > 0 && (
        <div className="text-center py-6">
          <Search className="w-8 h-8 text-gray-300 mx-auto mb-2" />
          <p className="text-sm text-gray-600">
            {isRTL ? "لا توجد نتائج للبحث" : "No results found"}
          </p>
        </div>
      )}
    </div>
  );
}
