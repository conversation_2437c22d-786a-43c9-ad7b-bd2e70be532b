"use client";

import React, { useState, useEffect, useCallback } from "react";
import { X, User, Mail, Calendar, Shield, Edit3, Save, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { getUserProfile, upsertUserProfile, UserProfile, UserRole } from "@/Firebase/firestore/services/UserService";
import { auth } from "@/Firebase/Authentication/authConfig";
import { Timestamp } from "firebase/firestore";
import Image from "next/image";

interface UserProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  isRTL: boolean;
  onNameUpdate?: (newName: string | null) => void;
}

export function UserProfileModal({ isOpen, onClose, isRTL, onNameUpdate }: UserProfileModalProps) {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [editedName, setEditedName] = useState("");
  const { toast } = useToast();

  const loadUserProfile = useCallback(async () => {
    if (!auth.currentUser) return;

    try {
      setIsLoading(true);
      const profile = await getUserProfile(auth.currentUser.uid);
      setUserProfile(profile);
      setEditedName(profile?.displayName || "");
    } catch (error) {
      console.error("Error loading user profile:", error);
      toast({
        title: isRTL ? "خطأ في تحميل البيانات" : "Error loading profile",
        description: isRTL ? "فشل في تحميل بيانات المستخدم" : "Failed to load user profile",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [isRTL, toast]);

  useEffect(() => {
    if (isOpen && auth.currentUser) {
      loadUserProfile();
    }
  }, [isOpen, loadUserProfile]);

  const handleSaveName = async () => {
    if (!auth.currentUser || !userProfile) return;

    try {
      setIsSaving(true);
      await upsertUserProfile(auth.currentUser.uid, {
        email: userProfile.email || auth.currentUser.email || "",
        displayName: editedName.trim() || null,
        photoURL: userProfile.photoURL,
        role: userProfile.role,
      });

      // Update local state
      setUserProfile({
        ...userProfile,
        displayName: editedName.trim() || null,
      });

      setIsEditing(false);
      
      toast({
        title: isRTL ? "تم الحفظ بنجاح" : "Profile Updated",
        description: isRTL ? "تم تحديث الاسم بنجاح" : "Your name has been updated successfully",
      });

      // Update the sidebar name via callback
      if (onNameUpdate) {
        onNameUpdate(editedName.trim() || null);
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: isRTL ? "فشل في تحديث البيانات" : "Failed to update profile",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const formatDate = (timestamp: Timestamp | Date | string | null | undefined) => {
    if (!timestamp) return "";
    
    // Handle different timestamp types
    let date: Date;
    if (timestamp instanceof Timestamp) {
      date = timestamp.toDate();
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else if (typeof timestamp === 'string') {
      date = new Date(timestamp);
    } else {
      return "";
    }
      
    return date.toLocaleDateString(isRTL ? "ar-SA" : "en-US", {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRoleLabel = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return isRTL ? "مدير" : "Admin";
      case UserRole.CONSULTANT:
        return isRTL ? "استشاري" : "Consultant";
      case UserRole.CLIENT:
        return isRTL ? "عميل" : "Client";
      case UserRole.VIEWER:
        return isRTL ? "مشاهد" : "Viewer";
      default:
        return isRTL ? "مستخدم" : "User";
    }
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return "bg-red-100 text-red-800";
      case UserRole.CONSULTANT:
        return "bg-blue-100 text-blue-800";
      case UserRole.CLIENT:
        return "bg-green-100 text-green-800";
      case UserRole.VIEWER:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center p-4">
      <div className={`bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden ${isRTL ? "rtl" : "ltr"}`}>
        {/* Header */}
        <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 p-6 text-white">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold">
              {isRTL ? "الملف الشخصي" : "User Profile"}
            </h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-white hover:bg-white/20"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* User Avatar */}
          <div className="flex flex-col items-center">
            {auth.currentUser?.photoURL ? (
              <Image
                src={auth.currentUser.photoURL}
                alt="User Profile"
                width={80}
                height={80}
                className="rounded-full border-4 border-white/30 shadow-lg"
              />
            ) : (
              <div className="w-20 h-20 rounded-full bg-white/30 flex items-center justify-center border-4 border-white/30">
                <User size={40} className="text-white" />
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-8 h-8 animate-spin text-[var(--brand-blue)]" />
            </div>
          ) : userProfile ? (
            <div className="space-y-6">
              {/* Name Section */}
              <div className="space-y-2">
                <label className="text-sm font-semibold text-gray-500 uppercase tracking-wider">
                  {isRTL ? "الاسم" : "Display Name"}
                </label>
                {isEditing ? (
                  <div className="flex gap-2">
                    <Input
                      value={editedName}
                      onChange={(e) => setEditedName(e.target.value)}
                      placeholder={isRTL ? "أدخل اسمك" : "Enter your name"}
                      className="flex-1"
                    />
                    <Button
                      onClick={handleSaveName}
                      disabled={isSaving}
                      size="sm"
                      className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
                    >
                      {isSaving ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <Save className="w-4 h-4" />
                      )}
                    </Button>
                    <Button
                      onClick={() => {
                        setIsEditing(false);
                        setEditedName(userProfile.displayName || "");
                      }}
                      variant="outline"
                      size="sm"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center justify-between">
                    <p className="text-lg font-semibold text-gray-900">
                      {userProfile.displayName || (isRTL ? "غير محدد" : "Not set")}
                    </p>
                    <Button
                      onClick={() => setIsEditing(true)}
                      variant="outline"
                      size="sm"
                    >
                      <Edit3 className="w-4 h-4" />
                    </Button>
                  </div>
                )}
              </div>

              {/* Email */}
              <div className="space-y-2">
                <label className="text-sm font-semibold text-gray-500 uppercase tracking-wider flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  {isRTL ? "البريد الإلكتروني" : "Email"}
                </label>
                <p className="text-lg font-semibold text-gray-900 break-all">
                  {userProfile.email || (isRTL ? "غير محدد" : "Not set")}
                </p>
              </div>

              {/* Role */}
              <div className="space-y-2">
                <label className="text-sm font-semibold text-gray-500 uppercase tracking-wider flex items-center gap-2">
                  <Shield className="w-4 h-4" />
                  {isRTL ? "الدور" : "Role"}
                </label>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(userProfile.role)}`}>
                  {getRoleLabel(userProfile.role)}
                </span>
              </div>

                             {/* Created Date */}
               <div className="space-y-2">
                 <label className="text-sm font-semibold text-gray-500 uppercase tracking-wider flex items-center gap-2">
                   <Calendar className="w-4 h-4" />
                   {isRTL ? "تاريخ الإنشاء" : "Member Since"}
                 </label>
                 <p className="text-lg font-semibold text-gray-900">
                   {formatDate(userProfile.createdAt)}
                 </p>
               </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">
                {isRTL ? "لم يتم العثور على بيانات المستخدم" : "User profile not found"}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 