"use client";

import React, { useState, useEffect } from "react";
import { X, MessageSquare, Tag, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";

import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { SystemsService, SystemContextPoint } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";

interface AddContextPointModalProps {
  isOpen: boolean;
  onClose: () => void;
  systemId: string;
  lang: string;
  editingPoint?: SystemContextPoint | null;
  onSuccess: () => void;
}

export function AddContextPointModal({ 
  isOpen, 
  onClose, 
  systemId, 
  lang, 
  editingPoint, 
  onSuccess 
}: AddContextPointModalProps) {
  const [content, setContent] = useState("");
  const [tag, setTag] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { toast } = useToast();
  const isRTL = lang === "ar";

  // Predefined tags for dropdown
  const tagOptions = [
    { value: "System Description", label: isRTL ? "وصف النظام" : "System Description" },
    { value: "System Personas", label: isRTL ? "شخصيات النظام" : "System Personas" },
    { value: "System Service Brief", label: isRTL ? "ملخص الخدمة" : "System Service Brief" }
  ];

  // Initialize form when editing
  useEffect(() => {
    if (editingPoint) {
      setContent(editingPoint.content);
      setTag(editingPoint.tag);
    } else {
      setContent("");
      setTag("");
    }
  }, [editingPoint, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim() || !tag.trim()) {
      toast({
        title: isRTL ? "خطأ في التحقق" : "Validation Error",
        description: isRTL ? "يرجى ملء جميع الحقول المطلوبة" : "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    if (content.trim().split(' ').length > 200) {
      toast({
        title: isRTL ? "خطأ في التحقق" : "Validation Error",
        description: isRTL ? "المحتوى يجب أن يكون أقل من 200 كلمة" : "Content must be less than 200 words",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSubmitting(true);
      
      if (editingPoint?.id) {
        // Update existing point
        await SystemsService.updateSystemContextPoint(
          systemId,
          editingPoint.id,
          content.trim(),
          tag.trim()
        );
        
        toast({
          title: isRTL ? "تم التحديث" : "Updated",
          description: isRTL ? "تم تحديث نقطة السياق بنجاح" : "Context point updated successfully",
        });
      } else {
        // Add new point
        await SystemsService.addSystemContextPoint(
          systemId,
          content.trim(),
          tag.trim()
        );
        
        toast({
          title: isRTL ? "تم الإضافة" : "Added",
          description: isRTL ? "تم إضافة نقطة السياق بنجاح" : "Context point added successfully",
        });
      }
      
      onSuccess();
    } catch (error) {
      console.error('Error saving context point:', error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: isRTL ? "فشل في حفظ نقطة السياق" : "Failed to save context point",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-[var(--brand-blue)]/10">
              <MessageSquare className="w-5 h-5 text-[var(--brand-blue)]" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {editingPoint 
                  ? (isRTL ? "تعديل نقطة السياق" : "Edit Context Point")
                  : (isRTL ? "إضافة نقطة سياق" : "Add Context Point")
                }
              </h3>
              <p className="text-sm text-gray-600">
                {isRTL ? "أضف معلومات مهمة حول النظام" : "Add important information about the system"}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0 hover:bg-gray-100"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Tag Selection */}
          <div className="space-y-2">
            <Label htmlFor="tag" className="flex items-center gap-2">
              <Tag className="w-4 h-4" />
              {isRTL ? "التصنيف" : "Tag"} <span className="text-red-500">*</span>
            </Label>
            <select
              id="tag"
              value={tag}
              onChange={(e) => setTag(e.target.value)}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
              required
            >
              <option value="">
                {isRTL ? "اختر التصنيف..." : "Select tag..."}
              </option>
              {tagOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Content */}
          <div className="space-y-2">
            <Label htmlFor="content" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              {isRTL ? "المحتوى" : "Content"} <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder={isRTL
                ? "اكتب محتوى نقطة السياق (حد أقصى 200 كلمة)..."
                : "Write the context point content (max 200 words)..."
              }
              className="min-h-[120px] resize-none"
              required
            />
            <div className="text-xs text-gray-500 text-right">
              {content.trim().split(' ').filter(word => word.length > 0).length}/200 {isRTL ? "كلمة" : "words"}
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isSubmitting}
            >
              {isRTL ? "إلغاء" : "Cancel"}
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
              disabled={isSubmitting}
            >
              {isSubmitting 
                ? (isRTL ? "جاري الحفظ..." : "Saving...")
                : editingPoint 
                  ? (isRTL ? "تحديث" : "Update")
                  : (isRTL ? "إضافة" : "Add")
              }
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
