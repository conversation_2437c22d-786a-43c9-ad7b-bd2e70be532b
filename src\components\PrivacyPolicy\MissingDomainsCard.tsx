"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON>gle, XCircle, Lightbulb } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface MissingDomainsCardProps {
  missingDomains: string[];
  recommendations: string[];
  isRTL: boolean;
}

export function MissingDomainsCard({ missingDomains, recommendations, isRTL }: MissingDomainsCardProps) {
  const domainDescriptions: Record<string, string> = {
    "Entity Name and Activity": "Official entity name, business overview, and specializations",
    "Contact Information and Update Record": "Contact details, DPO information, and update history",
    "Personal Data to Be Collected": "Data categories, mandatory vs optional data specifications",
    "Collecting Personal Data Methods and Purposes": "Collection methods, purposes, and legal bases",
    "Personal Data Processing": "Processing mechanisms and data lifecycle management",
    "Personal Data Sharing": "Data disclosure policies and recipient information",
    "Personal Data Storage, Retention Period, and Destruction": "Storage methods, retention periods, and destruction procedures",
    "Personal Data Subjects Rights": "Eight specific rights and exercise methods",
    "Complaint and Objection Filing Mechanism": "Complaint procedures and SDAIA contact information",
    "Availing and Providing Access to Privacy Policy": "Accessibility, language, and notification methods"
  };

  const arabicDomainDescriptions: Record<string, string> = {
    "Entity Name and Activity": "الاسم الرسمي للجهة ونظرة عامة على الأعمال والتخصصات",
    "Contact Information and Update Record": "معلومات الاتصال ومعلومات مسؤول حماية البيانات وسجل التحديثات",
    "Personal Data to Be Collected": "فئات البيانات ومواصفات البيانات الإلزامية مقابل الاختيارية",
    "Collecting Personal Data Methods and Purposes": "طرق الجمع والأغراض والأسس القانونية",
    "Personal Data Processing": "آليات المعالجة وإدارة دورة حياة البيانات",
    "Personal Data Sharing": "سياسات الكشف عن البيانات ومعلومات المستلمين",
    "Personal Data Storage, Retention Period, and Destruction": "طرق التخزين وفترات الاحتفاظ وإجراءات الإتلاف",
    "Personal Data Subjects Rights": "الحقوق الثمانية المحددة وطرق ممارستها",
    "Complaint and Objection Filing Mechanism": "إجراءات الشكاوى ومعلومات الاتصال بسدايا",
    "Availing and Providing Access to Privacy Policy": "إمكانية الوصول واللغة وطرق الإشعار"
  };

  if (missingDomains.length === 0 && recommendations.length === 0) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="border-l-4 border-l-red-500 bg-red-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-red-800">
            <AlertTriangle className="w-6 h-6" />
            {isRTL ? "المجالات المفقودة والتوصيات" : "Missing Domains & Recommendations"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Missing Domains */}
          {missingDomains.length > 0 && (
            <div>
              <div className="flex items-center gap-2 mb-4">
                <XCircle className="w-5 h-5 text-red-600" />
                <h3 className="text-lg font-semibold text-red-800">
                  {isRTL ? "المجالات المفقودة" : "Missing Domains"}
                </h3>
                <Badge variant="destructive" className="ml-2">
                  {missingDomains.length}
                </Badge>
              </div>
              
              <div className="grid gap-3">
                {missingDomains.map((domain, index) => (
                  <motion.div
                    key={domain}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="bg-white rounded-lg p-4 border border-red-200 shadow-sm"
                  >
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                      <div className="flex-1">
                        <h4 className="font-medium text-red-900 mb-1">
                          {domain}
                        </h4>
                        <p className="text-sm text-red-700">
                          {isRTL 
                            ? arabicDomainDescriptions[domain] || "وصف غير متوفر"
                            : domainDescriptions[domain] || "Description not available"
                          }
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Lightbulb className="w-5 h-5 text-amber-600" />
                <h3 className="text-lg font-semibold text-amber-800">
                  {isRTL ? "التوصيات العامة" : "General Recommendations"}
                </h3>
                <Badge variant="outline" className="ml-2 border-amber-300 text-amber-700">
                  {recommendations.length}
                </Badge>
              </div>
              
              <div className="space-y-3">
                {recommendations.map((recommendation, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="bg-amber-50 rounded-lg p-4 border border-amber-200"
                  >
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-bold text-amber-700">
                          {index + 1}
                        </span>
                      </div>
                      <p className="text-sm text-amber-800 leading-relaxed">
                        {recommendation}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
