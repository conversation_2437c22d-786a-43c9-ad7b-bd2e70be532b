"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";
import { useRouter } from "next/navigation";
import { ArrowLeft, ArrowRight, Shield, User, Mail, Database, Building, FileText, MessageSquare, FileCheck, ClipboardList, UserCheck, Key, FileType, BarChart3, TrendingUp, Users, Table, CheckCircle, Clock, Link, ExternalLink, Edit, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemsService, System, SystemData, SystemContext } from "@/Firebase/firestore/SystemsService";
import { Timestamp } from "firebase/firestore";
import { useToast } from "@/components/ui/use-toast";
import { ExcelImportModal } from "@/components/ui/ExcelImportModal";
import { SystemDataTable } from "@/components/ui/SystemDataTable";
import { SystemContextTab } from "@/components/SystemContext/SystemContextTab";
import { PersonalDataTable } from "@/components/ui/PersonalDataTable";
import { ContextModal } from "@/components/ui/ContextModal";

interface SystemDetailsPageProps {
  params: Promise<{ lang: Locale; systemId: string }>;
}

export default function SystemDetailsPage({ params }: SystemDetailsPageProps) {
  const [lang, setLang] = useState<string>('');
  const [systemId, setSystemId] = useState<string>('');
  const [system, setSystem] = useState<System | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [systemData, setSystemData] = useState<SystemData[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [isContextModalOpen, setIsContextModalOpen] = useState(false);
  const [systemContext, setSystemContext] = useState<SystemContext | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'data' | 'personal-data' | 'classification' | 'context' | 'system-links'>('overview');
  const [systemDataStats, setSystemDataStats] = useState<{
    totalRecords: number;
    confidentialityDistribution: Record<string, number>;
    personalDataDistribution: Record<string, number>;
    tableTypeDistribution: Record<string, number>;
    dataCategoryDistribution: Record<string, number>;
    classificationStatus: Record<string, number>;
  } | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(false);
  const [systemLinks, setSystemLinks] = useState<{
    dataSchemaLink: string;
    classificationLink: string;
    approvalEmailLink: string;
  }>({
    dataSchemaLink: '',
    classificationLink: '',
    approvalEmailLink: ''
  });
  const [isEditingLinks, setIsEditingLinks] = useState(false);
  const [tempLinks, setTempLinks] = useState<{
    dataSchemaLink: string;
    classificationLink: string;
    approvalEmailLink: string;
  }>({
    dataSchemaLink: '',
    classificationLink: '',
    approvalEmailLink: ''
  });

  const router = useRouter();
  const { toast } = useToast();

  // Initialize params
  useEffect(() => {
    params.then(({ lang, systemId }) => {
      setLang(lang);
      setSystemId(systemId);
    });
  }, [params]);

  // Define callback functions before any early returns
  const loadSystemDetails = useCallback(async () => {
    if (!systemId) return; // Guard clause to prevent execution without systemId
    
    try {
      setIsLoading(true);
      const systems = await SystemsService.getSystems();
      const foundSystem = systems.find(s => s.id === systemId);

      if (foundSystem) {
        setSystem(foundSystem);
      } else {
        const isRTL = lang === "ar";
        toast({
          title: isRTL ? "النظام غير موجود" : "System not found",
          description: isRTL ? "لم يتم العثور على النظام المطلوب" : "The requested system was not found",
          variant: "destructive",
        });
        router.push(`/${lang}/Thiqah/DataClassification`);
      }
    } catch (error) {
      // Log error for debugging
      console.error('Error loading system:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في تحميل النظام" : "Error loading system",
        description: isRTL ? "فشل في تحميل تفاصيل النظام" : "Failed to load system details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [systemId, lang, toast, router]);

  const loadSystemData = useCallback(async () => {
    if (!systemId) return; // Guard clause to prevent execution without systemId
    
    try {
      setIsLoadingData(true);
      const data = await SystemsService.getSystemData(systemId);
      setSystemData(data);
    } catch (error) {
      // Log error for debugging
      console.error('Error loading system data:', error);
    } finally {
      setIsLoadingData(false);
    }
  }, [systemId]);

  const loadSystemContext = useCallback(async () => {
    if (!systemId) return; // Guard clause to prevent execution without systemId

    try {
      // Load both legacy context and new context points for comprehensive context
      const [context, contextPoints] = await Promise.all([
        SystemsService.getSystemContext(systemId),
        SystemsService.getSystemContextPoints(systemId)
      ]);

      // Build comprehensive context string for AI classification
      let comprehensiveContext = '';

      if (contextPoints && contextPoints.length > 0) {
        const contextByTag = contextPoints.reduce((acc, point) => {
          if (!acc[point.tag]) {
            acc[point.tag] = [];
          }
          acc[point.tag].push(point.content);
          return acc;
        }, {} as Record<string, string[]>);

        const contextSections = [];
        if (contextByTag['System Description']) {
          contextSections.push(`SYSTEM DESCRIPTION: ${contextByTag['System Description'].join(' ')}`);
        }
        if (contextByTag['System Personas']) {
          contextSections.push(`SYSTEM PERSONAS: ${contextByTag['System Personas'].join(' ')}`);
        }
        if (contextByTag['System Service Brief']) {
          contextSections.push(`SYSTEM SERVICES: ${contextByTag['System Service Brief'].join(' ')}`);
        }

        comprehensiveContext = contextSections.join(' | ');
      }

      // Create enhanced context object
      const enhancedContext = {
        ...context,
        context: comprehensiveContext || context?.context || '',
        comprehensiveContext: comprehensiveContext,
        contextPoints: context?.contextPoints || [],
        createdAt: context?.createdAt || Timestamp.now(),
        updatedAt: context?.updatedAt || Timestamp.now(),
        systemId: context?.systemId || systemId
      };

      setSystemContext(enhancedContext);
    } catch (error) {
      // Log error for debugging
      console.error('Error loading system context:', error);
    }
  }, [systemId]);

  const loadSystemDataStats = useCallback(async () => {
    if (!systemId) return;
    
    try {
      setIsLoadingStats(true);
      const data = await SystemsService.getSystemData(systemId);
      
      // Calculate statistics
      const stats = {
        totalRecords: data.length,
        confidentialityDistribution: data.reduce((acc, item) => {
          const level = item.confidentialityLevel || 'Unclassified';
          acc[level] = (acc[level] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        personalDataDistribution: data.reduce((acc, item) => {
          const hasPersonal = item.hasPersonalData ? 'Has Personal Data' : 'No Personal Data';
          acc[hasPersonal] = (acc[hasPersonal] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        tableTypeDistribution: data.reduce((acc, item) => {
          const type = item.tableType || 'Unclassified';
          acc[type] = (acc[type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        dataCategoryDistribution: data.reduce((acc, item) => {
          const category = item.dataCategory || 'Uncategorized';
          acc[category] = (acc[category] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        classificationStatus: data.reduce((acc, item) => {
          const status = item.classificationStatus || 'pending';
          acc[status] = (acc[status] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      };
      
      setSystemDataStats(stats);
    } catch (error) {
      console.error('Error loading system data stats:', error);
    } finally {
      setIsLoadingStats(false);
    }
  }, [systemId]);

  // Load system links
  const loadSystemLinks = useCallback(async () => {
    if (!systemId) return;

    try {
      const systems = await SystemsService.getSystems();
      const foundSystem = systems.find(s => s.id === systemId);

      if (foundSystem) {
        const links = {
          dataSchemaLink: foundSystem.dataSchemaLink || '',
          classificationLink: foundSystem.classificationLink || '',
          approvalEmailLink: foundSystem.approvalEmailLink || ''
        };
        setSystemLinks(links);
        setTempLinks(links);
      }
    } catch (error) {
      console.error('Error loading system links:', error);
    }
  }, [systemId]);

  // Save system links
  const saveSystemLinks = async () => {
    if (!systemId) return;

    try {
      await SystemsService.updateSystem(systemId, {
        dataSchemaLink: tempLinks.dataSchemaLink || undefined,
        classificationLink: tempLinks.classificationLink || undefined,
        approvalEmailLink: tempLinks.approvalEmailLink || undefined
      });

      setSystemLinks(tempLinks);
      setIsEditingLinks(false);

      toast({
        title: isRTL ? "تم حفظ الروابط" : "Links Saved",
        description: isRTL ? "تم حفظ روابط النظام بنجاح" : "System links have been saved successfully",
      });
    } catch (error) {
      console.error('Error saving system links:', error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: isRTL ? "فشل في حفظ الروابط" : "Failed to save links",
        variant: "destructive",
      });
    }
  };

  // Load system data when systemId is available
  useEffect(() => {
    if (systemId) {
      loadSystemDetails();
      loadSystemData();
      loadSystemContext();
      loadSystemDataStats();
      loadSystemLinks();
    }
  }, [systemId, loadSystemDetails, loadSystemData, loadSystemContext, loadSystemDataStats, loadSystemLinks]);

  // Don't render until params are loaded
  if (!lang || !systemId) {
    return <div>Loading...</div>;
  }

  const isRTL = lang === "ar";

  const handleImportData = async (data: Omit<SystemData, 'id' | 'createdAt' | 'systemId'>[], importMode: 'add' | 'replace') => {
    try {
      if (importMode === 'replace') {
        await SystemsService.replaceSystemDataBatch(systemId, data);
      } else {
        await SystemsService.addSystemDataBatch(systemId, data);
      }
      await loadSystemData(); // Refresh the data
      toast({
        title: isRTL ? "تم الاستيراد بنجاح" : "Import Successful",
        description: isRTL ?
          `تم ${importMode === 'replace' ? 'استبدال' : 'استيراد'} ${data.length} سجل` :
          `Successfully ${importMode === 'replace' ? 'replaced' : 'imported'} ${data.length} records`,
      });
    } catch (error) {
      // Log error for debugging
      console.error('Error importing data:', error);
      throw error; // Let the modal handle the error
    }
  };

  const handleDeleteAllData = async () => {
    if (!confirm(isRTL ? "هل أنت متأكد من حذف جميع البيانات؟" : "Are you sure you want to delete all data?")) {
      return;
    }

    try {
      await SystemsService.deleteAllSystemData(systemId);
      await loadSystemData(); // Refresh the data
      toast({
        title: isRTL ? "تم الحذف بنجاح" : "Delete Successful",
        description: isRTL ? "تم حذف جميع البيانات" : "All data has been deleted",
      });
    } catch (error) {
      // Log error for debugging
      console.error('Error deleting data:', error);
      toast({
        title: isRTL ? "خطأ في الحذف" : "Delete Error",
        description: isRTL ? "فشل في حذف البيانات" : "Failed to delete data",
        variant: "destructive",
      });
    }
  };



  const handleGoBack = () => {
    router.push(`/${lang}/Thiqah/DataClassification`);
  };



  const tabs = [
    {
      id: 'overview' as const,
      label: isRTL ? "نظرة عامة" : "Overview",
      subtitle: isRTL ? "إحصائيات وتحليلات" : "Analytics & Insights",
      icon: FileText
    },
    {
      id: 'data' as const,
      label: isRTL ? "بيانات النظام" : "System Data",
      subtitle: isRTL ? "إدارة البيانات" : "Data Management",
      icon: Database
    },
    {
      id: 'personal-data' as const,
      label: isRTL ? "البيانات الشخصية" : "Personal Data",
      subtitle: isRTL ? "تصنيف متقدم" : "Further Classification",
      icon: User
    },
    {
      id: 'context' as const,
      label: `${system?.name || ''} ${isRTL ? "السياق" : "Context"}`,
      subtitle: isRTL ? "السياق والخدمات" : "Context & Services",
      icon: MessageSquare
    },
    {
      id: 'classification' as const,
      label: isRTL ? "قانون حماية البيانات" : "PDPL",
      subtitle: isRTL ? "الامتثال القانوني" : "Legal Compliance",
      icon: Shield
    },
    {
      id: 'system-links' as const,
      label: isRTL ? "روابط النظام" : "System Links",
      subtitle: isRTL ? "الروابط المرجعية" : "Reference Links",
      icon: Link
    }
  ];

  if (isLoading) {
    return (
      <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
        <div className="relative min-h-screen bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
          <div className="relative z-10 flex flex-col justify-center items-center min-h-screen px-8 py-16">
            <div className="animate-pulse text-center">
              <div className="w-24 h-24 bg-white/20 rounded-3xl mx-auto mb-8"></div>
              <div className="h-12 bg-white/20 rounded-lg w-96 mx-auto mb-4"></div>
              <div className="h-6 bg-white/20 rounded-lg w-64 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!system) {
    return null;
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Simplified Header */}
      <div className="relative bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
        </div>

        <div className="relative z-10 px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            {/* Back Button */}
            <Button
              onClick={handleGoBack}
              className="bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 hover:text-white shadow-lg"
            >
              {isRTL ? <ArrowRight className="w-4 h-4 mr-2" /> : <ArrowLeft className="w-4 h-4 mr-2" />}
              {isRTL ? "العودة" : "Back"}
            </Button>


          </div>

          {/* System Header */}
          <div className="flex items-center gap-6 mb-6">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center shadow-lg border border-white/20">
              <Building className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-1 tracking-tight">
                {system.name}
              </h1>
              <p className="text-white/90 text-lg">
                {isRTL ? "إدارة النظام المتقدمة" : "Advanced System Management"}
              </p>
            </div>
          </div>

          {/* System Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[
              { icon: User, label: isRTL ? "المسؤول" : "Owner", value: system.responsibleOwner },
              { icon: Database, label: isRTL ? "مدير قاعدة البيانات" : "DBA", value: system.dba },
              { icon: Mail, label: isRTL ? "البريد الإلكتروني" : "Email", value: system.email },
              ...(system.group ? [{ icon: Building, label: isRTL ? "المجموعة" : "Group", value: isRTL ? `المجموعة ${system.group}` : `Group ${system.group}` }] : [])
            ].map((item, index) => (
              <div
                key={index}
                className="bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-300"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-white/20 text-white">
                    <item.icon className="w-4 h-4" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="text-xs font-semibold text-white/80 uppercase tracking-wider">{item.label}</div>
                    <div className="text-sm font-bold text-white truncate" title={item.value}>{item.value}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-8 py-8">
        {/* Simple Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-2xl p-1 shadow-lg border border-gray-200">
            <div className="flex gap-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-6 py-3 rounded-xl transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-[var(--brand-blue)] text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  <span className="font-medium">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="max-w-7xl mx-auto">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-8"
            >
              {/* System Overview Header */}
              <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 rounded-3xl p-8 text-white relative overflow-hidden">
                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-10">
                  <div className="absolute top-0 right-0 w-64 h-64 bg-white rounded-full -translate-y-1/2 translate-x-1/2"></div>
                  <div className="absolute bottom-0 left-0 w-48 h-48 bg-white rounded-full translate-y-1/2 -translate-x-1/2"></div>
                </div>
                
                <div className="relative z-10">
                  <div className="flex items-center gap-6 mb-6">
                    <div className="w-20 h-20 bg-white/20 rounded-3xl flex items-center justify-center backdrop-blur-sm">
                      <Database className="w-10 h-10 text-white" />
                    </div>
                    <div className="flex-1">
                      <h2 className="text-3xl font-bold mb-2">{system.name}</h2>
                      <p className="text-white/90 text-lg">
                        {isRTL ? "نظرة عامة شاملة على البيانات والتصنيفات" : "Comprehensive data overview and classifications"}
                </p>
              </div>
                  </div>
                  
                  {/* Quick Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4">
                      <div className="flex items-center gap-3">
                        <Table className="w-8 h-8 text-white/80" />
                        <div>
                          <p className="text-white/80 text-sm">{isRTL ? "إجمالي السجلات" : "Total Records"}</p>
                          <p className="text-2xl font-bold">{systemDataStats?.totalRecords || 0}</p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4">
                      <div className="flex items-center gap-3">
                        <User className="w-8 h-8 text-white/80" />
                  <div>
                          <p className="text-white/80 text-sm">{isRTL ? "المسؤول" : "Owner"}</p>
                          <p className="text-lg font-bold truncate">{system.responsibleOwner}</p>
                        </div>
                      </div>
                  </div>
                    <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4">
                      <div className="flex items-center gap-3">
                        <Database className="w-8 h-8 text-white/80" />
                  <div>
                          <p className="text-white/80 text-sm">{isRTL ? "مدير قاعدة البيانات" : "DBA"}</p>
                          <p className="text-lg font-bold truncate">{system.dba}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {isLoadingStats ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
                      <div className="animate-pulse">
                        <div className="w-12 h-12 bg-gray-200 rounded-2xl mb-4"></div>
                        <div className="h-6 bg-gray-200 rounded mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : systemDataStats ? (
                <>
                  {/* Key Metrics Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Classification Status */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                      className="bg-gradient-to-br from-[var(--brand-blue)] to-[var(--brand-blue)]/80 rounded-2xl p-6 text-white"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <CheckCircle className="w-8 h-8 text-white/80" />
                        <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                          <CheckCircle className="w-6 h-6" />
                        </div>
                      </div>
                      <h3 className="text-sm font-medium text-white/80 mb-1">
                        {isRTL ? "مُصنف بالكامل" : "Fully Classified"}
                      </h3>
                      <p className="text-3xl font-bold">
                        {systemDataStats.classificationStatus.fully_classified || 0}
                      </p>
                      <p className="text-sm text-white/70 mt-2">
                        {systemDataStats.totalRecords > 0 
                          ? `${((systemDataStats.classificationStatus.fully_classified || 0) / systemDataStats.totalRecords * 100).toFixed(1)}%`
                          : '0%'
                        } {isRTL ? "من إجمالي البيانات" : "of total data"}
                      </p>
                    </motion.div>

                    {/* Personal Data */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/90 to-cyan-500 rounded-2xl p-6 text-white"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <Users className="w-8 h-8 text-white/80" />
                        <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                          <Users className="w-6 h-6" />
                        </div>
                      </div>
                      <h3 className="text-sm font-medium text-white/80 mb-1">
                        {isRTL ? "بيانات شخصية" : "Personal Data"}
                      </h3>
                      <p className="text-3xl font-bold">
                        {systemDataStats.personalDataDistribution['Has Personal Data'] || 0}
                      </p>
                      <p className="text-sm text-white/70 mt-2">
                        {systemDataStats.totalRecords > 0 
                          ? `${(((systemDataStats.personalDataDistribution['Has Personal Data'] || 0) / systemDataStats.totalRecords) * 100).toFixed(1)}%`
                          : '0%'
                        } {isRTL ? "من إجمالي البيانات" : "of total data"}
                      </p>
                    </motion.div>

                    {/* Pending Classification */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="bg-gradient-to-br from-[var(--brand-dark-gray)] to-[var(--brand-dark-gray)]/80 rounded-2xl p-6 text-white"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <Clock className="w-8 h-8 text-white/80" />
                        <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                          <Clock className="w-6 h-6" />
                        </div>
                      </div>
                      <h3 className="text-sm font-medium text-white/80 mb-1">
                        {isRTL ? "في انتظار التصنيف" : "Pending Classification"}
                      </h3>
                      <p className="text-3xl font-bold">
                        {systemDataStats.classificationStatus.pending || 0}
                      </p>
                      <p className="text-sm text-white/70 mt-2">
                        {systemDataStats.totalRecords > 0 
                          ? `${(((systemDataStats.classificationStatus.pending || 0) / systemDataStats.totalRecords) * 100).toFixed(1)}%`
                          : '0%'
                        } {isRTL ? "من إجمالي البيانات" : "of total data"}
                      </p>
                    </motion.div>
                  </div>

                  {/* Charts Section */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Confidentiality Distribution Chart */}
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5 }}
                      className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6"
                    >
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-12 h-12 bg-[var(--brand-blue)]/10 rounded-xl flex items-center justify-center">
                          <Shield className="w-6 h-6 text-[var(--brand-blue)]" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-gray-900">
                            {isRTL ? "توزيع مستويات السرية" : "Confidentiality Distribution"}
                          </h3>
                          <p className="text-gray-600 text-sm">
                            {isRTL ? "تصنيف البيانات حسب مستوى السرية" : "Data classification by confidentiality level"}
                          </p>
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        {Object.entries(systemDataStats.confidentialityDistribution).map(([level, count], index) => {
                          const percentage = systemDataStats.totalRecords > 0 ? (count / systemDataStats.totalRecords) * 100 : 0;
                          const colors = {
                            'Public': 'bg-green-500',
                            'Confidential': 'bg-yellow-500',
                            'Secret': 'bg-orange-500',
                            'Top Secret': 'bg-red-500',
                            'Unclassified': 'bg-gray-400'
                          };
                          
                          return (
                            <div key={level} className="space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-700">{level}</span>
                                <span className="text-sm font-bold text-gray-900">{count} ({percentage.toFixed(1)}%)</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-3">
                                <motion.div
                                  initial={{ width: 0 }}
                                  animate={{ width: `${percentage}%` }}
                                  transition={{ delay: 0.5 + index * 0.1, duration: 0.8 }}
                                  className={`h-3 rounded-full ${colors[level as keyof typeof colors] || 'bg-gray-400'}`}
                                ></motion.div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </motion.div>

                    {/* Personal Data Distribution */}
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6 }}
                      className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6"
                    >
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                          <Users className="w-6 h-6 text-red-600" />
                        </div>
                  <div>
                          <h3 className="text-xl font-bold text-gray-900">
                            {isRTL ? "توزيع البيانات الشخصية" : "Personal Data Distribution"}
                          </h3>
                          <p className="text-gray-600 text-sm">
                            {isRTL ? "تحليل وجود البيانات الشخصية" : "Analysis of personal data presence"}
                          </p>
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        {Object.entries(systemDataStats.personalDataDistribution).map(([type, count], index) => {
                          const percentage = systemDataStats.totalRecords > 0 ? (count / systemDataStats.totalRecords) * 100 : 0;
                          const color = type === 'Has Personal Data' ? 'bg-red-500' : 'bg-green-500';
                          
                          return (
                            <div key={type} className="space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-700">
                                  {isRTL ? (type === 'Has Personal Data' ? 'يحتوي على بيانات شخصية' : 'لا يحتوي على بيانات شخصية') : type}
                                </span>
                                <span className="text-sm font-bold text-gray-900">{count} ({percentage.toFixed(1)}%)</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-3">
                                <motion.div
                                  initial={{ width: 0 }}
                                  animate={{ width: `${percentage}%` }}
                                  transition={{ delay: 0.6 + index * 0.1, duration: 0.8 }}
                                  className={`h-3 rounded-full ${color}`}
                                ></motion.div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </motion.div>
                  </div>

                  {/* Additional Charts Row */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Table Type Distribution */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.7 }}
                      className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6"
                    >
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                          <Table className="w-6 h-6 text-purple-600" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-gray-900">
                            {isRTL ? "توزيع أنواع الجداول" : "Table Type Distribution"}
                          </h3>
                          <p className="text-gray-600 text-sm">
                            {isRTL ? "تصنيف الجداول حسب النوع" : "Table classification by type"}
                          </p>
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        {Object.entries(systemDataStats.tableTypeDistribution).map(([type, count], index) => {
                          const percentage = systemDataStats.totalRecords > 0 ? (count / systemDataStats.totalRecords) * 100 : 0;
                          const colors = {
                            'system_table': 'bg-blue-500',
                            'data_table': 'bg-purple-500',
                            'Unclassified': 'bg-gray-400'
                          };
                          
                          return (
                            <div key={type} className="space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-700">
                                  {isRTL ? 
                                    (type === 'system_table' ? 'جدول نظام' : 
                                     type === 'data_table' ? 'جدول بيانات' : 'غير مُصنف') : 
                                    type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
                                  }
                                </span>
                                <span className="text-sm font-bold text-gray-900">{count} ({percentage.toFixed(1)}%)</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-3">
                                <motion.div
                                  initial={{ width: 0 }}
                                  animate={{ width: `${percentage}%` }}
                                  transition={{ delay: 0.7 + index * 0.1, duration: 0.8 }}
                                  className={`h-3 rounded-full ${colors[type as keyof typeof colors] || 'bg-gray-400'}`}
                                ></motion.div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </motion.div>

                    {/* Classification Progress */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.8 }}
                      className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6"
                    >
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                          <TrendingUp className="w-6 h-6 text-green-600" />
                        </div>
                    <div>
                          <h3 className="text-xl font-bold text-gray-900">
                            {isRTL ? "تقدم التصنيف" : "Classification Progress"}
                          </h3>
                          <p className="text-gray-600 text-sm">
                            {isRTL ? "حالة تقدم تصنيف البيانات" : "Data classification progress status"}
                          </p>
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        {Object.entries(systemDataStats.classificationStatus).map(([status, count], index) => {
                          const percentage = systemDataStats.totalRecords > 0 ? (count / systemDataStats.totalRecords) * 100 : 0;
                          const colors = {
                            'pending': 'bg-yellow-500',
                            'table_classified': 'bg-blue-500',
                            'fully_classified': 'bg-green-500'
                          };
                          
                          return (
                            <div key={status} className="space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-gray-700">
                                  {isRTL ? 
                                    (status === 'pending' ? 'في انتظار التصنيف' : 
                                     status === 'table_classified' ? 'مُصنف جزئياً' : 'مُصنف بالكامل') : 
                                    status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
                                  }
                                </span>
                                <span className="text-sm font-bold text-gray-900">{count} ({percentage.toFixed(1)}%)</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-3">
                                <motion.div
                                  initial={{ width: 0 }}
                                  animate={{ width: `${percentage}%` }}
                                  transition={{ delay: 0.8 + index * 0.1, duration: 0.8 }}
                                  className={`h-3 rounded-full ${colors[status as keyof typeof colors] || 'bg-gray-400'}`}
                                ></motion.div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </motion.div>
                  </div>
                </>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <BarChart3 className="w-8 h-8 text-gray-400" />
                    </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    {isRTL ? "لا توجد بيانات للعرض" : "No Data Available"}
                  </h4>
                  <p className="text-gray-600">
                    {isRTL ? "لم يتم تحميل أي بيانات لهذا النظام بعد" : "No data has been loaded for this system yet"}
                  </p>
                </div>
              )}
            </motion.div>
          )}

          {/* System Data Tab */}
          {activeTab === 'data' && (
            <motion.div
              key="data"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <SystemDataTable
                data={systemData}
                isLoading={isLoadingData}
                isRTL={isRTL}
                systemId={systemId}
                onImportClick={() => setIsImportModalOpen(true)}
                onDeleteAll={systemData.length > 0 ? handleDeleteAllData : undefined}
                onDataUpdate={loadSystemData}
                systemContext={systemContext?.context}
              />
            </motion.div>
          )}

          {/* Personal Data Tab */}
          {activeTab === 'personal-data' && (
            <motion.div
              key="personal-data"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <PersonalDataTable
                data={systemData}
                isLoading={isLoadingData}
                isRTL={isRTL}
                systemId={systemId}
                onImportClick={() => setIsImportModalOpen(true)}
                onDeleteAll={systemData.length > 0 ? handleDeleteAllData : undefined}
                onDataUpdate={loadSystemData}
              />
            </motion.div>
          )}

          {/* System Context Tab */}
          {activeTab === 'context' && (
            <motion.div
              key="context"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <SystemContextTab
                systemId={systemId}
                lang={lang}
                system={system}
              />
            </motion.div>
          )}

          {/* Classification Tab */}
          {activeTab === 'classification' && (
            <motion.div
              key="classification"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8"
            >
              <div className="text-center mb-12">
                <div className="w-24 h-24 bg-[var(--brand-blue)]/10 rounded-3xl flex items-center justify-center mx-auto mb-6">
                  <Shield className="w-12 h-12 text-[var(--brand-blue)]" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-4">
                  {isRTL ? "قانون حماية البيانات الشخصية" : "PDPL - Personal Data Protection Law"}
                </h3>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  {isRTL 
                    ? "إدارة الامتثال لقانون حماية البيانات الشخصية"
                    : "Manage compliance with Personal Data Protection Law requirements"
                  }
                </p>
              </div>

              {/* PDPL Options Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
                {[
                  {
                    id: 'dpia',
                    title: isRTL ? "تقييم أثر حماية البيانات" : "DPIA",
                    subtitle: isRTL ? "Data Protection Impact Assessment" : "Data Protection Impact Assessment",
                    description: isRTL ? "تقييم المخاطر المحتملة على البيانات الشخصية" : "Assess potential risks to personal data",
                    icon: FileCheck,
                    color: "from-[var(--brand-blue)] to-blue-600"
                  },
                  {
                    id: 'ropa',
                    title: isRTL ? "سجل أنشطة المعالجة" : "RoPA",
                    subtitle: isRTL ? "Record of Processing Activities" : "Record of Processing Activities",
                    description: isRTL ? "توثيق جميع أنشطة معالجة البيانات الشخصية" : "Document all personal data processing activities",
                    icon: ClipboardList,
                    color: "from-[var(--brand-green)] to-emerald-600"
                  },
                  {
                    id: 'consent',
                    title: isRTL ? "إدارة الموافقة" : "Consent Management",
                    subtitle: isRTL ? "Consent Management" : "Consent Management",
                    description: isRTL ? "إدارة موافقات المستخدمين على معالجة البيانات" : "Manage user consents for data processing",
                    icon: UserCheck,
                    color: "from-[var(--brand-dark-gray)] to-gray-700"
                  },
                  {
                    id: 'access',
                    title: isRTL ? "وصول المستخدمين" : "User Access",
                    subtitle: isRTL ? "User Access Rights" : "User Access Rights",
                    description: isRTL ? "إدارة حقوق الوصول والتحكم في البيانات" : "Manage data access rights and controls",
                    icon: Key,
                    color: "from-[var(--brand-blue)] to-cyan-600"
                  },
                  {
                    id: 'privacy',
                    title: isRTL ? "سياسة الخصوصية" : "Privacy Policy",
                    subtitle: isRTL ? "Privacy Policy Management" : "Privacy Policy Management",
                    description: isRTL ? "إدارة وتحديث سياسات الخصوصية" : "Manage and update privacy policies",
                    icon: FileType,
                    color: "from-[var(--brand-green)] to-teal-600"
                  }
                ].map((option) => (
                  <motion.button
                    key={option.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => {
                      // Handle option click
                      if (option.id === 'dpia') {
                        // Navigate to DPIA page
                        router.push(`/${lang}/Thiqah/DataClassification/${systemId}/DPIA`);
                      } else if (option.id === 'ropa') {
                        // Navigate to ROPA page
                        router.push(`/${lang}/Thiqah/DataClassification/${systemId}/ROPA`);
                      } else if (option.id === 'privacy') {
                        // Navigate to Privacy Policy page
                        router.push(`/${lang}/Thiqah/DataClassification/${systemId}/PrivacyPolicy`);
                      } else {
                        toast({
                          title: option.title,
                          description: isRTL ? "قريباً..." : "Coming soon...",
                        });
                      }
                    }}
                    className="group bg-white rounded-2xl shadow-lg border border-gray-200 p-6 text-left hover:shadow-xl transition-all duration-300 hover:border-gray-300"
                  >
                    <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${option.color} flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <option.icon className="w-6 h-6" />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-[var(--brand-blue)] transition-colors duration-300">
                      {option.title}
                    </h4>
                    <p className="text-sm font-medium text-gray-500 mb-3 uppercase tracking-wider">
                      {option.subtitle}
                    </p>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {option.description}
                    </p>
                    <div className="mt-4 flex items-center text-[var(--brand-blue)] text-sm font-medium">
                      {isRTL ? "ابدأ الآن" : "Get Started"}
                      <ArrowRight className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'} group-hover:translate-x-1 transition-transform duration-300`} />
                    </div>
                  </motion.button>
                ))}
              </div>
            </motion.div>
          )}

          {/* System Links Tab */}
          {activeTab === 'system-links' && (
            <motion.div
              key="system-links"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8"
            >
              <div className="text-center mb-12">
                <div className="w-24 h-24 bg-[var(--brand-blue)]/10 rounded-3xl flex items-center justify-center mx-auto mb-6">
                  <Link className="w-12 h-12 text-[var(--brand-blue)]" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-4">
                  {isRTL ? "روابط النظام" : "System Links"}
                </h3>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  {isRTL
                    ? "إدارة الروابط المرجعية المهمة للنظام"
                    : "Manage important reference links for the system"
                  }
                </p>
              </div>

              {/* Links Management */}
              <div className="max-w-4xl mx-auto">
                <div className="flex items-center justify-between mb-8">
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900">
                      {isRTL ? "الروابط المرجعية" : "Reference Links"}
                    </h4>
                    <p className="text-gray-600 text-sm mt-1">
                      {isRTL ? "أضف وأدر الروابط المهمة للنظام" : "Add and manage important system links"}
                    </p>
                  </div>
                  <Button
                    onClick={() => {
                      if (isEditingLinks) {
                        setTempLinks(systemLinks);
                        setIsEditingLinks(false);
                      } else {
                        setIsEditingLinks(true);
                      }
                    }}
                    variant={isEditingLinks ? "outline" : "default"}
                    className={`${isEditingLinks ? 'border-gray-300 text-gray-700 hover:bg-gray-50' : 'bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white'} px-6 py-2 rounded-xl`}
                  >
                    {isEditingLinks ? (
                      <>
                        <X className="w-4 h-4 mr-2" />
                        {isRTL ? "إلغاء" : "Cancel"}
                      </>
                    ) : (
                      <>
                        <Edit className="w-4 h-4 mr-2" />
                        {isRTL ? "تعديل الروابط" : "Edit Links"}
                      </>
                    )}
                  </Button>
                </div>

                {/* Links Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Data Schema Link */}
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                        <Database className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h5 className="text-lg font-bold text-gray-900">
                          {isRTL ? "مخطط البيانات" : "Data Schema"}
                        </h5>
                        <p className="text-sm text-gray-600">
                          {isRTL ? "رابط مخطط قاعدة البيانات" : "Database schema link"}
                        </p>
                      </div>
                    </div>

                    {isEditingLinks ? (
                      <input
                        type="url"
                        value={tempLinks.dataSchemaLink}
                        onChange={(e) => setTempLinks(prev => ({ ...prev, dataSchemaLink: e.target.value }))}
                        placeholder={isRTL ? "https://example.com/schema" : "https://example.com/schema"}
                        className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                      />
                    ) : (
                      <div className="space-y-2">
                        {systemLinks.dataSchemaLink ? (
                          <a
                            href={systemLinks.dataSchemaLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm font-medium hover:underline"
                          >
                            <ExternalLink className="w-4 h-4" />
                            {isRTL ? "فتح الرابط" : "Open Link"}
                          </a>
                        ) : (
                          <p className="text-gray-500 text-sm italic">
                            {isRTL ? "لم يتم إضافة رابط بعد" : "No link added yet"}
                          </p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Initial and Further Classification Link */}
                  <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                        <FileCheck className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h5 className="text-lg font-bold text-gray-900">
                          {isRTL ? "التصنيف الأولي والمتقدم" : "Initial & Further Classification"}
                        </h5>
                        <p className="text-sm text-gray-600">
                          {isRTL ? "رابط عملية التصنيف" : "Classification process link"}
                        </p>
                      </div>
                    </div>

                    {isEditingLinks ? (
                      <input
                        type="url"
                        value={tempLinks.classificationLink}
                        onChange={(e) => setTempLinks(prev => ({ ...prev, classificationLink: e.target.value }))}
                        placeholder={isRTL ? "https://example.com/classification" : "https://example.com/classification"}
                        className="w-full px-3 py-2 border border-green-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                      />
                    ) : (
                      <div className="space-y-2">
                        {systemLinks.classificationLink ? (
                          <a
                            href={systemLinks.classificationLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-2 text-green-600 hover:text-green-800 text-sm font-medium hover:underline"
                          >
                            <ExternalLink className="w-4 h-4" />
                            {isRTL ? "فتح الرابط" : "Open Link"}
                          </a>
                        ) : (
                          <p className="text-gray-500 text-sm italic">
                            {isRTL ? "لم يتم إضافة رابط بعد" : "No link added yet"}
                          </p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Approval Email Link */}
                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center">
                        <Mail className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h5 className="text-lg font-bold text-gray-900">
                          {isRTL ? "بريد الموافقة" : "Approval Email"}
                        </h5>
                        <p className="text-sm text-gray-600">
                          {isRTL ? "رابط بريد الموافقة" : "Approval email link"}
                        </p>
                      </div>
                    </div>

                    {isEditingLinks ? (
                      <input
                        type="url"
                        value={tempLinks.approvalEmailLink}
                        onChange={(e) => setTempLinks(prev => ({ ...prev, approvalEmailLink: e.target.value }))}
                        placeholder={isRTL ? "https://example.com/approval" : "https://example.com/approval"}
                        className="w-full px-3 py-2 border border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                      />
                    ) : (
                      <div className="space-y-2">
                        {systemLinks.approvalEmailLink ? (
                          <a
                            href={systemLinks.approvalEmailLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-2 text-purple-600 hover:text-purple-800 text-sm font-medium hover:underline"
                          >
                            <ExternalLink className="w-4 h-4" />
                            {isRTL ? "فتح الرابط" : "Open Link"}
                          </a>
                        ) : (
                          <p className="text-gray-500 text-sm italic">
                            {isRTL ? "لم يتم إضافة رابط بعد" : "No link added yet"}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Save Button */}
                {isEditingLinks && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-center mt-8"
                  >
                    <Button
                      onClick={saveSystemLinks}
                      className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white px-8 py-3 rounded-xl font-medium"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      {isRTL ? "حفظ الروابط" : "Save Links"}
                    </Button>
                  </motion.div>
                )}
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Modals */}
      <ExcelImportModal
        isOpen={isImportModalOpen}
        onClose={() => setIsImportModalOpen(false)}
        onImport={handleImportData}
        isRTL={isRTL}
      />

      <ContextModal
        isOpen={isContextModalOpen}
        onClose={() => setIsContextModalOpen(false)}
        systemId={systemId}
        isRTL={isRTL}
      />
    </div>
  );
}