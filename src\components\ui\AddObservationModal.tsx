"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { X, Plus, FileSearch, AlertCircle, <PERSON>h, Star } from "lucide-react";
import { ObservationRating } from "@/Firebase/firestore/services/AuditService";

interface AddObservationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (observationData: {
    name: string;
    observationNumber: string;
    rating: ObservationRating;
    description?: string;
  }) => Promise<void>;
  isRTL: boolean;
  isLoading?: boolean;
}

export function AddObservationModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  isRTL, 
  isLoading = false 
}: AddObservationModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    observationNumber: "",
    rating: ObservationRating.MEDIUM,
    description: ""
  });
  
  const [errors, setErrors] = useState<{
    name?: string;
    observationNumber?: string;
    description?: string;
  }>({});

  const handleInputChange = (field: string, value: string | ObservationRating) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: {
      name?: string;
      observationNumber?: string;
      description?: string;
    } = {};

    if (!formData.name.trim()) {
      newErrors.name = isRTL ? "اسم الملاحظة مطلوب" : "Observation name is required";
    } else if (formData.name.length < 3) {
      newErrors.name = isRTL ? "يجب أن يكون الاسم 3 أحرف على الأقل" : "Name must be at least 3 characters";
    }

    if (!formData.observationNumber.trim()) {
      newErrors.observationNumber = isRTL ? "رقم الملاحظة مطلوب" : "Observation number is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const observationData = {
        name: formData.name.trim(),
        observationNumber: formData.observationNumber.trim(),
        rating: formData.rating,
        description: formData.description.trim() || undefined
      };

      await onSubmit(observationData);
      handleClose();
    } catch (error) {
      console.error('Error creating observation:', error);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        name: "",
        observationNumber: "",
        rating: ObservationRating.MEDIUM,
        description: ""
      });
      setErrors({});
      onClose();
    }
  };

  const ratingOptions = [
    { 
      value: ObservationRating.LOW, 
      label: isRTL ? "منخفضة" : "Low", 
      color: "text-green-600 bg-green-50 border-green-200 hover:bg-green-100",
      description: isRTL ? "مخاطر منخفضة" : "Low risk"
    },
    { 
      value: ObservationRating.MEDIUM, 
      label: isRTL ? "متوسطة" : "Medium", 
      color: "text-yellow-600 bg-yellow-50 border-yellow-200 hover:bg-yellow-100",
      description: isRTL ? "مخاطر متوسطة" : "Medium risk"
    },
    { 
      value: ObservationRating.HIGH, 
      label: isRTL ? "عالية" : "High", 
      color: "text-orange-600 bg-orange-50 border-orange-200 hover:bg-orange-100",
      description: isRTL ? "مخاطر عالية" : "High risk"
    },
    { 
      value: ObservationRating.CRITICAL, 
      label: isRTL ? "حرجة" : "Critical", 
      color: "text-red-600 bg-red-50 border-red-200 hover:bg-red-100",
      description: isRTL ? "مخاطر حرجة" : "Critical risk"
    }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-gray-900/20 backdrop-blur-sm"
        onClick={handleClose}
      />

      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        transition={{ duration: 0.2 }}
        className={`relative bg-white rounded-3xl shadow-2xl border border-gray-100 w-full max-w-2xl max-h-[90vh] overflow-hidden ${isRTL ? "rtl" : "ltr"}`}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center shadow-lg border border-white/20">
                <FileSearch className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">
                  {isRTL ? "إضافة ملاحظة جديدة" : "Add New Observation"}
                </h2>
                <p className="text-white/80 text-sm">
                  {isRTL ? "تسجيل ملاحظة رقابية جديدة" : "Record a new audit observation"}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="text-white hover:bg-white/20 rounded-xl"
              disabled={isLoading}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Form Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-160px)]">
          <form onSubmit={handleSubmit} className="p-8 space-y-6">
            {/* Observation Name */}
            <div className="space-y-2">
              <label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                <FileSearch className="w-4 h-4 text-[var(--brand-blue)]" />
                {isRTL ? "اسم الملاحظة" : "Observation Name"}
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                className={`text-lg font-medium ${errors.name ? "border-red-500 focus:border-red-500" : "border-gray-200 focus:border-[var(--brand-blue)]"}`}
                placeholder={isRTL ? "أدخل اسم الملاحظة" : "Enter observation name"}
                disabled={isLoading}
              />
              {errors.name && (
                <p className="text-sm text-red-600 flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" />
                  {errors.name}
                </p>
              )}
            </div>

            {/* Observation Number */}
            <div className="space-y-2">
              <label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                <Hash className="w-4 h-4 text-[var(--brand-blue)]" />
                {isRTL ? "رقم الملاحظة" : "Observation Number"}
              </label>
              <Input
                type="text"
                value={formData.observationNumber}
                onChange={(e) => handleInputChange("observationNumber", e.target.value)}
                className={`font-medium ${errors.observationNumber ? "border-red-500 focus:border-red-500" : "border-gray-200 focus:border-[var(--brand-blue)]"}`}
                placeholder={isRTL ? "مثال: OBS-2024-001" : "e.g., OBS-2024-001"}
                disabled={isLoading}
              />
              {errors.observationNumber && (
                <p className="text-sm text-red-600 flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" />
                  {errors.observationNumber}
                </p>
              )}
            </div>

            {/* Rating */}
            <div className="space-y-3">
              <label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                <Star className="w-4 h-4 text-[var(--brand-blue)]" />
                {isRTL ? "التصنيف" : "Rating"}
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {ratingOptions.map((option) => (
                  <button
                    key={option.value}
                    type="button"
                    onClick={() => handleInputChange("rating", option.value)}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 text-left ${
                      formData.rating === option.value
                        ? `${option.color} border-current shadow-md`
                        : 'border-gray-200 text-gray-600 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                    disabled={isLoading}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-semibold text-sm">{option.label}</div>
                        <div className="text-xs opacity-80 mt-1">{option.description}</div>
                      </div>
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        formData.rating === option.value
                          ? 'border-current bg-current'
                          : 'border-gray-300'
                      }`}>
                        {formData.rating === option.value && (
                          <div className="w-full h-full bg-white rounded-full scale-50"></div>
                        )}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <label className="flex items-center gap-2 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                <AlertCircle className="w-4 h-4 text-[var(--brand-blue)]" />
                {isRTL ? "وصف الملاحظة (اختياري)" : "Description (Optional)"}
              </label>
              <Textarea
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                className="min-h-32 resize-none border-gray-200 focus:border-[var(--brand-blue)]"
                placeholder={isRTL ? "اكتب وصفاً مفصلاً للملاحظة..." : "Write a detailed description of the observation..."}
                disabled={isLoading}
              />
              {errors.description && (
                <p className="text-sm text-red-600 flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" />
                  {errors.description}
                </p>
              )}
            </div>

            {/* Info Note */}
            <div className="bg-[var(--brand-blue)]/5 border border-[var(--brand-blue)]/20 rounded-xl p-4">
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 bg-[var(--brand-blue)]/20 rounded-full flex items-center justify-center mt-0.5">
                  <AlertCircle className="w-3 h-3 text-[var(--brand-blue)]" />
                </div>
                <div className="text-sm text-[var(--brand-blue)]">
                  <p className="font-medium mb-1">
                    {isRTL ? "ملاحظة مهمة" : "Important Note"}
                  </p>
                  <p className="opacity-80">
                    {isRTL 
                      ? "يمكنك إضافة المخاطر المتعلقة بهذه الملاحظة لاحقاً من خلال صفحة تفاصيل الملاحظة"
                      : "You can add risks related to this observation later through the observation details page"
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex gap-4 pt-6 border-t border-gray-100">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                className="flex-1 border-gray-200 hover:bg-gray-50"
                disabled={isLoading}
              >
                {isRTL ? "إلغاء" : "Cancel"}
              </Button>
              <Button
                type="submit"
                className="flex-1 bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 hover:from-[var(--brand-blue)]/90 hover:to-[var(--brand-blue)]/70 text-white shadow-lg"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    {isRTL ? "جاري الحفظ..." : "Creating..."}
                  </div>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    {isRTL ? "إنشاء الملاحظة" : "Create Observation"}
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
} 