"use client";

import React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface DayStats {
  created: number;
  updated: number;
  completed: number;
  statusChanges: number;
  progressUpdates: number;
}

interface ProgressCalendarProps {
  selectedDate: Date | null;
  onDateSelect: (date: Date) => void;
  currentMonth: Date;
  onMonthChange: (date: Date) => void;
  dailyStats: Map<string, DayStats>;
  isRTL: boolean;
}

export function ProgressCalendar({
  selectedDate,
  onDateSelect,
  currentMonth,
  onMonthChange,
  dailyStats,
  isRTL
}: ProgressCalendarProps) {
  
  // Get stats for a specific date
  const getStatsForDate = (date: Date): DayStats => {
    const dateKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    return dailyStats.get(dateKey) || {
      created: 0,
      updated: 0,
      completed: 0,
      statusChanges: 0,
      progressUpdates: 0
    };
  };

  // Get activity level for styling
  const getActivityLevel = (date: Date) => {
    const stats = getStatsForDate(date);
    const totalActivity = stats.created + stats.updated + stats.completed;
    
    if (totalActivity >= 10) return 'high';
    if (totalActivity >= 5) return 'medium';
    if (totalActivity > 0) return 'low';
    return 'none';
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    
    // First day of the month
    const firstDay = new Date(year, month, 1);
    
    // Start from the first day of the week containing the first day of the month
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    // Generate 42 days (6 weeks)
    const days = [];
    const currentDate = new Date(startDate);
    
    for (let i = 0; i < 42; i++) {
      days.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return days;
  };

  // Check if date is selected
  const isSelected = (date: Date) => {
    if (!selectedDate) return false;
    return date.getDate() === selectedDate.getDate() &&
           date.getMonth() === selectedDate.getMonth() &&
           date.getFullYear() === selectedDate.getFullYear();
  };

  // Check if date is in current month
  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentMonth.getMonth();
  };

  // Check if date is today
  const isToday = (date: Date) => {
    const today = new Date();
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  };

  const days = generateCalendarDays();
  const weekDays = isRTL 
    ? ['س', 'ج', 'خ', 'أ', 'ث', 'ا', 'ح']
    : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth);
    if (direction === 'prev') {
      newMonth.setMonth(newMonth.getMonth() - 1);
    } else {
      newMonth.setMonth(newMonth.getMonth() + 1);
    }
    onMonthChange(newMonth);
  };

  return (
    <div className="w-full">
      {/* Calendar Header */}
      <div className="flex items-center justify-between mb-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigateMonth('prev')}
          className="p-2 hover:bg-[var(--brand-blue)] hover:text-white"
        >
          {isRTL ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
        </Button>

        <h3 className="text-lg font-semibold text-gray-900">
          {currentMonth.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
            month: 'long',
            year: 'numeric'
          })}
        </h3>

        <Button
          variant="outline"
          size="sm"
          onClick={() => navigateMonth('next')}
          className="p-2 hover:bg-[var(--brand-blue)] hover:text-white"
        >
          {isRTL ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
        </Button>
      </div>

      {/* Week Days Header */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {weekDays.map((day) => (
          <div key={day} className="p-2 text-center text-xs font-semibold text-gray-600 uppercase tracking-wide">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-1 mb-4">
        {days.map((date, index) => {
          const activityLevel = getActivityLevel(date);
          const stats = getStatsForDate(date);
          const totalActivity = stats.created + stats.updated + stats.completed;

          return (
            <button
              key={index}
              onClick={() => onDateSelect(date)}
              className={`
                group relative p-2 h-10 text-sm font-medium rounded-lg transition-all duration-200 hover:scale-105
                ${isCurrentMonth(date) ? 'text-gray-900' : 'text-gray-400'}
                ${isSelected(date)
                  ? 'bg-[var(--brand-blue)] text-white shadow-lg scale-105'
                  : isToday(date)
                  ? 'bg-blue-50 text-[var(--brand-blue)] border-2 border-[var(--brand-blue)] font-bold'
                  : 'hover:bg-gray-100'
                }
                ${!isCurrentMonth(date) ? 'opacity-50' : ''}
                ${totalActivity > 0 ? 'font-semibold' : ''}
              `}
              title={totalActivity > 0 ? `${totalActivity} ${isRTL ? 'نشاط' : 'activities'}` : ''}
            >
              <span className="relative z-10">{date.getDate()}</span>

              {/* Activity Indicator */}
              {totalActivity > 0 && (
                <div className="absolute top-0.5 right-0.5">
                  <div className={`w-2 h-2 rounded-full ${
                    activityLevel === 'high' ? 'bg-green-500' :
                    activityLevel === 'medium' ? 'bg-yellow-500' :
                    'bg-blue-500'
                  } ${isSelected(date) ? 'bg-white' : ''} shadow-sm`}></div>
                </div>
              )}

              {/* Activity Level Background */}
              {totalActivity > 0 && !isSelected(date) && !isToday(date) && (
                <div className={`absolute inset-0 rounded-lg opacity-20 ${
                  activityLevel === 'high' ? 'bg-green-500' :
                  activityLevel === 'medium' ? 'bg-yellow-500' :
                  'bg-blue-500'
                }`}></div>
              )}
            </button>
          );
        })}
      </div>

      {/* Compact Legend */}
      <div className="flex items-center justify-center gap-4 text-xs bg-gray-50 rounded-lg p-3">
        <div className="flex items-center gap-1.5">
          <div className="w-2.5 h-2.5 bg-blue-500 rounded-full"></div>
          <span className="text-gray-600 font-medium">
            {isRTL ? "قليل (1-4)" : "Low (1-4)"}
          </span>
        </div>
        <div className="flex items-center gap-1.5">
          <div className="w-2.5 h-2.5 bg-yellow-500 rounded-full"></div>
          <span className="text-gray-600 font-medium">
            {isRTL ? "متوسط (5-9)" : "Med (5-9)"}
          </span>
        </div>
        <div className="flex items-center gap-1.5">
          <div className="w-2.5 h-2.5 bg-green-500 rounded-full"></div>
          <span className="text-gray-600 font-medium">
            {isRTL ? "عالي (10+)" : "High (10+)"}
          </span>
        </div>
      </div>
    </div>
  );
}
