"use client";

import React, { useState, useEffect } from "react";
import { Search, FileSpreadsheet, Trash2, Plus, Loader2, Download, Eye, Edit, Filter, X, Save, CheckCircle, User, XCircle, ChevronDown, Settings, Upload } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemData, SystemsService } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";
import { SystemDataTableActions } from "@/components/ui/SystemDataTableActions";
import { SystemDataRowActions } from "@/components/ui/SystemDataRowActions";
import * as ExcelJS from 'exceljs';
import { auth } from "@/Firebase/Authentication/authConfig";
import { getUserProfile, UserRole } from "@/Firebase/firestore/services/UserService";

// Extended SystemData interface for review functionality
interface ExtendedSystemData extends SystemData {
  isReviewed?: boolean;
  reviewedBy?: string;
  reviewedAt?: string;
  needsReview?: boolean;
  pushToClient?: string;
}

interface SystemDataTableProps {
  data: ExtendedSystemData[];
  isLoading: boolean;
  isRTL: boolean;
  systemId: string;
  onImportClick: () => void;
  onDeleteAll?: () => void;
  onDataUpdate?: () => void;
  systemContext?: string;
}

export function SystemDataTable({ data, isLoading, isRTL, systemId, onImportClick, onDeleteAll, onDataUpdate, systemContext }: SystemDataTableProps) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageInput, setPageInput] = useState("");
  const [viewMode, setViewMode] = useState<'classification' | 'editing'>('classification');
  const [tableFilter, setTableFilter] = useState("");
  const [dataTypeFilter, setDataTypeFilter] = useState("");
  const [confidentialityFilter, setConfidentialityFilter] = useState("");
  const [reviewStatusFilter, setReviewStatusFilter] = useState("");
  const [personalDataFilter, setPersonalDataFilter] = useState("");
  const [reviewerFilter, setReviewerFilter] = useState("");
  const [needsReviewFilter, setNeedsReviewFilter] = useState("");
  const [pushToClientFilter, setPushToClientFilter] = useState("");
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isChangesModalOpen, setIsChangesModalOpen] = useState(false);
  const [editingRow, setEditingRow] = useState<ExtendedSystemData | null>(null);
  const [changesViewRow, setChangesViewRow] = useState<ExtendedSystemData | null>(null);
  const [editingData, setEditingData] = useState<Partial<ExtendedSystemData>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());

  // User role state
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [isLoadingUserRole, setIsLoadingUserRole] = useState(true);
  const [isClassificationDropdownOpen, setIsClassificationDropdownOpen] = useState(false);

  const rowsPerPage = viewMode === 'classification' ? 50 : 200;

  // Check if user is consultant (has access to editing)
  const isConsultant = userRole === UserRole.CONSULTANT;

  // Get user role on component mount
  useEffect(() => {
    const checkUserRole = async () => {
      try {
        const user = auth.currentUser;
        if (user) {
          const userProfile = await getUserProfile(user.uid);
          setUserRole(userProfile?.role || null);
        }
      } catch (error) {
        console.error('Error fetching user role:', error);
      } finally {
        setIsLoadingUserRole(false);
      }
    };

    checkUserRole();
  }, []);

  // Force non-consultants to classification view only
  useEffect(() => {
    if (!isLoadingUserRole && !isConsultant && viewMode === 'editing') {
      setViewMode('classification');
    }
  }, [isLoadingUserRole, isConsultant, viewMode]);

  // Filter data based on search term and additional filters
  const filteredData = data.filter(row => {
    const matchesSearch = row.tableName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      row.columnName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      row.dataType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (row.schemaName && row.schemaName.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesTable = !tableFilter || row.tableName.toLowerCase().includes(tableFilter.toLowerCase());
    const matchesDataType = !dataTypeFilter || row.dataType.toLowerCase().includes(dataTypeFilter.toLowerCase());
    const matchesConfidentiality = !confidentialityFilter || 
      (row.confidentialityLevel && row.confidentialityLevel.toLowerCase().includes(confidentialityFilter.toLowerCase()));
    
    // Review status filter
    const matchesReviewStatus = !reviewStatusFilter || 
      (reviewStatusFilter === 'reviewed' && row.isReviewed) ||
      (reviewStatusFilter === 'unreviewed' && !row.isReviewed);
    
    // Personal data filter
    const matchesPersonalData = !personalDataFilter ||
      (personalDataFilter === 'yes' && row.hasPersonalData === true) ||
      (personalDataFilter === 'no' && row.hasPersonalData === false) ||
      (personalDataFilter === 'undefined' && row.hasPersonalData === undefined);

    // Reviewer filter
    const matchesReviewer = !reviewerFilter ||
      (row.reviewedBy && row.reviewedBy.toLowerCase().includes(reviewerFilter.toLowerCase()));

    // Needs review filter
    const matchesNeedsReview = !needsReviewFilter ||
      (needsReviewFilter === 'yes' && row.needsReview === true) ||
      (needsReviewFilter === 'no' && row.needsReview !== true);

    // Push to client filter
    const matchesPushToClient = !pushToClientFilter ||
      (pushToClientFilter === 'yes' && row.pushToClient === 'Yes') ||
      (pushToClientFilter === 'no' && row.pushToClient === 'No') ||
      (pushToClientFilter === 'not_set' && (!row.pushToClient || row.pushToClient === ''));

    return matchesSearch && matchesTable && matchesDataType && matchesConfidentiality && matchesReviewStatus && matchesPersonalData && matchesReviewer && matchesNeedsReview && matchesPushToClient;
  });

  // Get unique values for filter dropdowns
  const uniqueTables = Array.from(new Set(data.map(row => row.tableName))).sort();
  const uniqueDataTypes = Array.from(new Set(data.map(row => row.dataType))).sort();
  const uniqueConfidentialityLevels = Array.from(new Set(data.filter(row => row.confidentialityLevel).map(row => row.confidentialityLevel!))).sort();
  const uniqueReviewers = Array.from(new Set(data.filter(row => row.reviewedBy).map(row => row.reviewedBy!))).sort();

  // Reset filters when switching view modes
  const handleViewModeChange = (mode: 'classification' | 'editing') => {
    // Prevent non-consultants from switching to editing mode
    if (mode === 'editing' && !isConsultant) {
      toast({
        title: isRTL ? "غير مصرح" : "Unauthorized",
        description: isRTL ? "عرض التحرير متاح للمستشارين فقط" : "Editing view is only available for consultants",
        variant: "destructive"
      });
      return;
    }

    setViewMode(mode);
    setCurrentPage(1);
    if (mode === 'classification') {
      setTableFilter("");
      setDataTypeFilter("");
      setConfidentialityFilter("");
      setReviewStatusFilter("");
      setPersonalDataFilter("");
      setReviewerFilter("");
      setNeedsReviewFilter("");
    }
  };

  // Pagination
  const totalPages = Math.ceil(filteredData.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);







  // Function to retrieve comprehensive system context
  const getSystemContext = async (systemId: string) => {
    try {
      // Get system information
      const systems = await SystemsService.getSystems();
      const currentSystem = systems.find(sys => sys.id === systemId);

      if (!currentSystem) {
        console.log('System not found');
        return {
          systemName: 'Unknown System',
          systemDescription: '',
          systemPersonas: '',
          systemServices: ''
        };
      }

      // Get comprehensive system context from Firebase
      const contextPoints = await SystemsService.getSystemContextPoints(systemId);

      let systemDescription = '';
      let systemPersonas = '';
      let systemServices = '';

      if (contextPoints && contextPoints.length > 0) {
        // Group context points by tag
        const contextByTag = contextPoints.reduce((acc, point) => {
          if (!acc[point.tag]) {
            acc[point.tag] = [];
          }
          acc[point.tag].push(point.content);
          return acc;
        }, {} as Record<string, string[]>);

        // Extract specific context sections
        if (contextByTag['System Description']) {
          systemDescription = contextByTag['System Description'].join('\n\n');
        }

        if (contextByTag['System Personas']) {
          systemPersonas = contextByTag['System Personas'].join('\n\n');
        }

        if (contextByTag['System Service Brief']) {
          systemServices = contextByTag['System Service Brief'].join('\n\n');
        }
      }

      return {
        systemName: currentSystem.name,
        systemDescription,
        systemPersonas,
        systemServices
      };
    } catch (error) {
      console.error('Error fetching system context:', error);
      return {
        systemName: 'System',
        systemDescription: '',
        systemPersonas: '',
        systemServices: ''
      };
    }
  };

  // Excel Export function with professional formatting
  const handleExcelExport = async () => {
    try {
      // Get system context first
      console.log('Fetching system context for systemId:', systemId);
      const systemContext = await getSystemContext(systemId);
      console.log('System context retrieved:', {
        systemName: systemContext.systemName,
        hasDescription: !!systemContext.systemDescription,
        hasPersonas: !!systemContext.systemPersonas,
        hasServices: !!systemContext.systemServices
      });

      // Create workbook
      const workbook = new ExcelJS.Workbook();

      console.log('Starting Excel export with', filteredData.length, 'records');
      console.log('Introduction page setup completed successfully');

      // Thiqah brand colors (ARGB format for ExcelJS)
      const thiqahBlue = 'FF23A9DB';        // --brand-blue: #23A9DB
      const thiqahDarkGray = 'FF3D3D45';    // --brand-dark-gray: #3D3D45
      const thiqahWhite = 'FFFFFFFF';       // --brand-white: #FFFFFF
      const lightGray = 'FFF8FAFC';         // Light background
      const darkText = 'FF1F2937';          // Dark text
      const greenBg = 'FFD1FAE5';           // Light green for Public
      const yellowBg = 'FFFEF3C7';          // Light yellow for Confidential
      const orangeBg = 'FFFED7AA';          // Light orange for Secret
      const redBg = 'FFFECACA';             // Light red for Top Secret

      // ===== INTRODUCTION PAGE =====
      const welcomeSheet = workbook.addWorksheet(isRTL ? 'مقدمة' : 'Introduction');

      // Set page setup for welcome sheet
      welcomeSheet.pageSetup = {
        paperSize: 9, // A4
        orientation: 'portrait',
        fitToPage: true,
        fitToWidth: 1,
        fitToHeight: 1,
        margins: {
          left: 0.7,
          right: 0.7,
          top: 0.75,
          bottom: 0.75,
          header: 0.3,
          footer: 0.3
        }
      };

      // Welcome page content

      // ===== REVOLUTIONARY MODERN INTRODUCTION PAGE DESIGN =====
      let row = 1;

      // ===== SYSTEM NAME TITLE CARD =====
      // Create a simple title card for the system name (Row 1-2)
      welcomeSheet.mergeCells(`B${row}:I${row + 1}`);
      const titleCard = welcomeSheet.getCell(`B${row}`);
      titleCard.value = isRTL ?
        `${systemContext.systemName}` :
        `${systemContext.systemName.toUpperCase()}`;
      titleCard.font = {
        name: 'Arial Black',
        size: 20,
        bold: true,
        color: { argb: 'FFFFFFFF' }
      };
      titleCard.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      titleCard.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: thiqahDarkGray } // Thiqah dark gray background
      };
      titleCard.border = {
        top: { style: 'thick', color: { argb: thiqahBlue } }, // Thiqah blue border
        left: { style: 'thick', color: { argb: thiqahBlue } },
        bottom: { style: 'thick', color: { argb: thiqahBlue } },
        right: { style: 'thick', color: { argb: thiqahBlue } }
      };

      // ===== THIQAH BRANDING BADGE =====
      // Create a premium branding badge (Row 3)
      row = 3;
      welcomeSheet.mergeCells(`D${row}:G${row}`);
      const brandBadge = welcomeSheet.getCell(`D${row}`);
      brandBadge.value = isRTL ? '🏢 شركة ثقة للخدمات التجارية' : '🏢 THIQAH BUSINESS SERVICES';
      brandBadge.font = {
        name: 'Arial',
        size: 12,
        bold: true,
        color: { argb: thiqahBlue }
      };
      brandBadge.alignment = { horizontal: 'center', vertical: 'middle' };
      brandBadge.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } };
      brandBadge.border = {
        top: { style: 'medium', color: { argb: thiqahBlue } },
        left: { style: 'medium', color: { argb: thiqahBlue } },
        bottom: { style: 'medium', color: { argb: thiqahBlue } },
        right: { style: 'medium', color: { argb: thiqahBlue } }
      };

      row = 5; // Start content from row 5

      // ===== MISSION STATEMENT BANNER =====
      // Create a striking mission banner
      welcomeSheet.mergeCells(`A${row}:J${row + 1}`);
      const missionBanner = welcomeSheet.getCell(`A${row}`);
      missionBanner.value = isRTL ?
        '🎯 المهمة: ضمان امتثال شركة ثقة الكامل لقوانين حماية البيانات الشخصية ومتطلبات هيئة الاتصالات وتقنية المعلومات' :
        '🎯 MISSION: Ensuring Thiqah\'s Full Compliance with Personal Data Protection Laws & NCA Requirements';
      missionBanner.font = {
        name: 'Arial Black',
        size: 16,
        bold: true,
        color: { argb: 'FFFFFFFF' }
      };
      missionBanner.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      missionBanner.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahBlue } }; // Thiqah blue background
      missionBanner.border = {
        top: { style: 'thick', color: { argb: thiqahDarkGray } },
        left: { style: 'thick', color: { argb: thiqahDarkGray } },
        bottom: { style: 'thick', color: { argb: thiqahDarkGray } },
        right: { style: 'thick', color: { argb: thiqahDarkGray } }
      };
      row += 3; // Move to next section

      // ===== EXECUTIVE DASHBOARD CARDS =====
      // Create modern dashboard-style cards with Thiqah brand colors
      const dashboardCards = [
        {
          icon: '🔒',
          title: isRTL ? 'الامتثال التنظيمي' : 'REGULATORY COMPLIANCE',
          subtitle: isRTL ? 'PDPL & NCA' : 'PDPL & NCA',
          description: isRTL ? 'قوانين حماية البيانات' : 'Data Protection Laws',
          color: thiqahBlue, // Thiqah brand blue
          bgColor: 'FFDBEAFE'
        },
        {
          icon: '📊',
          title: isRTL ? 'تحليل شامل' : 'COMPREHENSIVE ANALYSIS',
          subtitle: isRTL ? `${filteredData.length.toLocaleString()} سجل` : `${filteredData.length.toLocaleString()} Records`,
          description: isRTL ? 'تصنيف كامل للبيانات' : 'Complete Data Classification',
          color: thiqahDarkGray, // Thiqah brand dark gray
          bgColor: 'FFF8FAFC'
        },
        {
          icon: '🏢',
          title: isRTL ? 'شركة ثقة' : 'THIQAH EXCELLENCE',
          subtitle: isRTL ? 'الخدمات التجارية' : 'Business Services',
          description: isRTL ? 'معايير عالمية' : 'International Standards',
          color: thiqahWhite, // White text on dark background
          bgColor: thiqahDarkGray
        }
      ];

      // Create dashboard cards in a row with proper spacing
      dashboardCards.forEach((card, index) => {
        const colStart = 2 + (index * 2); // B, D, F columns with spacing
        const colEnd = colStart + 1; // C, E, G columns

        // Skip the third card (index 2) to clear columns G-H
        if (index === 2) {
          return; // Skip this card to clear the black box content
        }

        // Card container (3 rows high to avoid overlaps)
        welcomeSheet.mergeCells(`${String.fromCharCode(65 + colStart)}${row}:${String.fromCharCode(65 + colEnd)}${row + 2}`);
        const cardCell = welcomeSheet.getCell(`${String.fromCharCode(65 + colStart)}${row}`);

        cardCell.value = `${card.icon}\n${card.title}\n${card.subtitle}\n${card.description}`;
        cardCell.font = {
          name: 'Arial',
          size: 10,
          bold: true,
          color: { argb: card.color }
        };
        cardCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        cardCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: card.bgColor } };
        cardCell.border = {
          top: { style: 'thick', color: { argb: card.color } },
          left: { style: 'thick', color: { argb: card.color } },
          bottom: { style: 'thick', color: { argb: card.color } },
          right: { style: 'thick', color: { argb: card.color } }
        };
      });

      row += 4; // Move past dashboard cards

      // ===== DOCUMENT OBJECTIVE SECTION =====
      // Add spacing before document objective
      row += 1;

      // Create document objective header
      welcomeSheet.mergeCells(`A${row}:J${row}`);
      const objectiveHeader = welcomeSheet.getCell(`A${row}`);
      objectiveHeader.value = isRTL ? '📋 هدف الوثيقة' : '📋 DOCUMENT OBJECTIVE';
      objectiveHeader.font = {
        name: 'Arial Black',
        size: 18,
        bold: true,
        color: { argb: 'FFFFFFFF' }
      };
      objectiveHeader.alignment = { horizontal: 'center', vertical: 'middle' };
      objectiveHeader.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahDarkGray } }; // Thiqah dark gray background
      objectiveHeader.border = {
        top: { style: 'thick', color: { argb: thiqahBlue } }, // Thiqah blue border
        left: { style: 'thick', color: { argb: thiqahBlue } },
        bottom: { style: 'thick', color: { argb: thiqahBlue } },
        right: { style: 'thick', color: { argb: thiqahBlue } }
      };
      row += 2; // Move to content

      // ===== DOCUMENT OBJECTIVE CONTENT =====
      const objectiveText = isRTL ?
        `🎯 هدف هذه الوثيقة هو تصنيف نظام "${systemContext.systemName}" وفقاً لمعيار إدارة البيانات الوطني (NDMO) لتحقيق الامتثال الكامل لهيئة الأمن السيبراني الوطني وقوانين حماية البيانات الشخصية.\n\n🔐 مستويات التصنيف:\n\n🟢 عام (Public): يمكن مشاركتها مع أي شخص دون قيود أمنية\n\n🟡 سري (Confidential): للاستخدام الداخلي فقط، قد تسبب ضرراً محدوداً إذا تم الكشف عنها\n\n🟠 سري جداً (Secret): معلومات حساسة جداً، قد تسبب ضرراً كبيراً للمؤسسة إذا تم الكشف عنها\n\n🔴 سري للغاية (Top Secret): معلومات بالغة الحساسية، قد تسبب ضرراً جسيماً للأمن القومي أو المؤسسة\n\n👤 كما نهدف إلى تصنيف البيانات الشخصية وتوضيح ما هو شخصي وما ليس كذلك وفقاً لقانون حماية البيانات الشخصية السعودي.` :
        `🎯 This document aims to classify the "${systemContext.systemName}" system according to the National Data Management Standard (NDMO) to achieve full compliance with the National Cybersecurity Authority and Personal Data Protection Laws.\n\n🔐 Classification Levels:\n\n🟢 Public: Can be shared with anyone without security restrictions\n\n🟡 Confidential: For internal use only, may cause limited harm if disclosed\n\n🟠 Secret: Highly sensitive information that could cause significant harm to the organization if disclosed\n\n🔴 Top Secret: Extremely sensitive information that could cause severe damage to national security or the organization if disclosed\n\n👤 We also aim to classify personal data and explain what constitutes personal information and what does not, according to Saudi Personal Data Protection Law.`;

      welcomeSheet.mergeCells(`B${row}:I${row + 6}`);
      const objectiveCard = welcomeSheet.getCell(`B${row}`);
      objectiveCard.value = objectiveText;
      objectiveCard.font = {
        name: 'Arial',
        size: 11,
        bold: true,
        color: { argb: thiqahDarkGray } // Thiqah brand dark gray
      };
      objectiveCard.alignment = { horizontal: 'left', vertical: 'top', wrapText: true };
      objectiveCard.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahWhite } }; // Thiqah white background
      objectiveCard.border = {
        top: { style: 'thick', color: { argb: thiqahBlue } },
        left: { style: 'thick', color: { argb: thiqahBlue } },
        bottom: { style: 'thick', color: { argb: thiqahBlue } },
        right: { style: 'thick', color: { argb: thiqahBlue } }
      };
      row += 8; // Move past objective card

      // ===== SYSTEM CONTEXT SHOWCASE =====
      // Create a modern showcase for system context information
      const contextSections = [];

      if (systemContext.systemDescription) {
        contextSections.push({
          icon: '🏢',
          title: isRTL ? 'وصف النظام' : 'SYSTEM DESCRIPTION',
          content: systemContext.systemDescription,
          color: thiqahBlue, // Thiqah brand blue
          bgColor: 'FFDBEAFE'
        });
      }

      if (systemContext.systemPersonas) {
        contextSections.push({
          icon: '👥',
          title: isRTL ? 'مستخدمو النظام' : 'SYSTEM USERS',
          content: systemContext.systemPersonas,
          color: thiqahDarkGray, // Thiqah brand dark gray
          bgColor: 'FFF8FAFC'
        });
      }

      if (systemContext.systemServices) {
        contextSections.push({
          icon: '⚙️',
          title: isRTL ? 'خدمات النظام' : 'SYSTEM SERVICES',
          content: systemContext.systemServices,
          color: thiqahBlue, // Thiqah brand blue
          bgColor: 'FFDBEAFE'
        });
      }

      // Display context sections as modern cards with proper spacing
      contextSections.forEach((section, index) => {
        // Add spacing between sections
        if (index > 0) {
          row += 1;
        }

        // Section header with modern styling
        welcomeSheet.mergeCells(`A${row}:J${row}`);
        const sectionHeader = welcomeSheet.getCell(`A${row}`);
        sectionHeader.value = `${section.icon} ${section.title}`;
        sectionHeader.font = {
          name: 'Arial Black',
          size: 16,
          bold: true,
          color: { argb: 'FFFFFFFF' }
        };
        sectionHeader.alignment = { horizontal: 'center', vertical: 'middle' };
        sectionHeader.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: section.color } };
        sectionHeader.border = {
          top: { style: 'thick', color: { argb: section.color } },
          left: { style: 'thick', color: { argb: section.color } },
          bottom: { style: 'thick', color: { argb: section.color } },
          right: { style: 'thick', color: { argb: section.color } }
        };
        row += 1;

        // Content card with modern design using Thiqah colors
        welcomeSheet.mergeCells(`B${row}:I${row + 2}`);
        const contentCard = welcomeSheet.getCell(`B${row}`);
        contentCard.value = section.content;
        contentCard.font = {
          name: 'Arial',
          size: 11,
          color: { argb: thiqahDarkGray } // Thiqah brand dark gray
        };
        contentCard.alignment = { horizontal: 'left', vertical: 'top', wrapText: true };
        contentCard.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: section.bgColor } };
        contentCard.border = {
          top: { style: 'medium', color: { argb: section.color } },
          left: { style: 'thick', color: { argb: section.color } },
          bottom: { style: 'thick', color: { argb: section.color } },
          right: { style: 'thick', color: { argb: section.color } }
        };
        row += 4; // Move to next section
      });









      // ===== USAGE GUIDELINES SECTION =====
      // Add some spacing
      row += 2;

      // Usage guidelines header (2 rows)
      welcomeSheet.mergeCells(`B${row}:I${row + 1}`);
      const guidelinesHeaderCell = welcomeSheet.getCell(`B${row}`);
      guidelinesHeaderCell.value = isRTL ? '📖 إرشادات الاستخدام والتنقل' : '📖 USAGE GUIDELINES & NAVIGATION';
      guidelinesHeaderCell.font = {
        name: 'Calibri',
        size: 16,
        bold: true,
        color: { argb: thiqahWhite }
      };
      guidelinesHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };
      guidelinesHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF059669' } };
      row += 3; // Move to content

      // Usage guidelines content (3 rows)
      const usageGuidelines = isRTL ?
        '1. ابدأ بمراجعة صفحة الملخص للحصول على نظرة عامة شاملة عن حالة البيانات\n2. استخدم صفحة البيانات التفصيلية للتحليل المتعمق والمراجعة الدقيقة\n3. انتبه للتصنيفات اللونية: الأخضر (عام)، الأصفر (سري)، البرتقالي (سري جداً)، الأحمر (سري للغاية)\n4. راجع التبريرات المقدمة لكل تصنيف لفهم الأسس العلمية للقرارات\n5. استخدم المرشحات المتقدمة للبحث والتصفية حسب المعايير المختلفة\n6. تأكد من مراجعة جميع البيانات الشخصية المحددة والتحقق من دقة التصنيف' :
        '1. Start by reviewing the Summary page for a comprehensive overview of data status\n2. Use the Detailed Data page for in-depth analysis and precise review\n3. Pay attention to color classifications: Green (Public), Yellow (Confidential), Orange (Secret), Red (Top Secret)\n4. Review the justifications provided for each classification to understand the scientific basis for decisions\n5. Use advanced filters to search and filter according to different criteria\n6. Ensure review of all identified personal data and verify classification accuracy';

      welcomeSheet.mergeCells(`B${row}:I${row + 2}`);
      const guidelinesCell = welcomeSheet.getCell(`B${row}`);
      guidelinesCell.value = usageGuidelines;
      guidelinesCell.font = {
        name: 'Calibri',
        size: 11,
        color: { argb: darkText }
      };
      guidelinesCell.alignment = { horizontal: 'left', vertical: 'top', wrapText: true };
      guidelinesCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFECFDF5' } };
      guidelinesCell.border = {
        top: { style: 'thin', color: { argb: 'FF059669' } },
        left: { style: 'thin', color: { argb: 'FF059669' } },
        bottom: { style: 'thin', color: { argb: 'FF059669' } },
        right: { style: 'thin', color: { argb: 'FF059669' } }
      };
      row += 4; // Move to next section

      // ===== KEY FEATURES SECTION =====
      // Add some spacing
      row += 2;

      // Key features header (2 rows)
      welcomeSheet.mergeCells(`B${row}:I${row + 1}`);
      const featuresHeaderCell = welcomeSheet.getCell(`B${row}`);
      featuresHeaderCell.value = isRTL ? '🌟 الميزات الرئيسية والقيمة المضافة' : '🌟 KEY FEATURES & ADDED VALUE';
      featuresHeaderCell.font = {
        name: 'Calibri',
        size: 16,
        bold: true,
        color: { argb: thiqahWhite }
      };
      featuresHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };
      featuresHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF8B5CF6' } };
      row += 3; // Move to content

      // Key features content (5 rows)
      const keyFeatures = isRTL ?
        '• تصنيف متقدم للبيانات لضمان الدقة والشمولية\n• تحليل شامل للبيانات الشخصية الحساسة وفقاً للمعايير الدولية\n• تقييم مخاطر متطور مع توصيات عملية قابلة للتنفيذ\n• توثيق شامل للمبررات العلمية لكل تصنيف وقرار\n• واجهة مستخدم متقدمة مع إمكانيات مراجعة وموافقة مهنية\n• تقارير تحليلية متطورة مع مؤشرات أداء رئيسية\n• امتثال كامل للوائح المحلية والدولية لحماية البيانات' :
        '• Advanced data classification for accuracy and completeness\n• Comprehensive analysis of sensitive personal data according to international standards\n• Advanced risk assessment with practical actionable recommendations\n• Comprehensive documentation of scientific justifications for each classification and decision\n• Advanced user interface with professional review and approval capabilities\n• Sophisticated analytical reports with key performance indicators\n• Full compliance with local and international data protection regulations';

      welcomeSheet.mergeCells(`B${row}:I${row + 4}`);
      const featuresCell = welcomeSheet.getCell(`B${row}`);
      featuresCell.value = keyFeatures;
      featuresCell.font = {
        name: 'Calibri',
        size: 11,
        color: { argb: darkText }
      };
      featuresCell.alignment = { horizontal: 'left', vertical: 'top', wrapText: true };
      featuresCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF3E8FF' } };
      featuresCell.border = {
        top: { style: 'thin', color: { argb: 'FF8B5CF6' } },
        left: { style: 'thin', color: { argb: 'FF8B5CF6' } },
        bottom: { style: 'thin', color: { argb: 'FF8B5CF6' } },
        right: { style: 'thin', color: { argb: 'FF8B5CF6' } }
      };
      row += 6; // Move to next section

      // ===== COMPLIANCE NOTICE =====
      // Add some spacing
      row += 2;

      // Enhanced compliance notice (4 rows)
      welcomeSheet.mergeCells(`B${row}:I${row + 3}`);
      const complianceCell = welcomeSheet.getCell(`B${row}`);
      complianceCell.value = isRTL ?
        '⚖️ إشعار الامتثال والسرية المهنية\n\nتم إنشاء هذا التقرير وفقاً لأعلى معايير الجودة والامتثال للوائح حماية البيانات الشخصية السعودية واللائحة العامة لحماية البيانات الأوروبية (GDPR). يحتوي التقرير على معلومات حساسة وسرية تتطلب التعامل معها بأقصى درجات الحذر والسرية المهنية. يُمنع منعاً باتاً مشاركة أو توزيع هذا التقرير خارج نطاق الأشخاص المخولين. جميع البيانات المعروضة محمية بموجب قوانين حماية البيانات والملكية الفكرية.' :
        '⚖️ COMPLIANCE & PROFESSIONAL CONFIDENTIALITY NOTICE\n\nThis report has been generated according to the highest quality standards and compliance with Saudi Personal Data Protection Law and European General Data Protection Regulation (GDPR). The report contains sensitive and confidential information requiring handling with utmost care and professional confidentiality. Sharing or distributing this report outside authorized personnel is strictly prohibited. All displayed data is protected under data protection and intellectual property laws.';
      complianceCell.font = {
        name: 'Calibri',
        size: 10,
        bold: true,
        color: { argb: 'FF7C2D12' }
      };
      complianceCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      complianceCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFEF2F2' } };
      complianceCell.border = {
        top: { style: 'medium', color: { argb: 'FFDC2626' } },
        left: { style: 'medium', color: { argb: 'FFDC2626' } },
        bottom: { style: 'medium', color: { argb: 'FFDC2626' } },
        right: { style: 'medium', color: { argb: 'FFDC2626' } }
      };
      row += 5; // Move to branding

      // Enhanced Thiqah branding footer (3 rows)
      welcomeSheet.mergeCells(`B${row}:I${row + 2}`);
      const brandingCell = welcomeSheet.getCell(`B${row}`);
      brandingCell.value = isRTL ?
        '🏢 شركة ثقة للاستشارات - الرائدة في حلول حماية البيانات والامتثال التنظيمي\n📧 <EMAIL> | 🌐 www.thiqah.sa | 📞 +966-11-XXXXXXX\nتقرير تم إنشاؤه بواسطة نظام إدارة البيانات المتقدم' :
        '🏢 Thiqah Consulting - Leading Provider of Data Protection and Regulatory Compliance Solutions\n📧 <EMAIL> | 🌐 www.thiqah.sa | 📞 +966-11-XXXXXXX\nReport Generated by Advanced Data Management System';
      brandingCell.font = {
        name: 'Calibri',
        size: 11,
        bold: true,
        color: { argb: thiqahBlue }
      };
      brandingCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      brandingCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: lightGray } };
      brandingCell.border = {
        top: { style: 'thin', color: { argb: thiqahBlue } },
        left: { style: 'thin', color: { argb: thiqahBlue } },
        bottom: { style: 'thin', color: { argb: thiqahBlue } },
        right: { style: 'thin', color: { argb: thiqahBlue } }
      };
      row += 4; // Move to bottom bar

      // Bottom brand bar
      welcomeSheet.mergeCells(`A${row}:J${row}`);
      const bottomBrandBarCell = welcomeSheet.getCell(`A${row}`);
      bottomBrandBarCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahBlue } };
      bottomBrandBarCell.value = '';

      // Set column widths for welcome sheet with optimized spacing
      welcomeSheet.columns = [
        { width: 3 },   // A - Left margin
        { width: 12 },  // B - Content start
        { width: 20 },  // C - Main content
        { width: 18 },  // D - Content
        { width: 20 },  // E - Content
        { width: 20 },  // F - Content
        { width: 18 },  // G - Content
        { width: 20 },  // H - Content
        { width: 12 },  // I - Content end
        { width: 3 }    // J - Right margin
      ];

      // Set row heights for modern introduction design
      for (let i = 1; i <= row; i++) {
        const currentRow = welcomeSheet.getRow(i);
        if (i >= 1 && i <= 2) {
          currentRow.height = 30; // System name title card
        } else if (i === 3) {
          currentRow.height = 20; // Branding badge
        } else if (i >= 5 && i <= 6) {
          currentRow.height = 30; // Mission banner
        } else if (i >= 8 && i <= 10) {
          currentRow.height = 25; // Dashboard cards
        } else if (i >= 12 && i <= 13) {
          currentRow.height = 30; // System spotlight header
        } else if (i >= 14 && i <= 18) {
          currentRow.height = 25; // Executive summary card
        } else if (i % 5 === 0 || i % 5 === 1) {
          currentRow.height = 30; // Context section headers
        } else if (i % 5 >= 2 && i % 5 <= 4) {
          currentRow.height = 22; // Context content cards
        } else {
          currentRow.height = 20; // Regular content
        }
      }

      console.log('Introduction page completed successfully with', row, 'rows');

      // ===== ADVANCED ANALYTICS & STATISTICS SUMMARY PAGE =====
      const summarySheet = workbook.addWorksheet(isRTL ? 'التحليلات والإحصائيات' : 'Analytics & Statistics');

      // Set page setup for summary sheet
      summarySheet.pageSetup = {
        paperSize: 9, // A4
        orientation: 'portrait',
        fitToPage: true,
        fitToWidth: 1,
        fitToHeight: 1,
        margins: {
          left: 0.7,
          right: 0.7,
          top: 0.75,
          bottom: 0.75,
          header: 0.3,
          footer: 0.3
        }
      };

      // Calculate comprehensive statistics
      const totalRecords = filteredData.length;
      const uniqueTables = new Set(filteredData.map(row => row.tableName)).size;
      const uniqueSchemas = new Set(filteredData.filter(row => row.schemaName).map(row => row.schemaName)).size;
      const classifiedRecords = filteredData.filter(row => row.confidentialityLevel).length;
      const personalDataRecords = filteredData.filter(row => row.hasPersonalData === true).length;
      const reviewedRecords = filteredData.filter(row => row.isReviewed).length;

      // Advanced statistics
      const publicRecords = filteredData.filter(row => row.confidentialityLevel === 'Public').length;
      const confidentialRecords = filteredData.filter(row => row.confidentialityLevel === 'Confidential').length;
      const secretRecords = filteredData.filter(row => row.confidentialityLevel === 'Secret').length;
      const topSecretRecords = filteredData.filter(row => row.confidentialityLevel === 'Top Secret').length;

      // Completion rates
      const classificationRate = totalRecords > 0 ? ((classifiedRecords / totalRecords) * 100).toFixed(1) : '0';
      const reviewRate = totalRecords > 0 ? ((reviewedRecords / totalRecords) * 100).toFixed(1) : '0';
      const personalDataRate = totalRecords > 0 ? ((personalDataRecords / totalRecords) * 100).toFixed(1) : '0';

      let summaryRow = 1;

      // ===== PROFESSIONAL HEADER SECTION =====
      // Top brand bar
      summarySheet.mergeCells(`A${summaryRow}:J${summaryRow}`);
      const summaryBrandBarCell = summarySheet.getCell(`A${summaryRow}`);
      summaryBrandBarCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahBlue } };
      summaryBrandBarCell.value = '';
      summaryRow += 2; // Move to row 3

      // Main title (Rows 3-5)
      summarySheet.mergeCells(`B${summaryRow}:I${summaryRow + 2}`);
      const summaryTitleCell = summarySheet.getCell(`B${summaryRow}`);
      summaryTitleCell.value = isRTL ? 'لوحة التحليلات والإحصائيات المتقدمة' : 'ADVANCED ANALYTICS & STATISTICS DASHBOARD';
      summaryTitleCell.font = {
        name: 'Calibri',
        size: 26,
        bold: true,
        color: { argb: thiqahBlue }
      };
      summaryTitleCell.alignment = { horizontal: 'center', vertical: 'middle' };
      summaryTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8FAFC' } };
      summaryTitleCell.border = {
        top: { style: 'thick', color: { argb: thiqahBlue } },
        left: { style: 'thick', color: { argb: thiqahBlue } },
        bottom: { style: 'thick', color: { argb: thiqahBlue } },
        right: { style: 'thick', color: { argb: thiqahBlue } }
      };
      summaryRow += 4; // Move to row 7

      // Subtitle (Rows 7-8)
      summarySheet.mergeCells(`B${summaryRow}:I${summaryRow + 1}`);
      const summarySubtitleCell = summarySheet.getCell(`B${summaryRow}`);
      summarySubtitleCell.value = isRTL ?
        'تحليل شامل ومتعمق لحالة البيانات مع مؤشرات الأداء الرئيسية ومقاييس الامتثال والجودة' :
        'Comprehensive In-Depth Data Status Analysis with Key Performance Indicators and Compliance Quality Metrics';
      summarySubtitleCell.font = {
        name: 'Calibri',
        size: 14,
        italic: true,
        color: { argb: thiqahDarkGray }
      };
      summarySubtitleCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      summarySubtitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } };
      summaryRow += 3; // Move to row 10

      // ===== EXECUTIVE SUMMARY METRICS =====
      // Section header
      summarySheet.mergeCells(`B${summaryRow}:I${summaryRow + 1}`);
      const execMetricsHeaderCell = summarySheet.getCell(`B${summaryRow}`);
      execMetricsHeaderCell.value = isRTL ? '📊 المؤشرات التنفيذية الرئيسية' : '📊 EXECUTIVE KEY PERFORMANCE INDICATORS';
      execMetricsHeaderCell.font = {
        name: 'Calibri',
        size: 18,
        bold: true,
        color: { argb: thiqahWhite }
      };
      execMetricsHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };
      execMetricsHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahBlue } };
      execMetricsHeaderCell.border = {
        top: { style: 'medium', color: { argb: thiqahDarkGray } },
        left: { style: 'medium', color: { argb: thiqahDarkGray } },
        bottom: { style: 'medium', color: { argb: thiqahDarkGray } },
        right: { style: 'medium', color: { argb: thiqahDarkGray } }
      };
      summaryRow += 3; // Move to row 13

      // ===== REDESIGNED EXECUTIVE METRICS - CLEAN & PROFESSIONAL =====
      // Create 4 distinct, well-spaced metric cards
      const executiveMetrics = [
        {
          icon: '📊',
          title: isRTL ? 'إجمالي السجلات' : 'TOTAL RECORDS',
          value: totalRecords.toLocaleString(),
          subtitle: isRTL ? `${uniqueTables} جدول • ${uniqueSchemas} مخطط` : `${uniqueTables} Tables • ${uniqueSchemas} Schemas`,
          color: thiqahBlue,
          bgColor: 'FFDBEAFE'
        },
        {
          icon: '🔐',
          title: isRTL ? 'معدل التصنيف' : 'CLASSIFICATION RATE',
          value: `${classificationRate}%`,
          subtitle: isRTL ? `${classifiedRecords.toLocaleString()} مصنف` : `${classifiedRecords.toLocaleString()} Classified`,
          color: 'FF10B981',
          bgColor: 'FFDCFCE7'
        },
        {
          icon: '👤',
          title: isRTL ? 'البيانات الشخصية' : 'PERSONAL DATA',
          value: `${personalDataRate}%`,
          subtitle: isRTL ? `${personalDataRecords.toLocaleString()} سجل حساس` : `${personalDataRecords.toLocaleString()} Sensitive Records`,
          color: 'FFEF4444',
          bgColor: 'FFFECACA'
        },
        {
          icon: '✅',
          title: isRTL ? 'معدل المراجعة' : 'REVIEW RATE',
          value: `${reviewRate}%`,
          subtitle: isRTL ? `${reviewedRecords.toLocaleString()} تم مراجعته` : `${reviewedRecords.toLocaleString()} Reviewed`,
          color: 'FF8B5CF6',
          bgColor: 'FFF3E8FF'
        }
      ];

      // Create clean, well-spaced metric cards in a single row
      const metricStartCol = 2; // Start from column B
      const metricRow = summaryRow;

      executiveMetrics.forEach((metric, index) => {
        const colStart = metricStartCol + (index * 2); // 2 columns per card with spacing
        const colEnd = colStart + 1; // Each card spans 2 columns

        // Card header with icon and title
        summarySheet.mergeCells(`${String.fromCharCode(65 + colStart)}${metricRow}:${String.fromCharCode(65 + colEnd)}${metricRow}`);
        const headerCell = summarySheet.getCell(`${String.fromCharCode(65 + colStart)}${metricRow}`);
        headerCell.value = `${metric.icon} ${metric.title}`;
        headerCell.font = {
          name: 'Arial Black',
          size: 12,
          bold: true,
          color: { argb: thiqahWhite }
        };
        headerCell.alignment = { horizontal: 'center', vertical: 'middle' };
        headerCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: metric.color } };
        headerCell.border = {
          top: { style: 'thick', color: { argb: metric.color } },
          left: { style: 'thick', color: { argb: metric.color } },
          bottom: { style: 'thin', color: { argb: metric.color } },
          right: { style: 'thick', color: { argb: metric.color } }
        };

        // Card value (large, prominent number)
        summarySheet.mergeCells(`${String.fromCharCode(65 + colStart)}${metricRow + 1}:${String.fromCharCode(65 + colEnd)}${metricRow + 1}`);
        const valueCell = summarySheet.getCell(`${String.fromCharCode(65 + colStart)}${metricRow + 1}`);
        valueCell.value = metric.value;
        valueCell.font = {
          name: 'Arial Black',
          size: 28,
          bold: true,
          color: { argb: metric.color }
        };
        valueCell.alignment = { horizontal: 'center', vertical: 'middle' };
        valueCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } };
        valueCell.border = {
          top: { style: 'thin', color: { argb: metric.color } },
          left: { style: 'thick', color: { argb: metric.color } },
          bottom: { style: 'thin', color: { argb: metric.color } },
          right: { style: 'thick', color: { argb: metric.color } }
        };

        // Card subtitle (additional info)
        summarySheet.mergeCells(`${String.fromCharCode(65 + colStart)}${metricRow + 2}:${String.fromCharCode(65 + colEnd)}${metricRow + 2}`);
        const subtitleCell = summarySheet.getCell(`${String.fromCharCode(65 + colStart)}${metricRow + 2}`);
        subtitleCell.value = metric.subtitle;
        subtitleCell.font = {
          name: 'Arial',
          size: 10,
          bold: true,
          color: { argb: thiqahDarkGray }
        };
        subtitleCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        subtitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: metric.bgColor } };
        subtitleCell.border = {
          top: { style: 'thin', color: { argb: metric.color } },
          left: { style: 'thick', color: { argb: metric.color } },
          bottom: { style: 'thick', color: { argb: metric.color } },
          right: { style: 'thick', color: { argb: metric.color } }
        };
      });

      summaryRow += 5; // Move past the metric cards

      // ===== REDESIGNED CLASSIFICATION BREAKDOWN - VISUAL & CLEAR =====
      // Add spacing
      summaryRow += 2;

      // Section header with better styling
      summarySheet.mergeCells(`B${summaryRow}:I${summaryRow + 1}`);
      const classificationHeaderCell = summarySheet.getCell(`B${summaryRow}`);
      classificationHeaderCell.value = isRTL ? '🔐 تفصيل مستويات التصنيف الأمني' : '🔐 SECURITY CLASSIFICATION LEVELS';
      classificationHeaderCell.font = {
        name: 'Arial Black',
        size: 20,
        bold: true,
        color: { argb: thiqahWhite }
      };
      classificationHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };
      classificationHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahDarkGray } };
      classificationHeaderCell.border = {
        top: { style: 'thick', color: { argb: thiqahBlue } },
        left: { style: 'thick', color: { argb: thiqahBlue } },
        bottom: { style: 'thick', color: { argb: thiqahBlue } },
        right: { style: 'thick', color: { argb: thiqahBlue } }
      };
      summaryRow += 3; // Move to content

      // Classification levels with proper system colors and design
      const classificationLevels = [
        {
          icon: '🟢',
          level: isRTL ? 'عام' : 'PUBLIC',
          count: publicRecords,
          percentage: totalRecords > 0 ? ((publicRecords / totalRecords) * 100).toFixed(1) : '0',
          description: isRTL ? 'بيانات غير حساسة • آمنة للنشر العام' : 'Non-sensitive data • Safe for public disclosure',
          color: 'FF10B981', // Green
          bgColor: 'FFDCFCE7',
          textColor: 'FF065F46'
        },
        {
          icon: '🟡',
          level: isRTL ? 'سري' : 'CONFIDENTIAL',
          count: confidentialRecords,
          percentage: totalRecords > 0 ? ((confidentialRecords / totalRecords) * 100).toFixed(1) : '0',
          description: isRTL ? 'بيانات حساسة • تتطلب حماية محدودة' : 'Sensitive data • Requires limited protection',
          color: 'FFF59E0B', // Yellow/Orange
          bgColor: 'FFFEF3C7',
          textColor: 'FF92400E'
        },
        {
          icon: '🟠',
          level: isRTL ? 'سري جداً' : 'SECRET',
          count: secretRecords,
          percentage: totalRecords > 0 ? ((secretRecords / totalRecords) * 100).toFixed(1) : '0',
          description: isRTL ? 'بيانات عالية الحساسية • حماية متقدمة مطلوبة' : 'Highly sensitive data • Advanced protection required',
          color: 'FFEA580C', // Orange
          bgColor: 'FFFED7AA',
          textColor: 'FFC2410C'
        },
        {
          icon: '🔴',
          level: isRTL ? 'سري للغاية' : 'TOP SECRET',
          count: topSecretRecords,
          percentage: totalRecords > 0 ? ((topSecretRecords / totalRecords) * 100).toFixed(1) : '0',
          description: isRTL ? 'بيانات فائقة الحساسية • أقصى درجات الحماية' : 'Ultra-sensitive data • Maximum protection level',
          color: 'FFDC2626', // Red
          bgColor: 'FFFECACA',
          textColor: 'FF991B1B'
        }
      ];

      // Create visually appealing classification cards
      classificationLevels.forEach((classification, index) => {
        const cardRow = summaryRow + (index * 4); // 4 rows per card with spacing

        // Card header with level name and icon
        summarySheet.mergeCells(`B${cardRow}:I${cardRow}`);
        const headerCell = summarySheet.getCell(`B${cardRow}`);
        headerCell.value = `${classification.icon} ${classification.level}`;
        headerCell.font = {
          name: 'Arial Black',
          size: 16,
          bold: true,
          color: { argb: thiqahWhite }
        };
        headerCell.alignment = { horizontal: 'center', vertical: 'middle' };
        headerCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: classification.color } };
        headerCell.border = {
          top: { style: 'thick', color: { argb: classification.color } },
          left: { style: 'thick', color: { argb: classification.color } },
          bottom: { style: 'thin', color: { argb: classification.color } },
          right: { style: 'thick', color: { argb: classification.color } }
        };

        // Statistics row (count and percentage)
        summarySheet.mergeCells(`B${cardRow + 1}:I${cardRow + 1}`);
        const statsCell = summarySheet.getCell(`B${cardRow + 1}`);
        statsCell.value = `${classification.count.toLocaleString()} Records (${classification.percentage}%)`;
        statsCell.font = {
          name: 'Arial Black',
          size: 20,
          bold: true,
          color: { argb: classification.textColor }
        };
        statsCell.alignment = { horizontal: 'center', vertical: 'middle' };
        statsCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } };
        statsCell.border = {
          top: { style: 'thin', color: { argb: classification.color } },
          left: { style: 'thick', color: { argb: classification.color } },
          bottom: { style: 'thin', color: { argb: classification.color } },
          right: { style: 'thick', color: { argb: classification.color } }
        };

        // Description row
        summarySheet.mergeCells(`B${cardRow + 2}:I${cardRow + 2}`);
        const descCell = summarySheet.getCell(`B${cardRow + 2}`);
        descCell.value = classification.description;
        descCell.font = {
          name: 'Arial',
          size: 12,
          bold: true,
          color: { argb: classification.textColor }
        };
        descCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        descCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: classification.bgColor } };
        descCell.border = {
          top: { style: 'thin', color: { argb: classification.color } },
          left: { style: 'thick', color: { argb: classification.color } },
          bottom: { style: 'thick', color: { argb: classification.color } },
          right: { style: 'thick', color: { argb: classification.color } }
        };
      });

      summaryRow += 17; // Move past classification cards (4 cards × 4 rows + spacing)

      // ===== PERSONAL DATA ANALYSIS BY CONFIDENTIALITY LEVEL =====
      // Add spacing
      summaryRow += 2;

      // Section header
      summarySheet.mergeCells(`B${summaryRow}:I${summaryRow + 1}`);
      const personalDataHeaderCell = summarySheet.getCell(`B${summaryRow}`);
      personalDataHeaderCell.value = isRTL ? '👤 تحليل البيانات الشخصية حسب مستوى السرية' : '👤 PERSONAL DATA ANALYSIS BY CONFIDENTIALITY LEVEL';
      personalDataHeaderCell.font = {
        name: 'Arial Black',
        size: 18,
        bold: true,
        color: { argb: thiqahWhite }
      };
      personalDataHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };
      personalDataHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF8B5CF6' } };
      personalDataHeaderCell.border = {
        top: { style: 'thick', color: { argb: 'FFA855F7' } },
        left: { style: 'thick', color: { argb: 'FFA855F7' } },
        bottom: { style: 'thick', color: { argb: 'FFA855F7' } },
        right: { style: 'thick', color: { argb: 'FFA855F7' } }
      };
      summaryRow += 3; // Move to content

      // Calculate personal data by confidentiality level
      const personalDataByLevel = [
        {
          level: isRTL ? 'عام' : 'PUBLIC',
          icon: '🟢',
          personalDataCount: filteredData.filter(row => row.confidentialityLevel === 'Public' && row.hasPersonalData === true).length,
          totalCount: publicRecords,
          color: 'FF10B981',
          bgColor: 'FFDCFCE7',
          textColor: 'FF065F46'
        },
        {
          level: isRTL ? 'سري' : 'CONFIDENTIAL',
          icon: '🟡',
          personalDataCount: filteredData.filter(row => row.confidentialityLevel === 'Confidential' && row.hasPersonalData === true).length,
          totalCount: confidentialRecords,
          color: 'FFF59E0B',
          bgColor: 'FFFEF3C7',
          textColor: 'FF92400E'
        },
        {
          level: isRTL ? 'سري جداً' : 'SECRET',
          icon: '🟠',
          personalDataCount: filteredData.filter(row => row.confidentialityLevel === 'Secret' && row.hasPersonalData === true).length,
          totalCount: secretRecords,
          color: 'FFEA580C',
          bgColor: 'FFFED7AA',
          textColor: 'FFC2410C'
        },
        {
          level: isRTL ? 'سري للغاية' : 'TOP SECRET',
          icon: '🔴',
          personalDataCount: filteredData.filter(row => row.confidentialityLevel === 'Top Secret' && row.hasPersonalData === true).length,
          totalCount: topSecretRecords,
          color: 'FFDC2626',
          bgColor: 'FFFECACA',
          textColor: 'FF991B1B'
        }
      ];

      // Create personal data analysis cards
      personalDataByLevel.forEach((level, index) => {
        const cardRow = summaryRow + (index * 4); // 4 rows per card with spacing
        const personalDataPercentage = level.totalCount > 0 ? ((level.personalDataCount / level.totalCount) * 100).toFixed(1) : '0';

        // Level header
        summarySheet.mergeCells(`B${cardRow}:I${cardRow}`);
        const levelHeaderCell = summarySheet.getCell(`B${cardRow}`);
        levelHeaderCell.value = `${level.icon} ${level.level}`;
        levelHeaderCell.font = {
          name: 'Arial Black',
          size: 16,
          bold: true,
          color: { argb: thiqahWhite }
        };
        levelHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };
        levelHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: level.color } };
        levelHeaderCell.border = {
          top: { style: 'thick', color: { argb: level.color } },
          left: { style: 'thick', color: { argb: level.color } },
          bottom: { style: 'thin', color: { argb: level.color } },
          right: { style: 'thick', color: { argb: level.color } }
        };

        // Personal data statistics
        summarySheet.mergeCells(`B${cardRow + 1}:I${cardRow + 1}`);
        const statsCell = summarySheet.getCell(`B${cardRow + 1}`);
        statsCell.value = isRTL ?
          `${level.personalDataCount.toLocaleString()} بيانات شخصية من أصل ${level.totalCount.toLocaleString()} سجل (${personalDataPercentage}%)` :
          `${level.personalDataCount.toLocaleString()} Personal Data out of ${level.totalCount.toLocaleString()} Records (${personalDataPercentage}%)`;
        statsCell.font = {
          name: 'Arial Black',
          size: 14,
          bold: true,
          color: { argb: level.textColor }
        };
        statsCell.alignment = { horizontal: 'center', vertical: 'middle' };
        statsCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } };
        statsCell.border = {
          top: { style: 'thin', color: { argb: level.color } },
          left: { style: 'thick', color: { argb: level.color } },
          bottom: { style: 'thin', color: { argb: level.color } },
          right: { style: 'thick', color: { argb: level.color } }
        };

        // Analysis note
        summarySheet.mergeCells(`B${cardRow + 2}:I${cardRow + 2}`);
        const analysisCell = summarySheet.getCell(`B${cardRow + 2}`);
        const analysisText = level.personalDataCount > 0 ?
          (isRTL ? `⚠️ يحتوي على بيانات شخصية تتطلب حماية خاصة` : `⚠️ Contains personal data requiring special protection`) :
          (isRTL ? `✅ لا يحتوي على بيانات شخصية` : `✅ No personal data identified`);
        analysisCell.value = analysisText;
        analysisCell.font = {
          name: 'Arial',
          size: 12,
          bold: true,
          color: { argb: level.personalDataCount > 0 ? 'FFDC2626' : 'FF059669' }
        };
        analysisCell.alignment = { horizontal: 'center', vertical: 'middle' };
        analysisCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: level.bgColor } };
        analysisCell.border = {
          top: { style: 'thin', color: { argb: level.color } },
          left: { style: 'thick', color: { argb: level.color } },
          bottom: { style: 'thick', color: { argb: level.color } },
          right: { style: 'thick', color: { argb: level.color } }
        };
      });

      summaryRow += 17; // Move past personal data analysis (4 cards × 4 rows + spacing)

      // Set optimized column widths for redesigned summary sheet
      summarySheet.columns = [
        { width: 3 },   // A - Left margin
        { width: 18 },  // B - Content start
        { width: 18 },  // C - Metric cards
        { width: 3 },   // D - Spacing
        { width: 18 },  // E - Metric cards
        { width: 18 },  // F - Metric cards
        { width: 3 },   // G - Spacing
        { width: 18 },  // H - Metric cards
        { width: 18 },  // I - Content end
        { width: 3 }    // J - Right margin
      ];

      // Set row heights for redesigned summary sheet
      for (let i = 1; i <= summaryRow; i++) {
        const currentRow = summarySheet.getRow(i);
        if (i === 1) {
          currentRow.height = 12; // Brand bar
        } else if (i === 3) {
          currentRow.height = 45; // Main title
        } else if (i === 7) {
          currentRow.height = 35; // Subtitle
        } else if (i === 10) {
          currentRow.height = 40; // KPI section header
        } else if (i >= 13 && i <= 15) {
          currentRow.height = 30; // Metric cards
        } else if ([20, 42].includes(i)) {
          currentRow.height = 40; // Section headers (Classification & Personal Data)
        } else if (i >= 23 && i <= 39 && (i - 23) % 4 === 0) {
          currentRow.height = 35; // Classification level headers
        } else if (i >= 24 && i <= 40 && (i - 24) % 4 === 0) {
          currentRow.height = 40; // Classification statistics
        } else if (i >= 25 && i <= 41 && (i - 25) % 4 === 0) {
          currentRow.height = 25; // Classification descriptions
        } else if (i >= 45 && i <= 61 && (i - 45) % 4 === 0) {
          currentRow.height = 35; // Personal data level headers
        } else if (i >= 46 && i <= 62 && (i - 46) % 4 === 0) {
          currentRow.height = 30; // Personal data statistics
        } else if (i >= 47 && i <= 63 && (i - 47) % 4 === 0) {
          currentRow.height = 25; // Personal data analysis
        } else {
          currentRow.height = 25; // Regular content with good spacing
        }
      }

      console.log('Summary page completed successfully with', summaryRow, 'rows');

      // ===== DATA TABLE SHEET =====
      const dataSheet = workbook.addWorksheet(isRTL ? 'بيانات النظام' : 'System Data');

      // Data sheet title
      dataSheet.mergeCells('A1:S2');
      const dataSheetTitleCell = dataSheet.getCell('A1');
      dataSheetTitleCell.value = isRTL ? 'بيانات النظام التفصيلية' : 'Detailed System Data';
      dataSheetTitleCell.font = { name: 'Arial', size: 16, bold: true, color: { argb: thiqahWhite } };
      dataSheetTitleCell.alignment = { horizontal: 'center', vertical: 'middle' };
      dataSheetTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahBlue } };

      // Column headers
      const headers = isRTL ? [
        'رقم الصف', 'اسم المخطط', 'اسم الجدول', 'اسم العمود', 'نوع البيانات',
        'الحد الأقصى', 'يقبل القيم الفارغة', 'ترتيب العمود', 'آخر بحث', 'آخر فحص',
        'آخر استعلام', 'آخر تحديث', 'مستوى السرية', 'سبب السرية', 'بيانات شخصية',
        'سبب البيانات الشخصية', 'حالة التصنيف', 'إرسال للعميل', 'ترتيب الاستيراد', 'تاريخ الإنشاء'
      ] : [
        'Row #', 'Schema Name', 'Table Name', 'Column Name', 'Data Type',
        'Max Length', 'Nullable', 'Column Order', 'Last Seek', 'Last Scan',
        'Last Lookup', 'Last Update', 'Confidentiality Level', 'Confidentiality Reasoning', 'Has Personal Data',
        'Personal Data Reasoning', 'Classification Status', 'Push to Client', 'Import Order', 'Created At'
      ];

      // Add headers to row 4
      headers.forEach((header, index) => {
        const cell = dataSheet.getCell(4, index + 1);
        cell.value = header;
        cell.font = { name: 'Arial', size: 11, bold: true, color: { argb: thiqahWhite } };
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahDarkGray } };
        cell.border = {
          top: { style: 'thin', color: { argb: thiqahWhite } },
          left: { style: 'thin', color: { argb: thiqahWhite } },
          bottom: { style: 'thin', color: { argb: thiqahWhite } },
          right: { style: 'thin', color: { argb: thiqahWhite } }
        };
      });

      // Add data rows
      filteredData.forEach((row, rowIndex) => {
        const excelRow = rowIndex + 5; // Start from row 5

        // Prepare row data
        const rowData = [
          rowIndex + 1, // Row #
          row.schemaName || '', // Schema Name
          row.tableName || '', // Table Name
          row.columnName || '', // Column Name
          row.dataType || '', // Data Type
          row.maxLength || '', // Max Length
          row.isNullable !== null ? (row.isNullable ? (isRTL ? 'نعم' : 'Yes') : (isRTL ? 'لا' : 'No')) : '', // Nullable
          row.columnOrder || '', // Column Order
          row.lastSeek || '', // Last Seek
          row.lastScan || '', // Last Scan
          row.lastLookup || '', // Last Lookup
          row.lastUpdate || '', // Last Update
          row.confidentialityLevel ? (isRTL ? (
            row.confidentialityLevel === 'Public' ? 'عام' :
            row.confidentialityLevel === 'Confidential' ? 'سري' :
            row.confidentialityLevel === 'Secret' ? 'سري جداً' : 'سري للغاية'
          ) : row.confidentialityLevel) : '', // Confidentiality Level
          row.confidentialityReasoning || '', // Confidentiality Reasoning
          row.hasPersonalData !== undefined ? (row.hasPersonalData ? (isRTL ? 'نعم' : 'Yes') : (isRTL ? 'لا' : 'No')) : '', // Has Personal Data
          row.personalDataReasoning || '', // Personal Data Reasoning
          row.classificationStatus ? (isRTL ? (
            row.classificationStatus === 'pending' ? 'في الانتظار' :
            row.classificationStatus === 'table_classified' ? 'مصنف جزئياً' : 'مصنف بالكامل'
          ) : row.classificationStatus) : '', // Classification Status
          row.pushToClient ? (row.pushToClient === "Yes" ? (isRTL ? "نعم" : "Yes") : (isRTL ? "لا" : "No")) : (isRTL ? "غير محدد" : "Not Set"), // Push to Client
          row.importOrder || '', // Import Order
          row.createdAt ? new Date(row.createdAt.toMillis()).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US') : '' // Created At
        ];

        // Add data to cells with formatting
        rowData.forEach((cellValue, colIndex) => {
          const cell = dataSheet.getCell(excelRow, colIndex + 1);

          // Sanitize cell value
          const sanitizedValue = cellValue === null || cellValue === undefined ? '' : String(cellValue);
          cell.value = sanitizedValue;

          cell.font = {
            name: 'Arial',
            size: 10,
            color: { argb: darkText },
            bold: false
          };
          cell.alignment = { horizontal: 'left', vertical: 'top', wrapText: true };
          cell.border = {
            top: { style: 'thin', color: { argb: 'FFE5E7EB' } },
            left: { style: 'thin', color: { argb: 'FFE5E7EB' } },
            bottom: { style: 'thin', color: { argb: 'FFE5E7EB' } },
            right: { style: 'thin', color: { argb: 'FFE5E7EB' } }
          };

          // Color code confidentiality levels
          if (colIndex === 12 && row.confidentialityLevel) { // Confidentiality Level column
            switch (row.confidentialityLevel) {
              case 'Public':
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: greenBg } };
                break;
              case 'Confidential':
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: yellowBg } };
                break;
              case 'Secret':
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: orangeBg } };
                break;
              case 'Top Secret':
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: redBg } };
                break;
            }
          }

          // Color code personal data
          if (colIndex === 14 && row.hasPersonalData !== undefined) { // Personal Data column
            if (row.hasPersonalData) {
              cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: redBg } };
            } else {
              cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: greenBg } };
            }
          }
        });
      });

      // Set column widths for data sheet
      dataSheet.columns = [
        { width: 8 },   // Row #
        { width: 15 },  // Schema Name
        { width: 20 },  // Table Name
        { width: 25 },  // Column Name
        { width: 15 },  // Data Type
        { width: 12 },  // Max Length
        { width: 10 },  // Nullable
        { width: 12 },  // Column Order
        { width: 15 },  // Last Seek
        { width: 15 },  // Last Scan
        { width: 15 },  // Last Lookup
        { width: 15 },  // Last Update
        { width: 18 },  // Confidentiality Level
        { width: 35 },  // Confidentiality Reasoning
        { width: 18 },  // Has Personal Data
        { width: 35 },  // Personal Data Reasoning
        { width: 18 },  // Classification Status
        { width: 15 },  // Push to Client
        { width: 12 },  // Import Order
        { width: 15 }   // Created At
      ];

      // Generate filename and export
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = isRTL ? `تصدير_بيانات_النظام_${timestamp}.xlsx` : `system_data_export_${timestamp}.xlsx`;

      // Export file using ExcelJS with enhanced error handling
      try {
        const buffer = await workbook.xlsx.writeBuffer();

        // Validate buffer
        if (!buffer || buffer.byteLength === 0) {
          throw new Error('Generated Excel file is empty');
        }

        const blob = new Blob([buffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';

        // Add to DOM, click, and remove
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up
        setTimeout(() => window.URL.revokeObjectURL(url), 100);

      } catch (exportError) {
        console.error('Excel file generation error:', exportError);
        throw new Error(`Failed to generate Excel file: ${exportError instanceof Error ? exportError.message : 'Unknown error'}`);
      }

      toast({
        title: isRTL ? "تم تصدير البيانات" : "Data Exported",
        description: isRTL ?
          `تم تصدير ${filteredData.length} سجل إلى ملف Excel احترافي` :
          `Exported ${filteredData.length} records to professional Excel file`,
      });

    } catch (error) {
      console.error('Excel export error:', error);
      toast({
        title: isRTL ? "خطأ في التصدير" : "Export Error",
        description: error instanceof Error ? error.message : "An error occurred during export",
        variant: "destructive"
      });
    }
  };

  // Handle page navigation
  const handlePageNavigation = (pageNumber: string) => {
    const page = parseInt(pageNumber);
    if (!isNaN(page) && page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      setPageInput("");
      toast({
        title: isRTL ? "تم الانتقال للصفحة" : "Navigated to Page",
        description: isRTL ? `الصفحة ${page}` : `Page ${page}`,
      });
    } else {
      toast({
        title: isRTL ? "رقم صفحة غير صحيح" : "Invalid Page Number",
        description: isRTL ?
          `يرجى إدخال رقم بين 1 و ${totalPages}` :
          `Please enter a number between 1 and ${totalPages}`,
        variant: "destructive"
      });
    }
  };

  // Handle Enter key press in page input
  const handlePageInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handlePageNavigation(pageInput);
    }
  };

  // Save edited data
  const handleSaveEdit = async () => {
    if (!editingRow || !editingData || Object.keys(editingData).length === 0) {
      setIsEditModalOpen(false);
      setEditingRow(null);
      setEditingData({});
      return;
    }

    setIsSaving(true);
    try {
      // Check for changes in specification levels and track them
      const updateData = { ...editingData };
      let hasSpecificationChanges = false;

      // Track confidentiality level changes
      if (editingData.confidentialityLevel !== editingRow.confidentialityLevel) {
        updateData.confidentialityLevelChanged = true;
        updateData.confidentialityLevelOldValue = editingRow.confidentialityLevel || '';
        updateData.confidentialityLevelNewValue = editingData.confidentialityLevel || '';
        hasSpecificationChanges = true;
      }

      // Track personal data changes
      if (editingData.hasPersonalData !== editingRow.hasPersonalData) {
        updateData.hasPersonalDataChanged = true;
        updateData.hasPersonalDataOldValue = editingRow.hasPersonalData;
        updateData.hasPersonalDataNewValue = editingData.hasPersonalData;
        hasSpecificationChanges = true;
      }

      // Auto-flag as needs review if specification levels changed (unless manually set)
      if (hasSpecificationChanges && editingData.needsReview === undefined) {
        updateData.needsReview = true;
      }

      await SystemsService.updateSystemDataBatch(systemId, [{
        documentId: editingRow.id || '',
        data: updateData
      }]);

      toast({
        title: isRTL ? "تم الحفظ" : "Saved",
        description: isRTL ? "تم حفظ التغييرات بنجاح" : "Changes saved successfully",
      });

      // Refresh data
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Save error:', error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: error instanceof Error ? error.message : "Failed to save changes",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
      setIsEditModalOpen(false);
      setEditingRow(null);
      setEditingData({});
    }
  };

  // Mark as reviewed
  const handleMarkAsReviewed = async (row: ExtendedSystemData) => {
    setIsSaving(true);
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('User not authenticated');
      }

      const userProfile = await getUserProfile(user.uid);
      const reviewData = {
        isReviewed: true,
        reviewedBy: userProfile?.displayName || user.email || 'Unknown',
        reviewedAt: new Date().toISOString()
      };

      await SystemsService.updateSystemDataBatch(systemId, [{
        documentId: row.id || '',
        data: reviewData
      }]);

      toast({
        title: isRTL ? "تم التمييز كمراجع" : "Marked as Reviewed",
        description: isRTL ? "تم تمييز العنصر كمراجع بنجاح" : "Item has been marked as reviewed successfully",
      });

      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Error marking as reviewed:', error);
      toast({
        title: isRTL ? "خطأ في المراجعة" : "Review Error",
        description: error instanceof Error ? error.message : "Failed to mark as reviewed",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Remove review mark
  const handleRemoveReview = async (row: ExtendedSystemData) => {
    setIsSaving(true);
    try {
      const reviewData = {
        isReviewed: false,
        reviewedBy: undefined,
        reviewedAt: undefined
      };

      await SystemsService.updateSystemDataBatch(systemId, [{
        documentId: row.id || '',
        data: reviewData
      }]);

      toast({
        title: isRTL ? "تم إزالة المراجعة" : "Review Removed",
        description: isRTL ? "تم إزالة تمييز المراجعة بنجاح" : "Review mark has been removed successfully",
      });

      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Error removing review:', error);
      toast({
        title: isRTL ? "خطأ في إزالة المراجعة" : "Remove Review Error",
        description: error instanceof Error ? error.message : "Failed to remove review",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Start editing
  const handleStartEdit = (row: ExtendedSystemData) => {
    // Check if user is consultant
    if (!isConsultant) {
      toast({
        title: isRTL ? "غير مصرح" : "Unauthorized",
        description: isRTL ? "التحرير متاح للمستشارين فقط" : "Editing is only available for consultants",
        variant: "destructive"
      });
      return;
    }

    setEditingRow(row);
    setEditingData({
      confidentialityLevel: row.confidentialityLevel,
      confidentialityReasoning: row.confidentialityReasoning,
      hasPersonalData: row.hasPersonalData,
      personalDataReasoning: row.personalDataReasoning
    });
    setIsEditModalOpen(true);
  };





  // Toggle row selection
  const handleRowSelection = (rowId: string) => {
    const newSelection = new Set(selectedRows);
    if (newSelection.has(rowId)) {
      newSelection.delete(rowId);
    } else {
      newSelection.add(rowId);
    }
    setSelectedRows(newSelection);
  };

  // Toggle all rows selection (current page)
  const handleSelectAll = () => {
    if (selectedRows.size === currentData.length) {
      setSelectedRows(new Set());
    } else {
      const allRowIds = new Set(currentData.map(row => row.id || '').filter(id => id));
      setSelectedRows(allRowIds);
    }
  };

  // Select all system records
  const handleSelectAllSystem = () => {
    if (selectedRows.size === data.length) {
      setSelectedRows(new Set());
    } else {
      // Get all valid IDs from the data
      const validIds = data
        .filter(row => row.id && row.id.trim() !== '') // Only include rows with valid IDs
        .map(row => row.id!); // Use non-null assertion since we filtered above

      const allSystemRowIds = new Set(validIds);

      console.log('Selecting all system records:', {
        totalRecords: data.length,
        recordsWithValidIds: validIds.length,
        validIds: validIds,
        recordsWithoutIds: data.filter(row => !row.id || row.id.trim() === '').length
      });

      if (validIds.length === 0) {
        toast({
          title: isRTL ? "خطأ" : "Error",
          description: isRTL ? "لا توجد سجلات صالحة للتحديد" : "No valid records to select",
          variant: "destructive"
        });
        return;
      }

      setSelectedRows(allSystemRowIds);

      toast({
        title: isRTL ? "تم التحديد" : "Selected",
        description: isRTL ?
          `تم تحديد ${validIds.length} سجل` :
          `Selected ${validIds.length} records`,
      });
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <div className="p-8 text-center">
          <div className="space-y-4">
            <div className="w-32 h-32 bg-gray-200 rounded-3xl mx-auto"></div>
            <div className="h-6 bg-gray-200 rounded w-48 mx-auto"></div>
            <div className="h-4 bg-gray-200 rounded w-32 mx-auto"></div>
          </div>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <div className="p-12 text-center">
          <div className="w-32 h-32 bg-[var(--brand-blue)]/10 rounded-3xl flex items-center justify-center mx-auto mb-8">
            <FileSpreadsheet className="w-16 h-16 text-[var(--brand-blue)]" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            {isRTL ? "لا توجد بيانات" : "No Data Available"}
          </h3>
          <p className="text-gray-600 mb-8 max-w-md mx-auto">
            {isRTL 
              ? "لم يتم استيراد أي بيانات بعد. قم بتحميل ملف Excel لبدء إدارة بيانات النظام."
              : "No data has been imported yet. Upload an Excel file to start managing system data."
            }
          </p>
          <Button
            onClick={onImportClick}
            className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            {isRTL ? "استيراد البيانات" : "Import Data"}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
              <FileSpreadsheet className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-white">
                {isRTL ? "بيانات النظام" : "System Data"}
              </h3>
              <p className="text-white/80">
                {isRTL ? `${data.length} سجل` : `${data.length} records`}
              </p>
            </div>
          </div>


          <div className="flex gap-3">
            {/* Comprehensive Actions Menu */}
            <div className="relative">
              {/* Main Actions Button */}
              <div className="relative">
                <Button
                  onClick={() => {
                    setIsClassificationDropdownOpen(!isClassificationDropdownOpen);
                  }}
                  className="bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 disabled:opacity-50 transition-all duration-200 font-medium"
                >
                    <Settings className="w-4 h-4 mr-2" />
                    <span>{isRTL ? "الإجراءات" : "Actions"}</span>
                    <ChevronDown className={`w-4 h-4 ml-2 transition-transform duration-200 ${isClassificationDropdownOpen ? 'rotate-180' : ''}`} />
                  </Button>

                  {/* Professional Actions Dropdown */}
                  {isClassificationDropdownOpen && (
                    <div className="absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-50 overflow-hidden">
                      {/* Header */}
                      <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
                        <h3 className="text-sm font-semibold text-gray-900">
                          {isRTL ? "إجراءات إدارة البيانات" : "Data Management Actions"}
                        </h3>
                        <p className="text-xs text-gray-500 mt-1">
                          {isRTL ? "اختر الإجراء المطلوب" : "Select the desired action"}
                        </p>
                      </div>

                      <div className="py-2">


                        {/* Data Management Section */}
                        <div className="px-4 py-2">
                          <h4 className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                            {isRTL ? "إدارة البيانات" : "Data Management"}
                          </h4>
                        </div>

                        {/* Export Excel */}
                        <button
                          onClick={() => {
                            handleExcelExport();
                            setIsClassificationDropdownOpen(false);
                          }}
                          disabled={filteredData.length === 0}
                          className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <div className="w-8 h-8 bg-green-50 rounded-md flex items-center justify-center">
                            <Download className="w-4 h-4 text-green-600" />
                          </div>
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900">
                              {isRTL ? "تصدير إلى Excel" : "Export to Excel"}
                            </div>
                            <div className="text-xs text-gray-500">
                              {isRTL ? "تصدير البيانات المفلترة" : "Export filtered data"}
                            </div>
                          </div>
                        </button>

                        {/* Import Data */}
                        <button
                          onClick={() => {
                            onImportClick();
                            setIsClassificationDropdownOpen(false);
                          }}
                          className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150"
                        >
                          <div className="w-8 h-8 bg-blue-50 rounded-md flex items-center justify-center">
                            <Upload className="w-4 h-4 text-blue-600" />
                          </div>
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900">
                              {isRTL ? "استيراد البيانات" : "Import Data"}
                            </div>
                            <div className="text-xs text-gray-500">
                              {isRTL ? "إضافة بيانات جديدة" : "Add new data records"}
                            </div>
                          </div>
                        </button>

                        {/* Delete All */}
                        {onDeleteAll && data.length > 0 && (
                          <>
                            <div className="border-t border-gray-100 my-2"></div>
                            <div className="px-4 py-2">
                              <h4 className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                                {isRTL ? "إجراءات خطيرة" : "Danger Zone"}
                              </h4>
                            </div>
                            <button
                              onClick={() => {
                                onDeleteAll();
                                setIsClassificationDropdownOpen(false);
                              }}
                              className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-red-50 transition-colors duration-150"
                            >
                              <div className="w-8 h-8 bg-red-50 rounded-md flex items-center justify-center">
                                <Trash2 className="w-4 h-4 text-red-600" />
                              </div>
                              <div className="flex-1">
                                <div className="text-sm font-medium text-red-900">
                                  {isRTL ? "حذف جميع البيانات" : "Delete All Data"}
                                </div>
                                <div className="text-xs text-red-500">
                                  {isRTL ? "إجراء لا يمكن التراجع عنه" : "This action cannot be undone"}
                                </div>
                              </div>
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </div>

              {/* Click outside to close dropdown */}
              {isClassificationDropdownOpen && (
                <div
                  className="fixed inset-0 z-40"
                  onClick={() => setIsClassificationDropdownOpen(false)}
                />
              )}
            </div>
          </div>
        </div>

        {/* View Mode Toggle */}
        <div className="flex justify-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-1 border border-white/20">
            <div className="flex gap-1">
              <button
                onClick={() => handleViewModeChange('classification')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
                  viewMode === 'classification'
                    ? 'bg-white text-[var(--brand-blue)] shadow-md'
                    : 'text-white/80 hover:text-white hover:bg-white/10'
                }`}
              >
                <Eye className="w-4 h-4" />
                <span className="font-medium">
                  {isRTL ? "عرض التصنيف" : "Classification View"}
                </span>
                <span className="text-xs opacity-75">
                  ({isRTL ? "50 صف" : "50 rows"})
                </span>
              </button>
              {/* Editing View - Only for Consultants */}
              {isConsultant && (
                <button
                  onClick={() => handleViewModeChange('editing')}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
                    viewMode === 'editing'
                      ? 'bg-white text-[var(--brand-blue)] shadow-md'
                      : 'text-white/80 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <Edit className="w-4 h-4" />
                  <span className="font-medium">
                    {isRTL ? "عرض التحرير" : "Editing View"}
                  </span>
                  <span className="text-xs opacity-75">
                    ({isRTL ? "50 صف" : "50 rows"})
                  </span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Search and Actions */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center gap-4 mb-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={isRTL ? "البحث في البيانات..." : "Search data..."}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1);
              }}
            />
          </div>
          
          {/* Bulk Actions - Only for Consultants */}
          {isConsultant && (
            <SystemDataTableActions
              selectedRows={selectedRows}
              systemId={systemId}
              isRTL={isRTL}
              onDataUpdate={onDataUpdate}
              onClearSelection={() => setSelectedRows(new Set())}
              onSelectAllSystem={handleSelectAllSystem}
              totalSystemRecords={data.length}
            />
          )}

          <div className="text-sm text-gray-600">
            {isRTL
              ? `عرض ${startIndex + 1}-${Math.min(endIndex, filteredData.length)} من ${filteredData.length}`
              : `Showing ${startIndex + 1}-${Math.min(endIndex, filteredData.length)} of ${filteredData.length}`
            }
          </div>
        </div>

        {/* Advanced Filters - Only in editing view */}
        {viewMode === 'editing' && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center gap-2 mb-3">
              <Filter className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                {isRTL ? "المرشحات:" : "Filters:"}
              </span>
            </div>

            {/* Filters Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 mb-3">
              {/* Table Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  {isRTL ? "الجدول" : "Table"}
                </label>
                <select
                  value={tableFilter}
                  onChange={(e) => {
                    setTableFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full px-2 py-1.5 text-xs border border-gray-200 rounded focus:ring-1 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
                >
                  <option value="">{isRTL ? "الكل" : "All"}</option>
                  {uniqueTables.map(table => (
                    <option key={table} value={table}>{table}</option>
                  ))}
                </select>
              </div>

              {/* Data Type Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  {isRTL ? "نوع البيانات" : "Data Type"}
                </label>
                <select
                  value={dataTypeFilter}
                  onChange={(e) => {
                    setDataTypeFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full px-2 py-1.5 text-xs border border-gray-200 rounded focus:ring-1 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
                >
                  <option value="">{isRTL ? "الكل" : "All"}</option>
                  {uniqueDataTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              {/* Confidentiality Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  {isRTL ? "السرية" : "Confidentiality"}
                </label>
                <select
                  value={confidentialityFilter}
                  onChange={(e) => {
                    setConfidentialityFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full px-2 py-1.5 text-xs border border-gray-200 rounded focus:ring-1 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
                >
                  <option value="">{isRTL ? "الكل" : "All"}</option>
                  {uniqueConfidentialityLevels.map(level => (
                    <option key={level} value={level}>
                      {isRTL ? (
                        level === 'Public' ? 'عام' :
                        level === 'Confidential' ? 'سري' :
                        level === 'Secret' ? 'سري جداً' : 'سري للغاية'
                      ) : level}
                    </option>
                  ))}
                </select>
              </div>

              {/* Personal Data Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  {isRTL ? "بيانات شخصية" : "Personal Data"}
                </label>
                <select
                  value={personalDataFilter}
                  onChange={(e) => {
                    setPersonalDataFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full px-2 py-1.5 text-xs border border-gray-200 rounded focus:ring-1 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
                >
                  <option value="">{isRTL ? "الكل" : "All"}</option>
                  <option value="yes">{isRTL ? "نعم" : "Yes"}</option>
                  <option value="no">{isRTL ? "لا" : "No"}</option>
                  <option value="undefined">{isRTL ? "غير محدد" : "Not Set"}</option>
                </select>
              </div>

              {/* Push to Client Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  {isRTL ? "إرسال للعميل" : "Push to Client"}
                </label>
                <select
                  value={pushToClientFilter}
                  onChange={(e) => {
                    setPushToClientFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full px-2 py-1.5 text-xs border border-gray-200 rounded focus:ring-1 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
                >
                  <option value="">{isRTL ? "الكل" : "All"}</option>
                  <option value="yes">{isRTL ? "نعم" : "Yes"}</option>
                  <option value="no">{isRTL ? "لا" : "No"}</option>
                  <option value="not_set">{isRTL ? "غير محدد" : "Not Set"}</option>
                </select>
              </div>

              {/* Review Status Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  {isRTL ? "حالة المراجعة" : "Review Status"}
                </label>
                <select
                  value={reviewStatusFilter}
                  onChange={(e) => {
                    setReviewStatusFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full px-2 py-1.5 text-xs border border-gray-200 rounded focus:ring-1 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
                >
                  <option value="">{isRTL ? "الكل" : "All"}</option>
                  <option value="reviewed">{isRTL ? "مراجع" : "Reviewed"}</option>
                  <option value="unreviewed">{isRTL ? "غير مراجع" : "Not Reviewed"}</option>
                </select>
              </div>

              {/* Needs Review Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  {isRTL ? "يحتاج مراجعة" : "Needs Review"}
                </label>
                <select
                  value={needsReviewFilter}
                  onChange={(e) => {
                    setNeedsReviewFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full px-2 py-1.5 text-xs border border-gray-200 rounded focus:ring-1 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
                >
                  <option value="">{isRTL ? "الكل" : "All"}</option>
                  <option value="yes">{isRTL ? "نعم" : "Yes"}</option>
                  <option value="no">{isRTL ? "لا" : "No"}</option>
                </select>
              </div>

              {/* Reviewer Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  {isRTL ? "المراجع" : "Reviewer"}
                </label>
                <select
                  value={reviewerFilter}
                  onChange={(e) => {
                    setReviewerFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full px-2 py-1.5 text-xs border border-gray-200 rounded focus:ring-1 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
                >
                  <option value="">{isRTL ? "الكل" : "All"}</option>
                  {uniqueReviewers.map(reviewer => (
                    <option key={reviewer} value={reviewer}>{reviewer}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Clear Filters Button */}
            <div className="flex justify-end">
              <Button
                onClick={() => {
                  setTableFilter("");
                  setDataTypeFilter("");
                  setConfidentialityFilter("");
                  setReviewStatusFilter("");
                  setPersonalDataFilter("");
                  setReviewerFilter("");
                  setNeedsReviewFilter("");
                  setPushToClientFilter("");
                  setCurrentPage(1);
                }}
                variant="outline"
                size="sm"
                className="text-xs px-3 py-1.5 h-auto"
              >
                <X className="w-3 h-3 mr-1" />
                {isRTL ? "مسح المرشحات" : "Clear Filters"}
              </Button>
            </div>
          </div>
        )}


      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full text-xs">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isConsultant ? (
                  <input
                    type="checkbox"
                    checked={selectedRows.size === currentData.length && currentData.length > 0}
                    onChange={handleSelectAll}
                    className="w-4 h-4 text-[var(--brand-blue)] border-gray-300 rounded focus:ring-[var(--brand-blue)]"
                  />
                ) : (
                  <span className="w-4 h-4 block"></span>
                )}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "اسم المخطط" : "Schema"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "اسم الجدول" : "Table"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "اسم العمود" : "Column"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "نوع البيانات" : "Data Type"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "مستوى السرية" : "Confidentiality"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "سبب السرية" : "Conf. Reason"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "بيانات شخصية" : "Personal Data"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "سبب البيانات الشخصية" : "Personal Reason"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "حالة المراجعة" : "Review Status"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "يحتاج مراجعة" : "Needs Review"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "إرسال للعميل" : "Push to Client"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "تحرير" : "Edit"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "الإجراءات" : "Actions"}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {currentData.map((row, index) => (
              <tr
                key={row.id || `row-${row.schemaName || 'no-schema'}-${row.tableName}-${row.columnName}-${index}`}
                className="hover:bg-gray-50"
              >
                <td className="px-3 py-2 text-xs">
                  {isConsultant ? (
                    <input
                      type="checkbox"
                      checked={selectedRows.has(row.id || '')}
                      onChange={() => handleRowSelection(row.id || '')}
                      className="w-4 h-4 text-[var(--brand-blue)] border-gray-300 rounded focus:ring-[var(--brand-blue)]"
                    />
                  ) : (
                    <span className="w-4 h-4 block"></span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs text-gray-600">
                  {row.schemaName || '-'}
                </td>
                <td className="px-3 py-2 text-xs font-medium text-gray-900">
                  {row.tableName}
                </td>
                <td className="px-3 py-2 text-xs text-gray-900">
                  {row.columnName}
                </td>
                <td className="px-3 py-2 text-xs">
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium bg-[var(--brand-blue)]/10 text-[var(--brand-blue)]">
                    {row.dataType}
                  </span>
                </td>
                <td className="px-3 py-2 text-xs">
                  {row.confidentialityLevel ? (
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium ${
                      row.confidentialityLevel === 'Public' ? 'bg-green-100 text-green-800' :
                      row.confidentialityLevel === 'Confidential' ? 'bg-yellow-100 text-yellow-800' :
                      row.confidentialityLevel === 'Secret' ? 'bg-orange-100 text-orange-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {isRTL ? (
                        row.confidentialityLevel === 'Public' ? 'عام' :
                        row.confidentialityLevel === 'Confidential' ? 'سري' :
                        row.confidentialityLevel === 'Secret' ? 'سري جداً' : 'سري للغاية'
                      ) : row.confidentialityLevel}
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {row.confidentialityReasoning ? (
                    <div
                      className={`text-[10px] text-gray-700 max-w-[120px] truncate ${
                        isConsultant ? 'cursor-pointer hover:text-gray-900' : ''
                      }`}
                      title={row.confidentialityReasoning}
                      onClick={isConsultant ? () => handleStartEdit(row) : undefined}
                    >
                      {row.confidentialityReasoning.length > 30
                        ? `${row.confidentialityReasoning.substring(0, 30)}...`
                        : row.confidentialityReasoning
                      }
                    </div>
                  ) : isConsultant ? (
                    <button
                      onClick={() => handleStartEdit(row)}
                      className="text-[10px] text-blue-600 hover:text-blue-800 cursor-pointer underline"
                    >
                      {isRTL ? "إضافة سبب" : "Add Reason"}
                    </button>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {row.hasPersonalData !== undefined ? (
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium ${
                      row.hasPersonalData
                        ? 'bg-red-100 text-red-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {row.hasPersonalData ? (isRTL ? "نعم" : "Yes") : (isRTL ? "لا" : "No")}
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {row.personalDataReasoning ? (
                    <div
                      className={`text-[10px] text-gray-700 max-w-[120px] truncate ${
                        isConsultant ? 'cursor-pointer hover:text-gray-900' : ''
                      }`}
                      title={row.personalDataReasoning}
                      onClick={isConsultant ? () => handleStartEdit(row) : undefined}
                    >
                      {row.personalDataReasoning.length > 30
                        ? `${row.personalDataReasoning.substring(0, 30)}...`
                        : row.personalDataReasoning
                      }
                    </div>
                  ) : isConsultant ? (
                    <button
                      onClick={() => handleStartEdit(row)}
                      className="text-[10px] text-blue-600 hover:text-blue-800 cursor-pointer underline"
                    >
                      {isRTL ? "إضافة سبب" : "Add Reason"}
                    </button>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  <div className="flex flex-col items-start gap-1">
                    {row.isReviewed ? (
                      <div className="flex items-center gap-2">
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium bg-green-100 text-green-800">
                          <CheckCircle className="w-2.5 h-2.5 mr-1" />
                          {isRTL ? "مراجع" : "Reviewed"}
                        </span>
                        <Button
                          onClick={() => handleRemoveReview(row)}
                          disabled={isSaving}
                          variant="outline"
                          size="sm"
                          className="text-[9px] px-2 py-0.5 h-5 bg-red-100 text-red-800 border-red-300 hover:bg-red-200"
                        >
                          <XCircle className="w-2.5 h-2.5 mr-1" />
                          {isRTL ? "إلغاء" : "Remove"}
                        </Button>
                      </div>
                    ) : (
                      <Button
                        onClick={() => handleMarkAsReviewed(row)}
                        disabled={isSaving}
                        variant="outline"
                        size="sm"
                        className="text-[10px] px-2 py-1 h-6 text-gray-600 hover:text-green-700 hover:bg-green-50 border-gray-300"
                      >
                        <CheckCircle className="w-3 h-3 mr-1" />
                        {isRTL ? "مراجعة" : "Review"}
                      </Button>
                    )}
                    {row.isReviewed && row.reviewedBy && (
                      <div className="text-[9px] text-gray-500 truncate max-w-[100px]" title={`${isRTL ? 'بواسطة' : 'By'}: ${row.reviewedBy}`}>
                        {isRTL ? 'بواسطة' : 'By'}: {row.reviewedBy}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-3 py-2 text-xs">
                  {row.needsReview ? (
                    <div className="flex flex-col items-start gap-1">
                      <button
                        onClick={() => {
                          setChangesViewRow(row);
                          setIsChangesModalOpen(true);
                        }}
                        className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium bg-orange-100 text-orange-800 hover:bg-orange-200 cursor-pointer transition-colors"
                      >
                        {isRTL ? "يحتاج مراجعة" : "Needs Review"}
                      </button>
                      {/* Show change indicators if any specification level changed */}
                      {(row.confidentialityLevelChanged || row.hasPersonalDataChanged) && (
                        <div className="text-[9px] text-blue-600 cursor-help"
                             title={`${isRTL ? 'تم تغيير مستوى التصنيف' : 'Specification level changed'}`}>
                          {isRTL ? "تم التغيير" : "Changed"}
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium bg-green-100 text-green-800">
                      {isRTL ? "لا يحتاج مراجعة" : "No Review Needed"}
                    </span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {row.pushToClient ? (
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium ${
                      row.pushToClient === "Yes"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-red-100 text-red-800"
                    }`}>
                      {row.pushToClient === "Yes"
                        ? (isRTL ? "نعم" : "Yes")
                        : (isRTL ? "لا" : "No")
                      }
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium bg-gray-100 text-gray-600">
                      {isRTL ? "غير محدد" : "Not Set"}
                    </span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {isConsultant ? (
                    <Button
                      onClick={() => handleStartEdit(row)}
                      variant="outline"
                      size="sm"
                      className="text-[10px] px-2 py-1 h-6"
                    >
                      <Edit className="w-3 h-3 mr-1" />
                      {isRTL ? "تحرير" : "Edit"}
                    </Button>
                  ) : (
                    <span className="text-[10px] text-gray-400 px-2 py-1">
                      {isRTL ? "عرض فقط" : "View Only"}
                    </span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {isConsultant ? (
                    <SystemDataRowActions
                      row={row}
                      systemId={systemId}
                      isRTL={isRTL}
                      onDataUpdate={onDataUpdate}
                    />
                  ) : (
                    <span className="text-[10px] text-gray-400">
                      {isRTL ? "عرض فقط" : "View Only"}
                    </span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {isRTL
                ? `صفحة ${currentPage} من ${totalPages}`
                : `Page ${currentPage} of ${totalPages}`
              }
            </div>
            <div className="flex items-center gap-3">
              {/* Page Navigation Input */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {isRTL ? "الانتقال إلى:" : "Go to:"}
                </span>
                <input
                  type="number"
                  min="1"
                  max={totalPages}
                  value={pageInput}
                  onChange={(e) => setPageInput(e.target.value)}
                  onKeyDown={handlePageInputKeyDown}
                  placeholder={isRTL ? "رقم الصفحة" : "Page #"}
                  className="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none text-center"
                />
                <Button
                  onClick={() => handlePageNavigation(pageInput)}
                  disabled={!pageInput.trim()}
                  variant="outline"
                  size="sm"
                  className="px-3"
                >
                  {isRTL ? "انتقال" : "Go"}
                </Button>
              </div>

              {/* Previous/Next Buttons */}
              <div className="flex gap-2">
                <Button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  variant="outline"
                  size="sm"
                >
                  {isRTL ? "السابق" : "Previous"}
                </Button>
                <Button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  variant="outline"
                  size="sm"
                >
                  {isRTL ? "التالي" : "Next"}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Creative Editing Modal */}
      {isEditModalOpen && editingRow && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-900/20 backdrop-blur-sm"
          onClick={() => setIsEditModalOpen(false)}
        >
          <div
            onClick={(e) => e.stopPropagation()}
            className="bg-white rounded-3xl shadow-2xl border border-gray-100 w-full max-w-4xl max-h-[90vh] overflow-hidden"
          >
              {/* Modal Header */}
              <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 px-8 py-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center">
                      <Edit className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-white">
                        {isRTL ? "تحرير تصنيف البيانات" : "Edit Data Classification"}
                      </h3>
                      <p className="text-white/80">
                        {editingRow.tableName}.{editingRow.columnName}
                      </p>
                    </div>
                  </div>
                  <Button
                    onClick={() => setIsEditModalOpen(false)}
                    variant="outline"
                    size="sm"
                    className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-8 space-y-8 max-h-[calc(90vh-200px)] overflow-y-auto">
                {/* Field Information */}
                <div className="bg-gray-50 rounded-2xl p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <FileSpreadsheet className="w-5 h-5 text-[var(--brand-blue)]" />
                    {isRTL ? "معلومات الحقل" : "Field Information"}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">{isRTL ? "اسم الجدول" : "Table Name"}</label>
                      <p className="text-lg font-semibold text-gray-900">{editingRow.tableName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">{isRTL ? "اسم العمود" : "Column Name"}</label>
                      <p className="text-lg font-semibold text-gray-900">{editingRow.columnName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">{isRTL ? "نوع البيانات" : "Data Type"}</label>
                      <p className="text-lg font-semibold text-gray-900">{editingRow.dataType}</p>
                    </div>
                  </div>
                </div>

                {/* Classification Form */}
                <div className="space-y-6">
                  {/* Confidentiality Level */}
                  <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl p-6 border border-orange-100">
                    <label className="block text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Eye className="w-4 h-4 text-orange-600" />
                      </div>
                      {isRTL ? "مستوى السرية" : "Confidentiality Level"}
                    </label>
                    <select
                      value={editingData.confidentialityLevel || ''}
                      onChange={(e) => setEditingData(prev => ({ 
                        ...prev, 
                        confidentialityLevel: e.target.value as "Public" | "Confidential" | "Secret" | "Top Secret" || undefined
                      }))}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none text-lg"
                    >
                      <option value="">{isRTL ? "اختر مستوى السرية" : "Select Confidentiality Level"}</option>
                      <option value="Public">{isRTL ? "عام" : "Public"}</option>
                      <option value="Confidential">{isRTL ? "سري" : "Confidential"}</option>
                      <option value="Secret">{isRTL ? "سري جداً" : "Secret"}</option>
                      <option value="Top Secret">{isRTL ? "سري للغاية" : "Top Secret"}</option>
                    </select>
                  </div>

                  {/* Confidentiality Reasoning */}
                  <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl p-6 border border-orange-100">
                    <label className="block text-lg font-semibold text-gray-900 mb-3">
                      {isRTL ? "سبب تصنيف السرية" : "Confidentiality Reasoning"}
                    </label>
                    <textarea
                      value={editingData.confidentialityReasoning || ''}
                      onChange={(e) => setEditingData(prev => ({ 
                        ...prev, 
                        confidentialityReasoning: e.target.value 
                      }))}
                      placeholder={isRTL ? "اشرح سبب تصنيف هذا الحقل بهذا المستوى من السرية..." : "Explain why this field is classified at this confidentiality level..."}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none resize-none"
                      rows={4}
                    />
                  </div>

                  {/* Personal Data */}
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                    <label className="block text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <User className="w-4 h-4 text-blue-600" />
                      </div>
                      {isRTL ? "هل يحتوي على بيانات شخصية؟" : "Contains Personal Data?"}
                    </label>
                    <select
                      value={editingData.hasPersonalData === undefined ? '' : editingData.hasPersonalData.toString()}
                      onChange={(e) => setEditingData(prev => ({ 
                        ...prev, 
                        hasPersonalData: e.target.value === '' ? undefined : e.target.value === 'true'
                      }))}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none text-lg"
                    >
                      <option value="">{isRTL ? "اختر..." : "Select..."}</option>
                      <option value="true">{isRTL ? "نعم، يحتوي على بيانات شخصية" : "Yes, contains personal data"}</option>
                      <option value="false">{isRTL ? "لا، لا يحتوي على بيانات شخصية" : "No, does not contain personal data"}</option>
                    </select>
                  </div>

                  {/* Personal Data Reasoning */}
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                    <label className="block text-lg font-semibold text-gray-900 mb-3">
                      {isRTL ? "سبب تصنيف البيانات الشخصية" : "Personal Data Reasoning"}
                    </label>
                    <textarea
                      value={editingData.personalDataReasoning || ''}
                      onChange={(e) => setEditingData(prev => ({ 
                        ...prev, 
                        personalDataReasoning: e.target.value 
                      }))}
                      placeholder={isRTL ? "اشرح سبب اعتبار هذا الحقل يحتوي أو لا يحتوي على بيانات شخصية..." : "Explain why this field does or does not contain personal data..."}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none resize-none"
                      rows={4}
                    />
                  </div>
                </div>

                {/* Needs Review Section */}
                <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-2xl p-6 border border-amber-100">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="needsReview"
                      checked={editingData.needsReview || false}
                      onChange={(e) => setEditingData(prev => ({
                        ...prev,
                        needsReview: e.target.checked
                      }))}
                      className="w-5 h-5 text-[var(--brand-blue)] border-gray-300 rounded focus:ring-[var(--brand-blue)]/20"
                    />
                    <label htmlFor="needsReview" className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <div className="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center">
                        <Eye className="w-4 h-4 text-amber-600" />
                      </div>
                      {isRTL ? "يحتاج إلى مراجعة إضافية" : "Needs Additional Review"}
                    </label>
                  </div>
                  <p className="text-sm text-gray-600 mt-2 mr-8">
                    {isRTL ? "ضع علامة إذا كان هذا العنصر يحتاج إلى مراجعة إضافية من قبل خبير" : "Mark if this item needs additional review by an expert"}
                  </p>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="px-8 py-6 bg-gray-50 border-t border-gray-100 flex justify-end gap-3">
                <Button
                  onClick={() => setIsEditModalOpen(false)}
                  variant="outline"
                  className="px-6 py-3"
                  disabled={isSaving}
                >
                  {isRTL ? "إلغاء" : "Cancel"}
                </Button>
                <Button
                  onClick={handleSaveEdit}
                  disabled={isSaving}
                  className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white px-6 py-3 flex items-center gap-2"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      {isRTL ? "جاري الحفظ..." : "Saving..."}
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      {isRTL ? "حفظ التغييرات" : "Save Changes"}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Changes Review Modal */}
        {isChangesModalOpen && changesViewRow && (
          <div
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-900/20 backdrop-blur-sm"
            onClick={() => setIsChangesModalOpen(false)}
          >
            <div
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-3xl shadow-2xl border border-gray-100 w-full max-w-3xl max-h-[90vh] overflow-hidden"
            >
              {/* Modal Header */}
              <div className="bg-gradient-to-r from-orange-500 to-amber-500 px-8 py-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center">
                      <Eye className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-white">
                        {isRTL ? "مراجعة التغييرات" : "Review Changes"}
                      </h3>
                      <p className="text-white/80">
                        {changesViewRow.tableName}.{changesViewRow.columnName}
                      </p>
                    </div>
                  </div>
                  <Button
                    onClick={() => setIsChangesModalOpen(false)}
                    variant="outline"
                    size="sm"
                    className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-8 max-h-[60vh] overflow-y-auto">
                <div className="space-y-6">
                  {/* Field Information */}
                  <div className="bg-gray-50 rounded-2xl p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <FileSpreadsheet className="w-5 h-5 text-gray-600" />
                      {isRTL ? "معلومات الحقل" : "Field Information"}
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600">{isRTL ? "اسم الجدول" : "Table Name"}</label>
                        <p className="text-lg font-semibold text-gray-900">{changesViewRow.tableName}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">{isRTL ? "اسم العمود" : "Column Name"}</label>
                        <p className="text-lg font-semibold text-gray-900">{changesViewRow.columnName}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">{isRTL ? "نوع البيانات" : "Data Type"}</label>
                        <p className="text-lg font-semibold text-gray-900">{changesViewRow.dataType}</p>
                      </div>
                    </div>
                  </div>

                  {/* Changes Summary */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <Eye className="w-5 h-5 text-orange-600" />
                      {isRTL ? "ملخص التغييرات" : "Changes Summary"}
                    </h4>

                    {/* Confidentiality Level Changes */}
                    {changesViewRow.confidentialityLevelChanged && (
                      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                        <h5 className="font-semibold text-blue-900 mb-2">
                          {isRTL ? "تغيير مستوى السرية" : "Confidentiality Level Change"}
                        </h5>
                        <div className="flex items-center gap-4">
                          <div className="flex-1">
                            <label className="text-sm text-blue-700">{isRTL ? "القيمة السابقة" : "Previous Value"}</label>
                            <div className="bg-red-100 border border-red-300 rounded-lg px-3 py-2 text-red-800 font-medium">
                              {changesViewRow.confidentialityLevelOldValue || (isRTL ? "غير محدد" : "Not Set")}
                            </div>
                          </div>
                          <div className="text-2xl text-blue-600">→</div>
                          <div className="flex-1">
                            <label className="text-sm text-blue-700">{isRTL ? "القيمة الجديدة" : "New Value"}</label>
                            <div className="bg-green-100 border border-green-300 rounded-lg px-3 py-2 text-green-800 font-medium">
                              {changesViewRow.confidentialityLevelNewValue || (isRTL ? "غير محدد" : "Not Set")}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Personal Data Changes */}
                    {changesViewRow.hasPersonalDataChanged && (
                      <div className="bg-purple-50 border border-purple-200 rounded-xl p-4">
                        <h5 className="font-semibold text-purple-900 mb-2">
                          {isRTL ? "تغيير حالة البيانات الشخصية" : "Personal Data Status Change"}
                        </h5>
                        <div className="flex items-center gap-4">
                          <div className="flex-1">
                            <label className="text-sm text-purple-700">{isRTL ? "القيمة السابقة" : "Previous Value"}</label>
                            <div className="bg-red-100 border border-red-300 rounded-lg px-3 py-2 text-red-800 font-medium">
                              {changesViewRow.hasPersonalDataOldValue === undefined
                                ? (isRTL ? "غير محدد" : "Not Set")
                                : changesViewRow.hasPersonalDataOldValue
                                  ? (isRTL ? "نعم" : "Yes")
                                  : (isRTL ? "لا" : "No")
                              }
                            </div>
                          </div>
                          <div className="text-2xl text-purple-600">→</div>
                          <div className="flex-1">
                            <label className="text-sm text-purple-700">{isRTL ? "القيمة الجديدة" : "New Value"}</label>
                            <div className="bg-green-100 border border-green-300 rounded-lg px-3 py-2 text-green-800 font-medium">
                              {changesViewRow.hasPersonalDataNewValue === undefined
                                ? (isRTL ? "غير محدد" : "Not Set")
                                : changesViewRow.hasPersonalDataNewValue
                                  ? (isRTL ? "نعم" : "Yes")
                                  : (isRTL ? "لا" : "No")
                              }
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* No Changes Message */}
                    {!changesViewRow.confidentialityLevelChanged && !changesViewRow.hasPersonalDataChanged && (
                      <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 text-center">
                        <p className="text-gray-600">
                          {isRTL ? "لم يتم تسجيل أي تغييرات في مستويات التصنيف" : "No specification level changes recorded"}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="px-8 py-6 bg-gray-50 border-t border-gray-100 flex justify-end gap-3">
                <Button
                  onClick={() => setIsChangesModalOpen(false)}
                  variant="outline"
                  className="px-6 py-3"
                >
                  {isRTL ? "إغلاق" : "Close"}
                </Button>
              </div>
            </div>
          </div>
        )}
    </div>
  );
}