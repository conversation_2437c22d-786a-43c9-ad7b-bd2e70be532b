import { 
  getAuth, 
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User,
  sendPasswordResetEmail,
  signInWithPopup,
  GoogleAuthProvider,
  sendEmailVerification,
  updateProfile,
  updateEmail,
  updatePassword
} from 'firebase/auth';
import { firebaseApp } from '../config';

// Initialize Firebase Authentication
const auth = getAuth(firebaseApp);

// Email & Password Authentication
const registerWithEmailAndPassword = async (
  email: string, 
  password: string
): Promise<User> => {
  const userCredential = await createUserWithEmailAndPassword(auth, email, password);
  return userCredential.user;
};

const loginWithEmailAndPassword = async (
  email: string, 
  password: string
): Promise<User> => {
  const userCredential = await signInWithEmailAndPassword(auth, email, password);
  return userCredential.user;
};

const resetPassword = async (email: string): Promise<void> => {
  await sendPasswordResetEmail(auth, email);
};

const sendUserEmailVerification = async (): Promise<void> => {
  if (auth.currentUser) {
    await sendEmailVerification(auth.currentUser);
  } else {
    throw new Error('No user is signed in to send verification email');
  }
};

// Social Media Authentication
const googleProvider = new GoogleAuthProvider();

const signInWithGoogle = async (): Promise<User> => {
  const userCredential = await signInWithPopup(auth, googleProvider);
  return userCredential.user;
};

// User Profile Management
const updateUserProfile = async (
  displayName?: string | null, 
  photoURL?: string | null
): Promise<void> => {
  if (auth.currentUser) {
    await updateProfile(auth.currentUser, { displayName, photoURL });
  } else {
    throw new Error('No user is signed in');
  }
};

const updateUserEmail = async (newEmail: string): Promise<void> => {
  if (auth.currentUser) {
    await updateEmail(auth.currentUser, newEmail);
  } else {
    throw new Error('No user is signed in');
  }
};

const updateUserPassword = async (newPassword: string): Promise<void> => {
  if (auth.currentUser) {
    await updatePassword(auth.currentUser, newPassword);
  } else {
    throw new Error('No user is signed in');
  }
};

// Session Management
const logoutUser = async (): Promise<void> => {
  await signOut(auth);
};

// Current User
const getCurrentUser = (): User | null => {
  return auth.currentUser;
};

// Check Email Verification Status
const isEmailVerified = (): boolean => {
  const user = auth.currentUser;
  if (!user) {
    // It's generally better to throw an error here or handle it based on specific app logic
    // Returning false might mask the fact that no user is logged in.
    throw new Error('Cannot check email verification status: No user is signed in.');
  }
  // The user object might need to be reloaded to get the latest verification status
  // Consider calling user.reload() before checking emailVerified in components where this matters.
  return user.emailVerified;
};

export {
  auth,
  registerWithEmailAndPassword,
  loginWithEmailAndPassword,
  resetPassword,
  sendUserEmailVerification,
  signInWithGoogle,
  updateUserProfile,
  updateUserEmail,
  updateUserPassword,
  logoutUser,
  getCurrentUser,
  isEmailVerified,
  onAuthStateChanged
}; 