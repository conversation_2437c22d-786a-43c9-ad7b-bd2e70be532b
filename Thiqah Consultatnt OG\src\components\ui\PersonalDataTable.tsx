"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Search, FileSpreadsheet, Trash2, Plus, Loader2, Download, Eye, Edit, Filter, X, Save, User, Brain, Settings, ChevronDown, Zap, Play, Pause, Upload, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemData, SystemsService } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";

import * as XLSX from 'xlsx';

// Extended SystemData interface for review functionality
interface ExtendedPersonalData extends SystemData {
  isReviewed?: boolean;
  reviewedBy?: string;
  reviewedAt?: string;
  auditTrail?: string; // "Yes" or "No"
  needsReview?: boolean;
  pseudonyms?: string; // "Yes" or "No"
  anonyms?: string; // "Yes" or "No"
  pseudonymsJustification?: string;
  anonymsJustification?: string;
}

interface PersonalDataTableProps {
  data: ExtendedPersonalData[];
  isLoading: boolean;
  isRTL: boolean;
  systemId: string;
  onImportClick: () => void;
  onDeleteAll?: () => void;
  onDataUpdate?: () => void;
  systemContext?: string;
}

export function PersonalDataTable({ data, isLoading, isRTL, systemId, onImportClick, onDeleteAll, onDataUpdate, systemContext }: PersonalDataTableProps) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageInput, setPageInput] = useState("");

  const [viewMode, setViewMode] = useState<'classification' | 'editing'>('classification');
  const [tableFilter, setTableFilter] = useState("");
  const [dataTypeFilter, setDataTypeFilter] = useState("");
  const [confidentialityFilter, setConfidentialityFilter] = useState("");
  const [reviewStatusFilter, setReviewStatusFilter] = useState("");
  const [reviewerFilter, setReviewerFilter] = useState("");
  const [personalDataTypeFilter, setPersonalDataTypeFilter] = useState("");
  const [specialCategoryFilter, setSpecialCategoryFilter] = useState("");
  const [auditTrailFilter, setAuditTrailFilter] = useState("");
  const [needsReviewFilter, setNeedsReviewFilter] = useState("");
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isChangesModalOpen, setIsChangesModalOpen] = useState(false);
  const [editingRow, setEditingRow] = useState<ExtendedPersonalData | null>(null);
  const [changesViewRow, setChangesViewRow] = useState<ExtendedPersonalData | null>(null);
  const [editingData, setEditingData] = useState<Partial<ExtendedPersonalData>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [isClassifyingPage, setIsClassifyingPage] = useState(false);

  // Auto-classification state
  const [isAutoClassifying, setIsAutoClassifying] = useState(false);
  const [autoClassifyProgress, setAutoClassifyProgress] = useState({ current: 0, total: 0 });
  const [autoClassifyPaused, setAutoClassifyPaused] = useState(false);
  const [isActionsDropdownOpen, setIsActionsDropdownOpen] = useState(false);

  // Multi-row selection state
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const [isMarkingAuditTrail, setIsMarkingAuditTrail] = useState(false);

  const rowsPerPage = viewMode === 'classification' ? 50 : 50;

  // Filter data - only personal data
  const personalDataOnly = data.filter(row => row.hasPersonalData === true);

  // Filter data based on search term and additional filters
  const filteredData = personalDataOnly.filter(row => {
    const matchesSearch = row.tableName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      row.columnName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      row.dataType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (row.schemaName && row.schemaName.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesTable = !tableFilter || row.tableName.toLowerCase().includes(tableFilter.toLowerCase());
    const matchesDataType = !dataTypeFilter || row.dataType.toLowerCase().includes(dataTypeFilter.toLowerCase());
    const matchesConfidentiality = !confidentialityFilter || 
      (row.confidentialityLevel && row.confidentialityLevel.toLowerCase().includes(confidentialityFilter.toLowerCase()));
    
    // Review status filter
    const matchesReviewStatus = !reviewStatusFilter || 
      (reviewStatusFilter === 'reviewed' && row.isReviewed) ||
      (reviewStatusFilter === 'unreviewed' && !row.isReviewed);

    // Reviewer filter
    const matchesReviewer = !reviewerFilter || 
      (row.reviewedBy && row.reviewedBy.toLowerCase().includes(reviewerFilter.toLowerCase()));

    // Personal data type filter
    const matchesPersonalDataType = !personalDataTypeFilter ||
      (row.personalDataType && row.personalDataType === personalDataTypeFilter);

    // Special category filter
    const matchesSpecialCategory = !specialCategoryFilter ||
      (row.specialCategoryType && row.specialCategoryType === specialCategoryFilter);

    // Audit trail filter
    const matchesAuditTrail = !auditTrailFilter ||
      (auditTrailFilter === 'yes' && row.auditTrail === 'Yes') ||
      (auditTrailFilter === 'no' && row.auditTrail === 'No') ||
      (auditTrailFilter === 'not_classified' && (!row.auditTrail || row.auditTrail === ''));

    // Needs review filter
    const matchesNeedsReview = !needsReviewFilter ||
      (needsReviewFilter === 'yes' && row.needsReview === true) ||
      (needsReviewFilter === 'no' && row.needsReview !== true);

    return matchesSearch && matchesTable && matchesDataType && matchesConfidentiality && matchesReviewStatus && matchesReviewer && matchesPersonalDataType && matchesSpecialCategory && matchesAuditTrail && matchesNeedsReview;
  });

  // Get unique values for filter dropdowns
  const uniqueTables = Array.from(new Set(personalDataOnly.map(row => row.tableName).filter(Boolean))).sort();
  const uniqueDataTypes = Array.from(new Set(personalDataOnly.map(row => row.dataType).filter(Boolean))).sort();
  const uniqueConfidentialityLevels = Array.from(new Set(personalDataOnly.filter(row => row.confidentialityLevel).map(row => row.confidentialityLevel!).filter(Boolean))).sort();

  const uniquePersonalDataTypes = Array.from(new Set(personalDataOnly.filter(row => row.personalDataType).map(row => row.personalDataType!).filter(Boolean))).sort();
  const uniqueSpecialCategories = Array.from(new Set(personalDataOnly.filter(row => row.specialCategoryType && row.specialCategoryType !== 'none').map(row => row.specialCategoryType!).filter(Boolean))).sort();

  // Reset filters when switching view modes
  const handleViewModeChange = (mode: 'classification' | 'editing') => {
    setViewMode(mode);
    setCurrentPage(1);
    setSelectedRows(new Set()); // Clear selection when switching modes
    if (mode === 'classification') {
      setTableFilter("");
      setDataTypeFilter("");
      setConfidentialityFilter("");
      setReviewStatusFilter("");
      setReviewerFilter("");
      setPersonalDataTypeFilter("");
      setSpecialCategoryFilter("");
      setAuditTrailFilter("");
      setNeedsReviewFilter("");
    }
  };

  // Selection helper functions
  const handleRowSelection = (rowId: string, checked: boolean) => {
    const newSelection = new Set(selectedRows);
    if (checked) {
      newSelection.add(rowId);
    } else {
      newSelection.delete(rowId);
    }
    setSelectedRows(newSelection);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allRowIds = new Set(currentData.map(row => row.id!).filter(Boolean));
      setSelectedRows(allRowIds);
    } else {
      setSelectedRows(new Set());
    }
  };

  // Pagination
  const totalPages = Math.ceil(filteredData.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  const isAllSelected = currentData.length > 0 && currentData.every(row => row.id && selectedRows.has(row.id));
  const isIndeterminate = selectedRows.size > 0 && !isAllSelected;

  // Mark selected rows as AuditTrail or Not AuditTrail
  const handleMarkAuditTrail = async (value: "Yes" | "No") => {
    if (selectedRows.size === 0) {
      toast({
        title: isRTL ? "لا توجد صفوف محددة" : "No Rows Selected",
        description: isRTL ? "يرجى تحديد صفوف لتمييزها" : "Please select rows to mark",
        variant: "destructive"
      });
      return;
    }

    setIsMarkingAuditTrail(true);

    try {
      const updates = Array.from(selectedRows).map(rowId => ({
        documentId: rowId,
        data: { auditTrail: value }
      }));

      await SystemsService.updateSystemDataBatch(systemId, updates);

      const isYes = value === "Yes";
      toast({
        title: isRTL ? "تم تمييز الصفوف" : "Rows Marked",
        description: isRTL ?
          `تم تمييز ${selectedRows.size} صف ${isYes ? "كجدول مراجعة" : "كغير جدول مراجعة"}` :
          `Marked ${selectedRows.size} rows as ${isYes ? "audit trail" : "not audit trail"}`,
      });

      setSelectedRows(new Set()); // Clear selection

      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Error marking rows:', error);
      toast({
        title: isRTL ? "خطأ في التمييز" : "Marking Error",
        description: isRTL ? "فشل في تمييز الصفوف" : "Failed to mark rows",
        variant: "destructive",
      });
    } finally {
      setIsMarkingAuditTrail(false);
    }
  };

  // Bulk mark as needs review
  const handleBulkMarkAsNeedsReview = async () => {
    if (selectedRows.size === 0) {
      toast({
        title: isRTL ? "لا توجد عناصر محددة" : "No Items Selected",
        description: isRTL ? "يرجى تحديد العناصر المراد تمييزها" : "Please select items to mark",
        variant: "destructive"
      });
      return;
    }

    setIsMarkingAuditTrail(true);
    try {
      const updates = Array.from(selectedRows).map(rowId => ({
        documentId: rowId,
        data: { needsReview: true }
      }));

      await SystemsService.updateSystemDataBatch(systemId, updates);

      toast({
        title: isRTL ? "تم التمييز المجمع" : "Bulk Marking Complete",
        description: isRTL ?
          `تم تمييز ${selectedRows.size} عنصر كيحتاج مراجعة` :
          `Marked ${selectedRows.size} items as needs review`,
      });

      setSelectedRows(new Set());
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Bulk needs review error:', error);
      toast({
        title: isRTL ? "خطأ في التمييز المجمع" : "Bulk Marking Error",
        description: error instanceof Error ? error.message : "Failed to mark items as needs review",
        variant: "destructive"
      });
    } finally {
      setIsMarkingAuditTrail(false);
    }
  };

  // Bulk remove needs review marks
  const handleBulkRemoveNeedsReview = async () => {
    if (selectedRows.size === 0) {
      toast({
        title: isRTL ? "لا توجد عناصر محددة" : "No Items Selected",
        description: isRTL ? "يرجى تحديد العناصر المراد إزالة تمييزها" : "Please select items to remove marks",
        variant: "destructive"
      });
      return;
    }

    setIsMarkingAuditTrail(true);
    try {
      const updates = Array.from(selectedRows).map(rowId => ({
        documentId: rowId,
        data: { needsReview: false }
      }));

      await SystemsService.updateSystemDataBatch(systemId, updates);

      toast({
        title: isRTL ? "تم إزالة التمييز المجمع" : "Bulk Marking Removal Complete",
        description: isRTL ?
          `تم إزالة تمييز ${selectedRows.size} عنصر` :
          `Removed needs review marks from ${selectedRows.size} items`,
      });

      setSelectedRows(new Set());
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Bulk remove needs review error:', error);
      toast({
        title: isRTL ? "خطأ في إزالة التمييز المجمع" : "Bulk Marking Removal Error",
        description: error instanceof Error ? error.message : "Failed to remove needs review marks",
        variant: "destructive"
      });
    } finally {
      setIsMarkingAuditTrail(false);
    }
  };

  // Export filtered personal data to Excel
  const handleExcelExport = () => {
    try {
      const exportData = filteredData.map(row => ({
        [isRTL ? 'اسم المخطط' : 'Schema Name']: row.schemaName || '',
        [isRTL ? 'اسم الجدول' : 'Table Name']: row.tableName,
        [isRTL ? 'اسم العمود' : 'Column Name']: row.columnName,
        [isRTL ? 'نوع البيانات' : 'Data Type']: row.dataType,
        [isRTL ? 'مستوى السرية' : 'Confidentiality Level']: row.confidentialityLevel || '',
        [isRTL ? 'سبب السرية' : 'Confidentiality Reasoning']: row.confidentialityReasoning || '',
        [isRTL ? 'بيانات شخصية' : 'Personal Data']: row.hasPersonalData ? (isRTL ? 'نعم' : 'Yes') : (isRTL ? 'لا' : 'No'),
        [isRTL ? 'سبب البيانات الشخصية' : 'Personal Data Reasoning']: row.personalDataReasoning || '',
        [isRTL ? 'نوع البيانات الشخصية' : 'Personal Data Type']: row.personalDataType ? (
          row.personalDataType === 'direct' ? (isRTL ? 'مباشر' : 'Direct') :
          row.personalDataType === 'indirect' ? (isRTL ? 'غير مباشر' : 'Indirect') :
          row.personalDataType === 'pseudonymous' ? (isRTL ? 'مستعار' : 'Pseudonymous') :
          row.personalDataType === 'anonymous' ? (isRTL ? 'مجهول' : 'Anonymous') :
          row.personalDataType
        ) : '',
        [isRTL ? 'يمكن إخفاء الهوية' : 'Can be pseudonymized']: (row as ExtendedPersonalData & { pseudonyms?: string }).pseudonyms || (isRTL ? 'غير محدد' : 'Not Set'),
        [isRTL ? 'يمكن إجراء إخفاء كامل للهوية' : 'Can be anonymized']: (row as ExtendedPersonalData & { anonyms?: string }).anonyms || (isRTL ? 'غير محدد' : 'Not Set'),
        [isRTL ? 'الفئة الخاصة' : 'Special Category']: row.specialCategoryType && row.specialCategoryType !== 'none' ? row.specialCategoryType : '',
        [isRTL ? 'جدول المراجعة' : 'Audit Trail']: row.auditTrail || (isRTL ? 'لا' : 'No'),
        [isRTL ? 'مراجع' : 'Reviewed']: row.isReviewed ? (isRTL ? 'نعم' : 'Yes') : (isRTL ? 'لا' : 'No'),
        [isRTL ? 'مراجع بواسطة' : 'Reviewed By']: row.reviewedBy || '',
        [isRTL ? 'تاريخ المراجعة' : 'Review Date']: row.reviewedAt || ''
      }));

      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, isRTL ? 'البيانات الشخصية' : 'Personal Data');
      
      const fileName = `personal-data-${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);

      toast({
        title: isRTL ? "تم تصدير البيانات" : "Data Exported",
        description: isRTL ? `تم تصدير ${exportData.length} سجل من البيانات الشخصية` : `Exported ${exportData.length} personal data records`,
      });
    } catch (error) {
      console.error('Error exporting data:', error);
      toast({
        title: isRTL ? "خطأ في التصدير" : "Export Error",
        description: isRTL ? "فشل في تصدير البيانات" : "Failed to export data",
        variant: "destructive",
      });
    }
  };

  // Handle page navigation
  const handlePageNavigation = (pageNumber: string) => {
    const page = parseInt(pageNumber);
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      setPageInput("");
    }
  };

  const handlePageInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handlePageNavigation(pageInput);
    }
  };

  // Save edit functionality
  const handleSaveEdit = async () => {
    if (!editingRow?.id) return;

    setIsSaving(true);
    try {
      // Check for changes in specification levels and track them
      const updateData = { ...editingData };
      let hasSpecificationChanges = false;

      // Track personal data type changes
      if (editingData.personalDataType !== editingRow.personalDataType) {
        updateData.personalDataTypeChanged = true;
        updateData.personalDataTypeOldValue = editingRow.personalDataType || '';
        updateData.personalDataTypeNewValue = editingData.personalDataType || '';
        hasSpecificationChanges = true;
      }

      // Track special category type changes
      if (editingData.specialCategoryType !== editingRow.specialCategoryType) {
        updateData.specialCategoryTypeChanged = true;
        updateData.specialCategoryTypeOldValue = editingRow.specialCategoryType || '';
        updateData.specialCategoryTypeNewValue = editingData.specialCategoryType || '';
        hasSpecificationChanges = true;
      }

      // Auto-flag as needs review if specification levels changed (unless manually set)
      if (hasSpecificationChanges && editingData.needsReview === undefined) {
        updateData.needsReview = true;
      }

      await SystemsService.updateSystemDataDocument(systemId, editingRow.id, {
        ...updateData,
        classificationStatus: 'fully_classified',
        personalDataClassificationStatus: updateData.personalDataType || updateData.specialCategoryType ? 'classified' : 'pending'
      });

      toast({
        title: isRTL ? "تم الحفظ بنجاح" : "Saved Successfully",
        description: isRTL ? "تم حفظ التغييرات" : "Changes have been saved",
      });

      setIsEditModalOpen(false);
      setEditingRow(null);
      setEditingData({});

      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Error saving edit:', error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: isRTL ? "فشل في حفظ التغييرات" : "Failed to save changes",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Personal Data Classification function (manual, current page only)
  const handlePersonalDataClassification = async () => {
    if (currentData.length === 0) {
      toast({
        title: isRTL ? "لا توجد بيانات" : "No Data",
        description: isRTL ? "لا توجد بيانات شخصية في هذه الصفحة" : "No personal data on this page",
        variant: "destructive"
      });
      return;
    }

    setIsClassifyingPage(true);

    try {
      // Validate document IDs before processing
      const invalidRecords = currentData.filter(record => !record.id || !record.id.trim());
      if (invalidRecords.length > 0) {
        console.warn(`Found ${invalidRecords.length} records with invalid IDs on page ${currentPage}`);
        throw new Error(`Page ${currentPage} contains ${invalidRecords.length} records with invalid document IDs. Please refresh the data and try again.`);
      }

      console.log(`Starting personal data AI classification for page ${currentPage} with ${currentData.length} records`);
      console.log('Record IDs:', currentData.map(r => r.id));

      toast({
        title: isRTL ? "تصنيف البيانات الشخصية" : "Classifying Personal Data",
        description: isRTL ?
          `تصنيف ${currentData.length} سجل في الصفحة ${currentPage} باستخدام Gemini-2.5-flash` :
          `Classifying ${currentData.length} records on page ${currentPage} using Gemini-2.5-flash`,
      });

      // Get comprehensive system context for better classification
      let comprehensiveContext = '';
      try {
        const contextPoints = await SystemsService.getSystemContextPoints(systemId);
        if (contextPoints && contextPoints.length > 0) {
          const contextByTag = contextPoints.reduce((acc, point) => {
            if (!acc[point.tag]) {
              acc[point.tag] = [];
            }
            acc[point.tag].push(point.content);
            return acc;
          }, {} as Record<string, string[]>);

          const contextSections = [];
          if (contextByTag['System Description']) {
            contextSections.push(`SYSTEM DESCRIPTION: ${contextByTag['System Description'].join(' ')}`);
          }
          if (contextByTag['System Personas']) {
            contextSections.push(`SYSTEM PERSONAS: ${contextByTag['System Personas'].join(' ')}`);
          }
          if (contextByTag['System Service Brief']) {
            contextSections.push(`SYSTEM SERVICES: ${contextByTag['System Service Brief'].join(' ')}`);
          }

          comprehensiveContext = contextSections.join(' | ');
        }
      } catch (contextError) {
        console.warn('Could not fetch comprehensive context:', contextError);
      }

      const personalDataClassificationResponse = await fetch('/api/ai/classification/personal-data-classification', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pageData: currentData,
          systemId,
          pageNumber: currentPage,
          systemContext: comprehensiveContext || undefined
        }),
        signal: AbortSignal.timeout(300000) // 300 seconds timeout
      });

      if (!personalDataClassificationResponse.ok) {
        const errorData = await personalDataClassificationResponse.json();
        throw new Error(errorData.error || 'Personal data classification failed');
      }

      const personalDataClassificationResult = await personalDataClassificationResponse.json();
      console.log('Personal data classification result:', personalDataClassificationResult);

      // Validate AI response
      if (!personalDataClassificationResult.classifications || !Array.isArray(personalDataClassificationResult.classifications)) {
        throw new Error('Invalid AI response: missing classifications array');
      }

      // Update personal data classifications with validation
      const personalDataUpdates: Array<{
        documentId: string;
        data: Partial<ExtendedPersonalData & {
          pseudonyms?: string;
          anonyms?: string;
          pseudonymsJustification?: string;
          anonymsJustification?: string;
        }>;
      }> = [];

      personalDataClassificationResult.classifications.forEach((classification: {
        recordId: string;
        personalDataType: string;
        specialCategoryType: string;
        pseudonyms: string;
        anonyms: string;
        pseudonymsJustification: string;
        anonymsJustification: string;
      }) => {
        // Validate classification data
        if (!classification.recordId || !classification.recordId.trim()) {
          console.warn('Skipping classification with invalid recordId:', classification);
          return;
        }

        // Verify the record exists in current page data
        const originalRecord = currentData.find(record => record.id === classification.recordId);
        if (!originalRecord || !originalRecord.id) {
          console.warn(`Record ${classification.recordId} not found in current page data, skipping`);
          return;
        }

        // Validate personalDataType to allow all supported types
        const validPersonalDataType = classification.personalDataType.toLowerCase();
        const allowedTypes = ['direct', 'indirect', 'pseudonymous', 'anonymous'];
        const finalPersonalDataType = allowedTypes.includes(validPersonalDataType) ? validPersonalDataType : 'direct';

        personalDataUpdates.push({
          documentId: originalRecord.id,
          data: {
            personalDataType: finalPersonalDataType as "direct" | "indirect" | "pseudonymous" | "anonymous",
            specialCategoryType: classification.specialCategoryType as "PII" | "PHI" | "PCI" | "Genetic" | "Biometric" | "none",
            pseudonyms: classification.pseudonyms, // Store the new fields
            anonyms: classification.anonyms, // Store the new fields
            pseudonymsJustification: classification.pseudonymsJustification, // Store justifications
            anonymsJustification: classification.anonymsJustification, // Store justifications
            personalDataClassificationStatus: 'classified'
          }
        });
      });

      console.log(`Prepared ${personalDataUpdates.length} valid updates for batch processing`);

      // Batch update personal data classifications
      if (personalDataUpdates.length > 0) {
        try {
          await SystemsService.updateSystemDataBatch(systemId, personalDataUpdates);
          console.log(`Successfully updated ${personalDataUpdates.length} records`);
        } catch (updateError) {
          console.error('Batch update failed:', updateError);
          throw new Error(`Failed to update personal data classifications: ${updateError instanceof Error ? updateError.message : 'Unknown error'}`);
        }
      } else {
        throw new Error('No valid classifications to update. Please check the data and try again.');
      }

      toast({
        title: isRTL ? "تم تصنيف البيانات الشخصية" : "Personal Data Classified",
        description: isRTL ?
          `تم تصنيف ${personalDataUpdates.length} سجل` :
          `Classified ${personalDataUpdates.length} records`,
      });

      // Refresh data
      if (onDataUpdate) {
        onDataUpdate();
      }

    } catch (error) {
      console.error('Personal data classification error:', error);
      toast({
        title: isRTL ? "خطأ في تصنيف البيانات الشخصية" : "Personal Data Classification Error",
        description: error instanceof Error ? error.message : "An error occurred during personal data classification",
        variant: "destructive"
      });
    } finally {
      setIsClassifyingPage(false);
    }
  };

  // Auto-classification function for personal data that processes all pages automatically
  const handleAutoPersonalDataClassification = async () => {
    if (filteredData.length === 0) {
      toast({
        title: isRTL ? "لا توجد بيانات شخصية" : "No Personal Data",
        description: isRTL ? "لا توجد بيانات شخصية للتصنيف" : "No personal data to classify",
        variant: "destructive"
      });
      return;
    }

    setIsAutoClassifying(true);
    setAutoClassifyPaused(false);

    const totalPages = Math.ceil(filteredData.length / rowsPerPage);
    setAutoClassifyProgress({ current: 0, total: totalPages });

    toast({
      title: isRTL ? "بدء التصنيف التلقائي للبيانات الشخصية" : "Starting Auto Personal Data Classification",
      description: isRTL ?
        `سيتم تصنيف ${totalPages} صفحة تلقائياً` :
        `Will automatically classify ${totalPages} pages`,
    });

    try {
      for (let page = 1; page <= totalPages; page++) {
        // Check if auto-classification is paused
        if (autoClassifyPaused) {
          toast({
            title: isRTL ? "تم إيقاف التصنيف التلقائي مؤقتاً" : "Auto-Classification Paused",
            description: isRTL ? "يمكنك استئناف التصنيف لاحقاً" : "You can resume classification later",
          });
          break;
        }

        // Update progress
        setAutoClassifyProgress({ current: page, total: totalPages });
        setCurrentPage(page);

        // Get current page data
        const startIndex = (page - 1) * rowsPerPage;
        const endIndex = startIndex + rowsPerPage;
        const pageData = filteredData.slice(startIndex, endIndex);

        // Check if page is already classified (all records have personal data classifications)
        const unclassifiedRecords = pageData.filter(record =>
          !record.personalDataType || !record.pseudonyms || !record.anonyms
        );

        if (unclassifiedRecords.length === 0) {
          // Page is already classified, skip it
          toast({
            title: isRTL ? `تم تخطي الصفحة ${page}` : `Skipped Page ${page}`,
            description: isRTL ? "الصفحة مصنفة بالفعل" : "Page already classified",
          });
          continue;
        }

        // Classify the page
        try {
          // Get comprehensive system context for better classification
          let comprehensiveContext = systemContext || '';
          try {
            const contextPoints = await SystemsService.getSystemContextPoints(systemId);
            if (contextPoints && contextPoints.length > 0) {
              const contextByTag = contextPoints.reduce((acc, point) => {
                if (!acc[point.tag]) {
                  acc[point.tag] = [];
                }
                acc[point.tag].push(point.content);
                return acc;
              }, {} as Record<string, string[]>);

              const contextSections = [];
              if (contextByTag['System Description']) {
                contextSections.push(`SYSTEM DESCRIPTION: ${contextByTag['System Description'].join(' ')}`);
              }
              if (contextByTag['System Personas']) {
                contextSections.push(`SYSTEM PERSONAS: ${contextByTag['System Personas'].join(' ')}`);
              }
              if (contextByTag['System Service Brief']) {
                contextSections.push(`SYSTEM SERVICES: ${contextByTag['System Service Brief'].join(' ')}`);
              }

              comprehensiveContext = contextSections.join(' | ');
            }
          } catch (contextError) {
            console.warn('Could not fetch comprehensive context:', contextError);
          }

          const personalDataClassificationResponse = await fetch('/api/ai/classification/personal-data-classification', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              pageData: pageData,
              systemId,
              pageNumber: page,
              systemContext: comprehensiveContext || undefined
            }),
            signal: AbortSignal.timeout(300000) // 300 seconds timeout
          });

          if (!personalDataClassificationResponse.ok) {
            const errorData = await personalDataClassificationResponse.json();
            throw new Error(errorData.error || 'Personal data classification failed');
          }

          const personalDataClassificationResult = await personalDataClassificationResponse.json();

          // Update personal data classifications
          const personalDataUpdates: Array<{
            documentId: string;
            data: Partial<Pick<ExtendedPersonalData, 'personalDataType' | 'pseudonyms' | 'pseudonymsJustification' | 'anonyms' | 'anonymsJustification' | 'specialCategoryType'>>;
          }> = [];

          personalDataClassificationResult.classifications.forEach((classification: {
            recordId: string;
            personalDataType: string;
            pseudonyms: "Yes" | "No";
            pseudonymsJustification: string;
            anonyms: "Yes" | "No";
            anonymsJustification: string;
            specialCategoryType: string;
          }) => {
            personalDataUpdates.push({
              documentId: classification.recordId,
              data: {
                personalDataType: classification.personalDataType as "direct" | "indirect" | "pseudonymous" | "anonymous",
                pseudonyms: classification.pseudonyms,
                pseudonymsJustification: classification.pseudonymsJustification,
                anonyms: classification.anonyms,
                anonymsJustification: classification.anonymsJustification,
                specialCategoryType: classification.specialCategoryType as "PII" | "PHI" | "PCI" | "Genetic" | "Biometric" | "none"
              }
            });
          });

          // Batch update personal data classifications
          if (personalDataUpdates.length > 0) {
            try {
              await SystemsService.updateSystemDataBatch(systemId, personalDataUpdates);
            } catch (updateError) {
              console.warn('Some documents could not be updated:', updateError);
            }
          }

          toast({
            title: isRTL ? `تم تصنيف الصفحة ${page}` : `Classified Page ${page}`,
            description: isRTL ?
              `تم تصنيف ${personalDataUpdates.length} سجل` :
              `Classified ${personalDataUpdates.length} records`,
          });

          // Refresh data after each page
          if (onDataUpdate) {
            onDataUpdate();
          }

          // Small delay between pages to prevent overwhelming the API
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (pageError) {
          console.error(`Error classifying page ${page}:`, pageError);
          toast({
            title: isRTL ? `خطأ في تصنيف الصفحة ${page}` : `Error Classifying Page ${page}`,
            description: pageError instanceof Error ? pageError.message : "An error occurred during page classification",
            variant: "destructive"
          });
          // Continue with next page instead of stopping
          continue;
        }
      }

      if (!autoClassifyPaused) {
        toast({
          title: isRTL ? "اكتمل التصنيف التلقائي" : "Auto-Classification Complete",
          description: isRTL ?
            `تم تصنيف جميع الصفحات بنجاح` :
            `All pages have been successfully classified`,
        });
      }

    } catch (error) {
      console.error('Auto-classification error:', error);
      toast({
        title: isRTL ? "خطأ في التصنيف التلقائي" : "Auto-Classification Error",
        description: error instanceof Error ? error.message : "An error occurred during auto-classification",
        variant: "destructive"
      });
    } finally {
      setIsAutoClassifying(false);
      setAutoClassifyProgress({ current: 0, total: 0 });
    }
  };

  // Function to pause/resume auto-classification
  const toggleAutoClassificationPause = () => {
    setAutoClassifyPaused(!autoClassifyPaused);
    toast({
      title: autoClassifyPaused ?
        (isRTL ? "تم استئناف التصنيف التلقائي" : "Auto-Classification Resumed") :
        (isRTL ? "تم إيقاف التصنيف التلقائي مؤقتاً" : "Auto-Classification Paused"),
      description: autoClassifyPaused ?
        (isRTL ? "سيتم استئناف التصنيف من الصفحة الحالية" : "Classification will resume from current page") :
        (isRTL ? "يمكنك استئناف التصنيف لاحقاً" : "You can resume classification later"),
    });
  };

  // Function to stop auto-classification
  const stopAutoClassification = () => {
    setIsAutoClassifying(false);
    setAutoClassifyPaused(false);
    setAutoClassifyProgress({ current: 0, total: 0 });
    toast({
      title: isRTL ? "تم إيقاف التصنيف التلقائي" : "Auto-Classification Stopped",
      description: isRTL ? "تم إيقاف عملية التصنيف التلقائي" : "Auto-classification process has been stopped",
    });
  };

  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden"
      >
        <div className="p-8 text-center">
          <div className="w-16 h-16 bg-[var(--brand-blue)]/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <Loader2 className="w-8 h-8 text-[var(--brand-blue)] animate-spin" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {isRTL ? "جاري تحميل البيانات الشخصية..." : "Loading Personal Data..."}
          </h3>
          <p className="text-gray-600">
            {isRTL ? "يرجى الانتظار..." : "Please wait..."}
          </p>
        </div>
      </motion.div>
    );
  }

  if (personalDataOnly.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden"
      >
        <div className="p-8 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <User className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {isRTL ? "لا توجد بيانات شخصية" : "No Personal Data Found"}
          </h3>
          <p className="text-gray-600 mb-6">
            {isRTL ? "لم يتم العثور على أي بيانات مصنفة كبيانات شخصية" : "No data has been classified as personal data yet"}
          </p>
          <Button
            onClick={onImportClick}
            className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            {isRTL ? "استيراد بيانات" : "Import Data"}
          </Button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden"
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 px-6 py-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
              <User className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-white">
                {isRTL ? "إدارة البيانات الشخصية" : "Personal Data Management"}
              </h3>
              <p className="text-white/80 text-sm">
                {isRTL ? `${personalDataOnly.length} من البيانات الشخصية` : `${personalDataOnly.length} personal data records`}
              </p>
            </div>
          </div>
          <div className="flex gap-3">
            {/* AuditTrail Buttons - Only in editing view when rows are selected */}
            {viewMode === 'editing' && selectedRows.size > 0 && (
              <>
                <Button
                  onClick={() => handleMarkAuditTrail("Yes")}
                  disabled={isMarkingAuditTrail}
                  className="bg-purple-500/20 backdrop-blur-sm border border-purple-300/50 text-white hover:bg-purple-500/30 disabled:opacity-50"
                >
                  {isMarkingAuditTrail ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <FileSpreadsheet className="w-4 h-4 mr-2" />
                  )}
                  {isMarkingAuditTrail
                    ? (isRTL ? "جاري التمييز..." : "Marking...")
                    : (isRTL ? `تمييز كجدول مراجعة (${selectedRows.size})` : `Mark as AuditTrail (${selectedRows.size})`)
                  }
                </Button>
                <Button
                  onClick={() => handleMarkAuditTrail("No")}
                  disabled={isMarkingAuditTrail}
                  className="bg-gray-500/20 backdrop-blur-sm border border-gray-300/50 text-white hover:bg-gray-500/30 disabled:opacity-50"
                >
                  {isMarkingAuditTrail ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <X className="w-4 h-4 mr-2" />
                  )}
                  {isMarkingAuditTrail
                    ? (isRTL ? "جاري التمييز..." : "Marking...")
                    : (isRTL ? `تمييز كغير جدول مراجعة (${selectedRows.size})` : `Mark as Not AuditTrail (${selectedRows.size})`)
                  }
                </Button>
                <Button
                  onClick={handleBulkMarkAsNeedsReview}
                  disabled={isMarkingAuditTrail}
                  className="bg-amber-500/20 backdrop-blur-sm border border-amber-300/50 text-white hover:bg-amber-500/30 disabled:opacity-50"
                >
                  {isMarkingAuditTrail ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Eye className="w-4 h-4 mr-2" />
                  )}
                  {isMarkingAuditTrail
                    ? (isRTL ? "جاري التمييز..." : "Marking...")
                    : (isRTL ? `يحتاج مراجعة (${selectedRows.size})` : `Needs Review (${selectedRows.size})`)
                  }
                </Button>
                <Button
                  onClick={handleBulkRemoveNeedsReview}
                  disabled={isMarkingAuditTrail}
                  className="bg-green-500/20 backdrop-blur-sm border border-green-300/50 text-white hover:bg-green-500/30 disabled:opacity-50"
                >
                  {isMarkingAuditTrail ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <X className="w-4 h-4 mr-2" />
                  )}
                  {isMarkingAuditTrail
                    ? (isRTL ? "جاري التمييز..." : "Marking...")
                    : (isRTL ? `لا يحتاج مراجعة (${selectedRows.size})` : `No Review Needed (${selectedRows.size})`)
                  }
                </Button>
              </>
            )}
            {/* Comprehensive Personal Data Actions Menu */}
            <div className="relative">
              {/* Main Actions Button */}
              {!isAutoClassifying && !isClassifyingPage ? (
                <div className="relative">
                  <Button
                    onClick={() => {
                      const wasOpen = isActionsDropdownOpen;
                      setIsActionsDropdownOpen(!isActionsDropdownOpen);

                      // Show professional toast if closing dropdown during classification
                      if (wasOpen && (isAutoClassifying || isClassifyingPage)) {
                        toast({
                          title: isRTL ? "العملية قيد التنفيذ" : "Operation in Progress",
                          description: isRTL ?
                            "يتم تنفيذ عملية تصنيف البيانات الشخصية في الخلفية. يمكنك متابعة العمل أو مراقبة التقدم من شريط الحالة." :
                            "Personal data classification is running in the background. You can continue working or monitor progress from the status bar.",
                          duration: 4000,
                        });
                      }
                    }}
                    className="bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 disabled:opacity-50 transition-all duration-200 font-medium"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    <span>{isRTL ? "الإجراءات" : "Actions"}</span>
                    <ChevronDown className={`w-4 h-4 ml-2 transition-transform duration-200 ${isActionsDropdownOpen ? 'rotate-180' : ''}`} />
                  </Button>

                  {/* Professional Actions Dropdown */}
                  {isActionsDropdownOpen && (
                    <div className="absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-50 overflow-hidden">
                      {/* Header */}
                      <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
                        <h3 className="text-sm font-semibold text-gray-900">
                          {isRTL ? "إجراءات البيانات الشخصية" : "Personal Data Actions"}
                        </h3>
                        <p className="text-xs text-gray-500 mt-1">
                          {isRTL ? "اختر الإجراء المطلوب" : "Select the desired action"}
                        </p>
                      </div>

                      <div className="py-2">
                        {/* Classification Section */}
                        {viewMode === 'classification' && (
                          <>
                            <div className="px-4 py-2">
                              <h4 className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                                {isRTL ? "تصنيف البيانات الشخصية" : "Personal Data Classification"}
                              </h4>
                            </div>

                            {/* Current Page Classification */}
                            <button
                              onClick={() => {
                                handlePersonalDataClassification();
                                setIsActionsDropdownOpen(false);
                              }}
                              disabled={isClassifyingPage || currentData.length === 0}
                              className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <div className="w-8 h-8 bg-blue-50 rounded-md flex items-center justify-center">
                                {isClassifyingPage ? (
                                  <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
                                ) : (
                                  <Brain className="w-4 h-4 text-blue-600" />
                                )}
                              </div>
                              <div className="flex-1">
                                <div className="text-sm font-medium text-gray-900">
                                  {isRTL ? `تصنيف الصفحة ${currentPage}` : `Classify Current Page`}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {isRTL ? `${currentData.length} سجل` : `${currentData.length} records`}
                                </div>
                              </div>
                            </button>

                            {/* Auto Classification */}
                            <button
                              onClick={() => {
                                handleAutoPersonalDataClassification();
                                setIsActionsDropdownOpen(false);
                              }}
                              disabled={isClassifyingPage || filteredData.length === 0}
                              className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <div className="w-8 h-8 bg-purple-50 rounded-md flex items-center justify-center">
                                <Zap className="w-4 h-4 text-purple-600" />
                              </div>
                              <div className="flex-1">
                                <div className="text-sm font-medium text-gray-900">
                                  {isRTL ? "تصنيف تلقائي شامل" : "Auto Classify All Pages"}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {isRTL ? `${Math.ceil(filteredData.length / rowsPerPage)} صفحة` : `${Math.ceil(filteredData.length / rowsPerPage)} pages total`}
                                </div>
                              </div>
                            </button>

                            <div className="border-t border-gray-100 my-2"></div>
                          </>
                        )}

                        {/* Personal Data Management Section */}
                        {viewMode === 'editing' && selectedRows.size > 0 && (
                          <>
                            <div className="px-4 py-2">
                              <h4 className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                                {isRTL ? "إدارة البيانات المحددة" : "Selected Data Management"}
                              </h4>
                            </div>

                            {/* Mark as Audit Trail */}
                            <button
                              onClick={() => {
                                handleMarkAuditTrail("Yes");
                                setIsActionsDropdownOpen(false);
                              }}
                              disabled={isMarkingAuditTrail}
                              className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <div className="w-8 h-8 bg-purple-50 rounded-md flex items-center justify-center">
                                <CheckCircle className="w-4 h-4 text-purple-600" />
                              </div>
                              <div className="flex-1">
                                <div className="text-sm font-medium text-gray-900">
                                  {isRTL ? "تمييز كجدول مراجعة" : "Mark as Audit Trail"}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {isRTL ? `${selectedRows.size} عنصر محدد` : `${selectedRows.size} selected items`}
                                </div>
                              </div>
                            </button>

                            {/* Mark as Needs Review */}
                            <button
                              onClick={() => {
                                handleBulkMarkAsNeedsReview();
                                setIsActionsDropdownOpen(false);
                              }}
                              disabled={isMarkingAuditTrail}
                              className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <div className="w-8 h-8 bg-amber-50 rounded-md flex items-center justify-center">
                                <Eye className="w-4 h-4 text-amber-600" />
                              </div>
                              <div className="flex-1">
                                <div className="text-sm font-medium text-gray-900">
                                  {isRTL ? "يحتاج مراجعة" : "Mark as Needs Review"}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {isRTL ? `${selectedRows.size} عنصر محدد` : `${selectedRows.size} selected items`}
                                </div>
                              </div>
                            </button>

                            <div className="border-t border-gray-100 my-2"></div>
                          </>
                        )}

                        {/* Data Management Section */}
                        <div className="px-4 py-2">
                          <h4 className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                            {isRTL ? "إدارة البيانات" : "Data Management"}
                          </h4>
                        </div>

                        {/* Export Excel */}
                        <button
                          onClick={() => {
                            handleExcelExport();
                            setIsActionsDropdownOpen(false);
                          }}
                          disabled={personalDataOnly.length === 0}
                          className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <div className="w-8 h-8 bg-green-50 rounded-md flex items-center justify-center">
                            <Download className="w-4 h-4 text-green-600" />
                          </div>
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900">
                              {isRTL ? "تصدير إلى Excel" : "Export to Excel"}
                            </div>
                            <div className="text-xs text-gray-500">
                              {isRTL ? "تصدير البيانات الشخصية" : "Export personal data"}
                            </div>
                          </div>
                        </button>

                        {/* Import Data */}
                        <button
                          onClick={() => {
                            onImportClick();
                            setIsActionsDropdownOpen(false);
                          }}
                          className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150"
                        >
                          <div className="w-8 h-8 bg-blue-50 rounded-md flex items-center justify-center">
                            <Upload className="w-4 h-4 text-blue-600" />
                          </div>
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900">
                              {isRTL ? "استيراد البيانات" : "Import Data"}
                            </div>
                            <div className="text-xs text-gray-500">
                              {isRTL ? "إضافة بيانات جديدة" : "Add new data records"}
                            </div>
                          </div>
                        </button>

                        {/* Delete All */}
                        {onDeleteAll && data.length > 0 && (
                          <>
                            <div className="border-t border-gray-100 my-2"></div>
                            <div className="px-4 py-2">
                              <h4 className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                                {isRTL ? "إجراءات خطيرة" : "Danger Zone"}
                              </h4>
                            </div>
                            <button
                              onClick={() => {
                                onDeleteAll();
                                setIsActionsDropdownOpen(false);
                              }}
                              className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-red-50 transition-colors duration-150"
                            >
                              <div className="w-8 h-8 bg-red-50 rounded-md flex items-center justify-center">
                                <Trash2 className="w-4 h-4 text-red-600" />
                              </div>
                              <div className="flex-1">
                                <div className="text-sm font-medium text-red-900">
                                  {isRTL ? "حذف جميع البيانات" : "Delete All Data"}
                                </div>
                                <div className="text-xs text-red-500">
                                  {isRTL ? "إجراء لا يمكن التراجع عنه" : "This action cannot be undone"}
                                </div>
                              </div>
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                /* Classification Active Controls */
                <div className="flex items-center gap-3">
                  {/* Professional Progress Indicator */}
                  <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                      <span className="text-white text-sm font-medium">
                        {isAutoClassifying
                          ? (isRTL ? "تصنيف تلقائي" : "Auto Classification")
                          : (isRTL ? "تصنيف الصفحة" : "Page Classification")
                        }
                      </span>
                    </div>
                    {isAutoClassifying && (
                      <>
                        <div className="h-4 w-px bg-white/30"></div>
                        <div className="text-white text-sm">
                          <span className="font-mono">{autoClassifyProgress.current}</span>
                          <span className="text-white/70 mx-1">/</span>
                          <span className="font-mono">{autoClassifyProgress.total}</span>
                        </div>
                      </>
                    )}
                  </div>

                  {/* Control Buttons - Only for Auto Classification */}
                  {isAutoClassifying && (
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={toggleAutoClassificationPause}
                        disabled={isClassifyingPage}
                        className="bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 disabled:opacity-50 transition-all duration-200"
                        size="sm"
                      >
                        {autoClassifyPaused ? (
                          <Play className="w-4 h-4" />
                        ) : (
                          <Pause className="w-4 h-4" />
                        )}
                      </Button>

                      <Button
                        onClick={stopAutoClassification}
                        disabled={isClassifyingPage}
                        className="bg-red-500/20 backdrop-blur-sm border border-red-300/50 text-white hover:bg-red-500/30 disabled:opacity-50 transition-all duration-200"
                        size="sm"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  )}
                </div>
              )}

              {/* Click outside to close dropdown */}
              {isActionsDropdownOpen && (
                <div
                  className="fixed inset-0 z-40"
                  onClick={() => {
                    setIsActionsDropdownOpen(false);
                    // Show professional toast if classification is in progress
                    if (isAutoClassifying || isClassifyingPage) {
                      toast({
                        title: isRTL ? "العملية قيد التنفيذ" : "Operation in Progress",
                        description: isRTL ?
                          "يتم تنفيذ عملية تصنيف البيانات الشخصية في الخلفية. يمكنك متابعة العمل أو مراقبة التقدم من شريط الحالة." :
                          "Personal data classification is running in the background. You can continue working or monitor progress from the status bar.",
                        duration: 4000,
                      });
                    }
                  }}
                />
              )}
            </div>
          </div>
        </div>

        {/* View Mode Toggle */}
        <div className="flex justify-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-1 border border-white/20">
            <div className="flex gap-1">
              <button
                onClick={() => handleViewModeChange('classification')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 ${
                  viewMode === 'classification'
                    ? 'bg-white text-[var(--brand-blue)] shadow-md'
                    : 'text-white/80 hover:text-white hover:bg-white/10'
                }`}
              >
                <Eye className="w-4 h-4" />
                <span className="font-medium">
                  {isRTL ? "عرض التصنيف" : "Classification View"}
                </span>
                <span className="text-xs opacity-75">
                  ({isRTL ? "50 صف" : "50 rows"})
                </span>
              </button>
              <button
                onClick={() => handleViewModeChange('editing')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 ${
                  viewMode === 'editing'
                    ? 'bg-white text-[var(--brand-blue)] shadow-md'
                    : 'text-white/80 hover:text-white hover:bg-white/10'
                }`}
              >
                <Edit className="w-4 h-4" />
                <span className="font-medium">
                  {isRTL ? "عرض التحرير" : "Editing View"}
                </span>
                <span className="text-xs opacity-75">
                  ({isRTL ? "50 صف" : "50 rows"})
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Actions */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center gap-4 mb-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={isRTL ? "البحث في البيانات الشخصية..." : "Search personal data..."}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1);
              }}
            />
          </div>
          
          <div className="text-sm text-gray-600">
            {isRTL
              ? `عرض ${startIndex + 1}-${Math.min(endIndex, filteredData.length)} من ${filteredData.length}`
              : `Showing ${startIndex + 1}-${Math.min(endIndex, filteredData.length)} of ${filteredData.length}`
            }
          </div>
        </div>

        {/* Advanced Filters - Only in editing view */}
        {viewMode === 'editing' && (
          <div className="flex items-center gap-4 mb-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                {isRTL ? "المرشحات:" : "Filters:"}
              </span>
            </div>
            
            {/* Table Filter */}
            <div className="flex-1 max-w-xs">
              <select
                value={tableFilter}
                onChange={(e) => {
                  setTableFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
              >
                <option value="">{isRTL ? "جميع الجداول" : "All Tables"}</option>
                {uniqueTables.map((table, index) => (
                  <option key={table || `table-${index}`} value={table}>{table}</option>
                ))}
              </select>
            </div>

            {/* Data Type Filter */}
            <div className="flex-1 max-w-xs">
              <select
                value={dataTypeFilter}
                onChange={(e) => {
                  setDataTypeFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
              >
                <option value="">{isRTL ? "جميع أنواع البيانات" : "All Data Types"}</option>
                {uniqueDataTypes.map((type, index) => (
                  <option key={type || `datatype-${index}`} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Confidentiality Filter */}
            <div className="flex-1 max-w-xs">
              <select
                value={confidentialityFilter}
                onChange={(e) => {
                  setConfidentialityFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
              >
                <option value="">{isRTL ? "جميع مستويات السرية" : "All Confidentiality Levels"}</option>
                {uniqueConfidentialityLevels.map((level, index) => (
                  <option key={level || `confidentiality-${index}`} value={level}>
                    {isRTL ? (
                      level === 'Public' ? 'عام' :
                      level === 'Confidential' ? 'سري' :
                      level === 'Secret' ? 'سري جداً' : 'سري للغاية'
                    ) : level}
                  </option>
                ))}
              </select>
            </div>

            {/* Review Status Filter */}
            <div className="flex-1 max-w-xs">
              <select
                value={reviewStatusFilter}
                onChange={(e) => {
                  setReviewStatusFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
              >
                <option value="">{isRTL ? "جميع حالات المراجعة" : "All Review Status"}</option>
                <option value="reviewed">{isRTL ? "مراجع" : "Reviewed"}</option>
                <option value="unreviewed">{isRTL ? "غير مراجع" : "Not Reviewed"}</option>
              </select>
            </div>

            {/* Personal Data Type Filter */}
            <div className="flex-1 max-w-xs">
              <select
                value={personalDataTypeFilter}
                onChange={(e) => {
                  setPersonalDataTypeFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
              >
                <option value="">{isRTL ? "جميع الأنواع" : "All Types"}</option>
                {uniquePersonalDataTypes.map((type, index) => (
                  <option key={type || `personaldata-${index}`} value={type}>
                    {type === 'direct' ? (isRTL ? "مباشر" : "Direct") :
                     type === 'indirect' ? (isRTL ? "غير مباشر" : "Indirect") :
                     type === 'pseudonymous' ? (isRTL ? "مستعار" : "Pseudonymous") :
                     type === 'anonymous' ? (isRTL ? "مجهول" : "Anonymous") :
                     type}
                  </option>
                ))}
              </select>
            </div>

            {/* Special Category Filter */}
            <div className="flex-1 max-w-xs">
              <select
                value={specialCategoryFilter}
                onChange={(e) => {
                  setSpecialCategoryFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
              >
                <option value="">{isRTL ? "جميع الفئات" : "All Categories"}</option>
                {uniqueSpecialCategories.map((category, index) => (
                  <option key={category || `category-${index}`} value={category}>{category}</option>
                ))}
              </select>
            </div>

            {/* Audit Trail Filter */}
            <div className="flex-1 max-w-xs">
              <select
                value={auditTrailFilter}
                onChange={(e) => {
                  setAuditTrailFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
              >
                <option value="">{isRTL ? "جميع حالات المراجعة" : "All Audit Trail Status"}</option>
                <option value="yes">{isRTL ? "جدول مراجعة" : "Audit Trail"}</option>
                <option value="no">{isRTL ? "ليس جدول مراجعة" : "Not Audit Trail"}</option>
                <option value="not_classified">{isRTL ? "غير مصنف" : "Not Classified"}</option>
              </select>
            </div>

            {/* Needs Review Filter */}
            <div className="flex-1 max-w-xs">
              <select
                value={needsReviewFilter}
                onChange={(e) => {
                  setNeedsReviewFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
              >
                <option value="">{isRTL ? "جميع حالات المراجعة المطلوبة" : "All Needs Review Status"}</option>
                <option value="yes">{isRTL ? "يحتاج مراجعة" : "Needs Review"}</option>
                <option value="no">{isRTL ? "لا يحتاج مراجعة" : "No Review Needed"}</option>
              </select>
            </div>

            {/* Clear Filters */}
            <Button
              onClick={() => {
                setTableFilter("");
                setDataTypeFilter("");
                setConfidentialityFilter("");
                setReviewStatusFilter("");
                setReviewerFilter("");
                setPersonalDataTypeFilter("");
                setSpecialCategoryFilter("");
                setAuditTrailFilter("");
                setNeedsReviewFilter("");
                setCurrentPage(1);
              }}
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              <X className="w-3 h-3" />
              {isRTL ? "مسح" : "Clear"}
            </Button>
          </div>
        )}
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full text-xs">
          <thead className="bg-gray-50">
            <tr>
              {/* Checkbox column - Only in editing view */}
              {viewMode === 'editing' && (
                <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700 w-12">
                  <input
                    type="checkbox"
                    checked={isAllSelected}
                    ref={(el) => {
                      if (el) el.indeterminate = isIndeterminate;
                    }}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="w-4 h-4 text-[var(--brand-blue)] bg-gray-100 border-gray-300 rounded focus:ring-[var(--brand-blue)] focus:ring-2"
                  />
                </th>
              )}
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "اسم المخطط" : "Schema"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "اسم الجدول" : "Table"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "اسم العمود" : "Column"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "نوع البيانات" : "Data Type"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "مستوى السرية" : "Confidentiality"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "سبب السرية" : "Conf. Reason"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "سبب البيانات الشخصية" : "Personal Reason"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "نوع البيانات الشخصية" : "Personal Data Type"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "يمكن إخفاء الهوية" : "Can be pseudonymized"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "يمكن إجراء إخفاء كامل للهوية" : "Can be anonymized"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "الفئة الخاصة" : "Special Category"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "جدول المراجعة" : "Audit Trail"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "يحتاج مراجعة" : "Needs Review"}
              </th>
              <th className="px-3 py-3 text-left text-xs font-semibold text-gray-700">
                {isRTL ? "الإجراءات" : "Actions"}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {currentData.map((row, index) => (
              <motion.tr
                key={row.id || `row-${row.schemaName || 'no-schema'}-${row.tableName}-${row.columnName}-${row.dataType}-${index}`}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="hover:bg-gray-50 transition-colors"
              >
                {/* Checkbox column - Only in editing view */}
                {viewMode === 'editing' && (
                  <td className="px-3 py-2 text-xs w-12">
                    <input
                      type="checkbox"
                      checked={row.id ? selectedRows.has(row.id) : false}
                      onChange={(e) => row.id && handleRowSelection(row.id, e.target.checked)}
                      disabled={!row.id}
                      className="w-4 h-4 text-[var(--brand-blue)] bg-gray-100 border-gray-300 rounded focus:ring-[var(--brand-blue)] focus:ring-2"
                    />
                  </td>
                )}
                <td className="px-3 py-2 text-xs text-gray-600">
                  {row.schemaName || '-'}
                </td>
                <td className="px-3 py-2 text-xs font-medium text-gray-900">
                  {row.tableName}
                </td>
                <td className="px-3 py-2 text-xs text-gray-900">
                  {row.columnName}
                </td>
                <td className="px-3 py-2 text-xs">
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium bg-[var(--brand-blue)]/10 text-[var(--brand-blue)]">
                    {row.dataType}
                  </span>
                </td>
                <td className="px-3 py-2 text-xs">
                  {row.confidentialityLevel ? (
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium ${
                      row.confidentialityLevel === 'Public' ? 'bg-green-100 text-green-800' :
                      row.confidentialityLevel === 'Confidential' ? 'bg-yellow-100 text-yellow-800' :
                      row.confidentialityLevel === 'Secret' ? 'bg-orange-100 text-orange-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {isRTL ? (
                        row.confidentialityLevel === 'Public' ? 'عام' :
                        row.confidentialityLevel === 'Confidential' ? 'سري' :
                        row.confidentialityLevel === 'Secret' ? 'سري جداً' : 'سري للغاية'
                      ) : row.confidentialityLevel}
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {row.confidentialityReasoning ? (
                    <div
                      className="text-[10px] text-gray-700 max-w-[120px] cursor-pointer hover:text-gray-900 truncate"
                      title={row.confidentialityReasoning}
                    >
                      {row.confidentialityReasoning.length > 30
                        ? `${row.confidentialityReasoning.substring(0, 30)}...`
                        : row.confidentialityReasoning
                      }
                    </div>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {row.personalDataReasoning ? (
                    <div
                      className="text-[10px] text-gray-700 max-w-[120px] cursor-pointer hover:text-gray-900 truncate"
                      title={row.personalDataReasoning}
                    >
                      {row.personalDataReasoning.length > 30
                        ? `${row.personalDataReasoning.substring(0, 30)}...`
                        : row.personalDataReasoning
                      }
                    </div>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {row.personalDataType ? (
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium ${
                      row.personalDataType === 'direct'
                        ? 'bg-blue-100 text-blue-800'
                        : row.personalDataType === 'indirect'
                        ? 'bg-purple-100 text-purple-800'
                        : row.personalDataType === 'pseudonymous'
                        ? 'bg-orange-100 text-orange-800'
                        : row.personalDataType === 'anonymous'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {row.personalDataType === 'direct'
                        ? (isRTL ? "مباشر" : "Direct")
                        : row.personalDataType === 'indirect'
                        ? (isRTL ? "غير مباشر" : "Indirect")
                        : row.personalDataType === 'pseudonymous'
                        ? (isRTL ? "مستعار" : "Pseudonymous")
                        : row.personalDataType === 'anonymous'
                        ? (isRTL ? "مجهول" : "Anonymous")
                        : (isRTL ? "غير محدد" : "Not Set")
                      }
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {(row as ExtendedPersonalData & { pseudonyms?: string }).pseudonyms ? (
                    <span
                      className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium cursor-help ${
                        (row as ExtendedPersonalData & { pseudonyms?: string }).pseudonyms === 'Yes' ? 'bg-orange-100 text-orange-800' : 'bg-gray-100 text-gray-800'
                      }`}
                      title={
                        (row as ExtendedPersonalData & { pseudonymsJustification?: string }).pseudonymsJustification ||
                        (isRTL
                          ? `تحليل إمكانية إخفاء الهوية لـ ${row.columnName}`
                          : `Pseudonymization analysis for ${row.columnName}`)
                      }
                    >
                      {(row as ExtendedPersonalData & { pseudonyms?: string }).pseudonyms === 'Yes' ? (isRTL ? "نعم" : "Yes") : (isRTL ? "لا" : "No")}
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {(row as ExtendedPersonalData & { anonyms?: string }).anonyms ? (
                    <span
                      className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium cursor-help ${
                        (row as ExtendedPersonalData & { anonyms?: string }).anonyms === 'Yes' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}
                      title={
                        (row as ExtendedPersonalData & { anonymsJustification?: string }).anonymsJustification ||
                        (isRTL
                          ? `تحليل إمكانية الإخفاء الكامل للهوية لـ ${row.columnName}`
                          : `Anonymization analysis for ${row.columnName}`)
                      }
                    >
                      {(row as ExtendedPersonalData & { anonyms?: string }).anonyms === 'Yes' ? (isRTL ? "نعم" : "Yes") : (isRTL ? "لا" : "No")}
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {row.specialCategoryType && row.specialCategoryType !== 'none' ? (
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium ${
                      row.specialCategoryType === 'PII' ? 'bg-green-100 text-green-800' :
                      row.specialCategoryType === 'PHI' ? 'bg-red-100 text-red-800' :
                      row.specialCategoryType === 'PCI' ? 'bg-yellow-100 text-yellow-800' :
                      row.specialCategoryType === 'Genetic' ? 'bg-pink-100 text-pink-800' :
                      row.specialCategoryType === 'Biometric' ? 'bg-indigo-100 text-indigo-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {row.specialCategoryType}
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {row.auditTrail ? (
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium ${
                      row.auditTrail === 'Yes' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {row.auditTrail === 'Yes' ? (isRTL ? "نعم" : "Yes") : (isRTL ? "لا" : "No")}
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  {row.needsReview ? (
                    <div className="flex flex-col items-start gap-1">
                      <button
                        onClick={() => {
                          setChangesViewRow(row);
                          setIsChangesModalOpen(true);
                        }}
                        className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium bg-orange-100 text-orange-800 hover:bg-orange-200 cursor-pointer transition-colors"
                      >
                        {isRTL ? "يحتاج مراجعة" : "Needs Review"}
                      </button>
                      {/* Show change indicators if any specification level changed */}
                      {(row.personalDataTypeChanged || row.specialCategoryTypeChanged) && (
                        <div className="text-[9px] text-blue-600 cursor-help"
                             title={`${isRTL ? 'تم تغيير مستوى التصنيف' : 'Specification level changed'}`}>
                          {isRTL ? "تم التغيير" : "Changed"}
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium bg-green-100 text-green-800">
                      {isRTL ? "لا يحتاج مراجعة" : "No Review Needed"}
                    </span>
                  )}
                </td>
                <td className="px-3 py-2 text-xs">
                  <Button
                    onClick={() => {
                      setEditingRow(row);
                      setEditingData(row);
                      setIsEditModalOpen(true);
                    }}
                    variant="outline"
                    size="sm"
                    className="text-[10px] px-2 py-1 h-6"
                  >
                    <Edit className="w-3 h-3 mr-1" />
                    {isRTL ? "تحرير" : "Edit"}
                  </Button>
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {isRTL
                ? `صفحة ${currentPage} من ${totalPages}`
                : `Page ${currentPage} of ${totalPages}`
              }
            </div>
            <div className="flex items-center gap-3">
              {/* Page Navigation Input */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {isRTL ? "الانتقال إلى:" : "Go to:"}
                </span>
                <input
                  type="number"
                  min="1"
                  max={totalPages}
                  value={pageInput}
                  onChange={(e) => setPageInput(e.target.value)}
                  onKeyDown={handlePageInputKeyDown}
                  placeholder={isRTL ? "رقم الصفحة" : "Page #"}
                  className="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none text-center"
                />
                <Button
                  onClick={() => handlePageNavigation(pageInput)}
                  disabled={!pageInput.trim()}
                  variant="outline"
                  size="sm"
                  className="px-3"
                >
                  {isRTL ? "انتقال" : "Go"}
                </Button>
              </div>

              {/* Previous/Next Buttons */}
              <div className="flex gap-2">
                <Button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  variant="outline"
                  size="sm"
                >
                  {isRTL ? "السابق" : "Previous"}
                </Button>
                <Button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  variant="outline"
                  size="sm"
                >
                  {isRTL ? "التالي" : "Next"}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      <AnimatePresence>
        {isEditModalOpen && editingRow && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-900/20 backdrop-blur-sm"
            onClick={() => setIsEditModalOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.95, opacity: 0, y: 20 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-3xl shadow-2xl border border-gray-100 w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col"
            >
              {/* Modal Header */}
              <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 px-8 py-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center">
                      <User className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-white">
                        {isRTL ? "تحرير البيانات الشخصية" : "Edit Personal Data"}
                      </h3>
                      <p className="text-white/80">
                        {editingRow.tableName}.{editingRow.columnName}
                      </p>
                    </div>
                  </div>
                  <Button
                    onClick={() => setIsEditModalOpen(false)}
                    variant="outline"
                    size="sm"
                    className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-8 space-y-8 flex-1 overflow-y-auto">
                {/* Field Information */}
                <div className="bg-gray-50 rounded-2xl p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <User className="w-5 h-5 text-[var(--brand-blue)]" />
                    {isRTL ? "معلومات البيانات الشخصية" : "Personal Data Information"}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">{isRTL ? "اسم الجدول" : "Table Name"}</label>
                      <p className="text-lg font-semibold text-gray-900">{editingRow.tableName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">{isRTL ? "اسم العمود" : "Column Name"}</label>
                      <p className="text-lg font-semibold text-gray-900">{editingRow.columnName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">{isRTL ? "نوع البيانات" : "Data Type"}</label>
                      <p className="text-lg font-semibold text-gray-900">{editingRow.dataType}</p>
                    </div>
                  </div>
                </div>

                {/* Classification Form */}
                <div className="space-y-6">
                  {/* Confidentiality Level */}
                  <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl p-6 border border-orange-100">
                    <label className="block text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Eye className="w-4 h-4 text-orange-600" />
                      </div>
                      {isRTL ? "مستوى السرية" : "Confidentiality Level"}
                    </label>
                    <select
                      value={editingData.confidentialityLevel || ''}
                      onChange={(e) => setEditingData(prev => ({ 
                        ...prev, 
                        confidentialityLevel: e.target.value as "Public" | "Confidential" | "Secret" | "Top Secret" || undefined
                      }))}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none text-lg"
                    >
                      <option value="">{isRTL ? "اختر مستوى السرية" : "Select Confidentiality Level"}</option>
                      <option value="Public">{isRTL ? "عام" : "Public"}</option>
                      <option value="Confidential">{isRTL ? "سري" : "Confidential"}</option>
                      <option value="Secret">{isRTL ? "سري جداً" : "Secret"}</option>
                      <option value="Top Secret">{isRTL ? "سري للغاية" : "Top Secret"}</option>
                    </select>
                  </div>

                  {/* Confidentiality Reasoning */}
                  <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl p-6 border border-orange-100">
                    <label className="block text-lg font-semibold text-gray-900 mb-3">
                      {isRTL ? "سبب تصنيف السرية" : "Confidentiality Reasoning"}
                    </label>
                    <textarea
                      value={editingData.confidentialityReasoning || ''}
                      onChange={(e) => setEditingData(prev => ({ 
                        ...prev, 
                        confidentialityReasoning: e.target.value 
                      }))}
                      placeholder={isRTL ? "اشرح سبب تصنيف هذا الحقل بهذا المستوى من السرية..." : "Explain why this field is classified at this confidentiality level..."}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none resize-none"
                      rows={4}
                    />
                  </div>

                  {/* Personal Data Reasoning */}
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                    <label className="block text-lg font-semibold text-gray-900 mb-3">
                      {isRTL ? "سبب تصنيف البيانات الشخصية" : "Personal Data Reasoning"}
                    </label>
                    <textarea
                      value={editingData.personalDataReasoning || ''}
                      onChange={(e) => setEditingData(prev => ({ 
                        ...prev, 
                        personalDataReasoning: e.target.value 
                      }))}
                      placeholder={isRTL ? "اشرح سبب اعتبار هذا الحقل يحتوي على بيانات شخصية..." : "Explain why this field contains personal data..."}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none resize-none"
                      rows={4}
                    />
                  </div>

                  {/* Personal Data Type */}
                  <div className="bg-gradient-to-r from-cyan-50 to-blue-50 rounded-2xl p-6 border border-cyan-100">
                    <label className="block text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <div className="w-8 h-8 bg-cyan-100 rounded-lg flex items-center justify-center">
                        <User className="w-4 h-4 text-cyan-600" />
                      </div>
                      {isRTL ? "نوع البيانات الشخصية" : "Personal Data Type"}
                    </label>
                    <select
                      value={editingData.personalDataType || ''}
                      onChange={(e) => setEditingData(prev => ({
                        ...prev,
                        personalDataType: e.target.value as "direct" | "indirect" | "pseudonymous" | "anonymous" || undefined
                      }))}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none text-lg"
                    >
                      <option value="">{isRTL ? "اختر نوع البيانات الشخصية" : "Select Personal Data Type"}</option>
                      <option value="direct">{isRTL ? "مباشر - يمكن تحديد الهوية مباشرة" : "Direct - Can directly identify a person"}</option>
                      <option value="indirect">{isRTL ? "غير مباشر - يحتاج لدمجه مع بيانات أخرى" : "Indirect - Requires combination with other data"}</option>
                      <option value="pseudonymous">{isRTL ? "مستعار - تم استبدال المعرفات بمعرفات مستعارة" : "Pseudonymous - Identifiers replaced with pseudonyms"}</option>
                      <option value="anonymous">{isRTL ? "مجهول - تم إزالة جميع المعرفات نهائياً" : "Anonymous - All identifiers permanently removed"}</option>
                    </select>
                    <p className="text-sm text-gray-600 mt-2">
                      {isRTL ?
                        "المباشر: الاسم، الهوية الوطنية، البريد الإلكتروني. غير المباشر: عنوان IP، معرف الجهاز، تاريخ الميلاد" :
                        "Direct: Name, National ID, Email. Indirect: IP address, Device ID, Date of Birth"
                      }
                    </p>
                  </div>

                  {/* Can be Pseudonymized */}
                  <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-2xl p-6 border border-orange-100">
                    <label className="block text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Eye className="w-4 h-4 text-orange-600" />
                      </div>
                      {isRTL ? "يمكن إخفاء الهوية" : "Can be Pseudonymized"}
                    </label>
                    <select
                      value={editingData.pseudonyms || ''}
                      onChange={(e) => setEditingData(prev => ({
                        ...prev,
                        pseudonyms: e.target.value
                      }))}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none text-lg"
                    >
                      <option value="">{isRTL ? "اختر إمكانية إخفاء الهوية" : "Select Pseudonymization Capability"}</option>
                      <option value="Yes">{isRTL ? "نعم - يمكن إخفاء الهوية" : "Yes - Can be pseudonymized"}</option>
                      <option value="No">{isRTL ? "لا - لا يمكن إخفاء الهوية" : "No - Cannot be pseudonymized"}</option>
                    </select>
                    <p className="text-sm text-gray-600 mt-2">
                      {isRTL ?
                        "إخفاء الهوية يعني استبدال البيانات الشخصية برموز أو معرفات مشفرة مع الحفاظ على إمكانية الربط" :
                        "Pseudonymization means replacing personal data with tokens or encrypted identifiers while maintaining linkability"
                      }
                    </p>
                  </div>

                  {/* Can be Anonymized */}
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-100">
                    <label className="block text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <User className="w-4 h-4 text-green-600" />
                      </div>
                      {isRTL ? "يمكن إجراء إخفاء كامل للهوية" : "Can be Anonymized"}
                    </label>
                    <select
                      value={editingData.anonyms || ''}
                      onChange={(e) => setEditingData(prev => ({
                        ...prev,
                        anonyms: e.target.value
                      }))}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none text-lg"
                    >
                      <option value="">{isRTL ? "اختر إمكانية الإخفاء الكامل" : "Select Anonymization Capability"}</option>
                      <option value="Yes">{isRTL ? "نعم - يمكن الإخفاء الكامل" : "Yes - Can be anonymized"}</option>
                      <option value="No">{isRTL ? "لا - لا يمكن الإخفاء الكامل" : "No - Cannot be anonymized"}</option>
                    </select>
                    <p className="text-sm text-gray-600 mt-2">
                      {isRTL ?
                        "الإخفاء الكامل يعني إزالة جميع المعرفات الشخصية بحيث لا يمكن ربط البيانات بأشخاص محددين" :
                        "Anonymization means removing all personal identifiers so data cannot be linked to specific individuals"
                      }
                    </p>
                  </div>

                  {/* Special Category Type */}
                  <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-2xl p-6 border border-amber-100">
                    <label className="block text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <div className="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center">
                        <FileSpreadsheet className="w-4 h-4 text-amber-600" />
                      </div>
                      {isRTL ? "الفئة الخاصة للبيانات" : "Special Category Type"}
                    </label>
                    <select
                      value={editingData.specialCategoryType || ''}
                      onChange={(e) => setEditingData(prev => ({ 
                        ...prev, 
                        specialCategoryType: e.target.value as "PII" | "PHI" | "PCI" | "Genetic" | "Biometric" | "none" || undefined
                      }))}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none text-lg"
                    >
                      <option value="">{isRTL ? "اختر الفئة الخاصة" : "Select Special Category"}</option>
                      <option value="PII">{isRTL ? "معلومات شخصية عامة (PII)" : "Personal Identifiable Information (PII)"}</option>
                      <option value="PHI">{isRTL ? "معلومات صحية محمية (PHI)" : "Protected Health Information (PHI)"}</option>
                      <option value="PCI">{isRTL ? "بيانات صناعة بطاقات الدفع (PCI)" : "Payment Card Industry Data (PCI)"}</option>
                      <option value="Genetic">{isRTL ? "معلومات وراثية" : "Genetic Information"}</option>
                      <option value="Biometric">{isRTL ? "بيانات بيومترية" : "Biometric Data"}</option>
                      <option value="none">{isRTL ? "لا توجد فئة خاصة" : "No Special Category"}</option>
                    </select>
                    <div className="text-sm text-gray-600 mt-2 space-y-1">
                      <p><strong>PII:</strong> {isRTL ? "الأسماء، العناوين، أرقام الهوية" : "Names, addresses, ID numbers"}</p>
                      <p><strong>PHI:</strong> {isRTL ? "السجلات الطبية، معلومات التأمين الصحي" : "Medical records, health insurance info"}</p>
                      <p><strong>PCI:</strong> {isRTL ? "أرقام بطاقات الائتمان، البيانات المصرفية" : "Credit card numbers, banking data"}</p>
                      <p><strong>Genetic:</strong> {isRTL ? "التسلسل الجيني، التاريخ الطبي العائلي" : "DNA sequences, family medical history"}</p>
                      <p><strong>Biometric:</strong> {isRTL ? "بصمات الأصابع، التعرف على الوجه" : "Fingerprints, facial recognition"}</p>
                    </div>
                  </div>
                </div>
              </div>



              {/* Modal Footer */}
              <div className="px-8 py-6 bg-gray-50 border-t border-gray-100 flex justify-end gap-3 flex-shrink-0">
                <Button
                  onClick={() => setIsEditModalOpen(false)}
                  variant="outline"
                  className="px-6 py-3"
                  disabled={isSaving}
                >
                  {isRTL ? "إلغاء" : "Cancel"}
                </Button>
                <Button
                  onClick={handleSaveEdit}
                  disabled={isSaving}
                  className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white px-6 py-3 flex items-center gap-2"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      {isRTL ? "جاري الحفظ..." : "Saving..."}
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      {isRTL ? "حفظ التغييرات" : "Save Changes"}
                    </>
                  )}
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}

        {/* Changes Review Modal */}
        <AnimatePresence>
          {isChangesModalOpen && changesViewRow && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-900/20 backdrop-blur-sm"
              onClick={() => setIsChangesModalOpen(false)}
            >
              <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.95, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
                className="bg-white rounded-3xl shadow-2xl border border-gray-100 w-full max-w-3xl max-h-[90vh] overflow-hidden"
              >
                {/* Modal Header */}
                <div className="bg-gradient-to-r from-orange-500 to-amber-500 px-8 py-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center">
                        <Eye className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-white">
                          {isRTL ? "مراجعة التغييرات" : "Review Changes"}
                        </h3>
                        <p className="text-white/80">
                          {changesViewRow.tableName}.{changesViewRow.columnName}
                        </p>
                      </div>
                    </div>
                    <Button
                      onClick={() => setIsChangesModalOpen(false)}
                      variant="outline"
                      size="sm"
                      className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Modal Content */}
                <div className="p-8 max-h-[60vh] overflow-y-auto">
                  <div className="space-y-6">
                    {/* Field Information */}
                    <div className="bg-gray-50 rounded-2xl p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                        <FileSpreadsheet className="w-5 h-5 text-gray-600" />
                        {isRTL ? "معلومات الحقل" : "Field Information"}
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-600">{isRTL ? "اسم الجدول" : "Table Name"}</label>
                          <p className="text-lg font-semibold text-gray-900">{changesViewRow.tableName}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">{isRTL ? "اسم العمود" : "Column Name"}</label>
                          <p className="text-lg font-semibold text-gray-900">{changesViewRow.columnName}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">{isRTL ? "نوع البيانات" : "Data Type"}</label>
                          <p className="text-lg font-semibold text-gray-900">{changesViewRow.dataType}</p>
                        </div>
                      </div>
                    </div>

                    {/* Changes Summary */}
                    <div className="space-y-4">
                      <h4 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <Eye className="w-5 h-5 text-orange-600" />
                        {isRTL ? "ملخص التغييرات" : "Changes Summary"}
                      </h4>

                      {/* Personal Data Type Changes */}
                      {changesViewRow.personalDataTypeChanged && (
                        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                          <h5 className="font-semibold text-blue-900 mb-2">
                            {isRTL ? "تغيير نوع البيانات الشخصية" : "Personal Data Type Change"}
                          </h5>
                          <div className="flex items-center gap-4">
                            <div className="flex-1">
                              <label className="text-sm text-blue-700">{isRTL ? "القيمة السابقة" : "Previous Value"}</label>
                              <div className="bg-red-100 border border-red-300 rounded-lg px-3 py-2 text-red-800 font-medium">
                                {changesViewRow.personalDataTypeOldValue || (isRTL ? "غير محدد" : "Not Set")}
                              </div>
                            </div>
                            <div className="text-2xl text-blue-600">→</div>
                            <div className="flex-1">
                              <label className="text-sm text-blue-700">{isRTL ? "القيمة الجديدة" : "New Value"}</label>
                              <div className="bg-green-100 border border-green-300 rounded-lg px-3 py-2 text-green-800 font-medium">
                                {changesViewRow.personalDataTypeNewValue || (isRTL ? "غير محدد" : "Not Set")}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Special Category Type Changes */}
                      {changesViewRow.specialCategoryTypeChanged && (
                        <div className="bg-purple-50 border border-purple-200 rounded-xl p-4">
                          <h5 className="font-semibold text-purple-900 mb-2">
                            {isRTL ? "تغيير نوع الفئة الخاصة" : "Special Category Type Change"}
                          </h5>
                          <div className="flex items-center gap-4">
                            <div className="flex-1">
                              <label className="text-sm text-purple-700">{isRTL ? "القيمة السابقة" : "Previous Value"}</label>
                              <div className="bg-red-100 border border-red-300 rounded-lg px-3 py-2 text-red-800 font-medium">
                                {changesViewRow.specialCategoryTypeOldValue || (isRTL ? "غير محدد" : "Not Set")}
                              </div>
                            </div>
                            <div className="text-2xl text-purple-600">→</div>
                            <div className="flex-1">
                              <label className="text-sm text-purple-700">{isRTL ? "القيمة الجديدة" : "New Value"}</label>
                              <div className="bg-green-100 border border-green-300 rounded-lg px-3 py-2 text-green-800 font-medium">
                                {changesViewRow.specialCategoryTypeNewValue || (isRTL ? "غير محدد" : "Not Set")}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* No Changes Message */}
                      {!changesViewRow.personalDataTypeChanged && !changesViewRow.specialCategoryTypeChanged && (
                        <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 text-center">
                          <p className="text-gray-600">
                            {isRTL ? "لم يتم تسجيل أي تغييرات في مستويات التصنيف" : "No specification level changes recorded"}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Modal Footer */}
                <div className="px-8 py-6 bg-gray-50 border-t border-gray-100 flex justify-end gap-3">
                  <Button
                    onClick={() => setIsChangesModalOpen(false)}
                    variant="outline"
                    className="px-6 py-3"
                  >
                    {isRTL ? "إغلاق" : "Close"}
                  </Button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </AnimatePresence>
    </motion.div>
  );
}