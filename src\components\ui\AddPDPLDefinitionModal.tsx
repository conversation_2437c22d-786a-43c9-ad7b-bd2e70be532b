"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Save, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { PDPLDocumentType } from "@/Firebase/firestore/services/PDPLService";
import { Locale } from "@/i18n-config";

interface AddPDPLDefinitionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (definitionData: {
    term: string;
    definition: string;
  }) => Promise<void>;
  lang: Locale;
  isLoading: boolean;
  documentType?: PDPLDocumentType; // Made optional since definitions are now shared
}

export function AddPDPLDefinitionModal({
  isOpen,
  onClose,
  onSubmit,
  lang,
  isLoading,
  documentType
}: AddPDPLDefinitionModalProps) {
  const isRTL = lang === "ar";
  
  const [formData, setFormData] = useState({
    term: "",
    definition: ""
  });

  const documentTypeLabels = {
    [PDPLDocumentType.PDPL_LAW]: {
      en: "Personal Data Protection Law",
      ar: "قانون حماية البيانات الشخصية"
    },
    [PDPLDocumentType.IMPLEMENTING_REGULATION]: {
      en: "Implementing Regulation",
      ar: "اللائحة التنفيذية"
    },
    [PDPLDocumentType.TRANSFER_RULES]: {
      en: "Data Transfer Outside Kingdom",
      ar: "نقل البيانات خارج المملكة"
    }
  };

  // Use shared definitions label when no specific document type
  const currentTypeInfo = documentType ? documentTypeLabels[documentType] : {
    en: "Shared Definitions - All Documents",
    ar: "التعريفات المشتركة - جميع الوثائق"
  };

  const handleClose = () => {
    setFormData({
      term: "",
      definition: ""
    });
    onClose();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.term.trim() || !formData.definition.trim()) {
      return;
    }

    try {
      await onSubmit({
        ...formData
        // documentType removed - definitions are now shared across all document types
      });
      
      // Only close if submission was successful
      handleClose();
    } catch (error) {
      // Don't close modal on error - let the parent handle error display
      console.error('Error submitting definition:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/20 backdrop-blur-sm"
          onClick={handleClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.3 }}
          className="relative bg-white rounded-3xl shadow-2xl border border-gray-200 w-full max-w-2xl"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 text-white p-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center">
                  <BookOpen className="w-6 h-6" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold">
                    {isRTL ? "إضافة تعريف مشترك" : "Add Shared Definition"}
                  </h2>
                  <p className="text-white/90 text-sm">
                    {isRTL ? currentTypeInfo.ar : currentTypeInfo.en}
                  </p>
                </div>
              </div>
              <Button
                onClick={handleClose}
                variant="ghost"
                size="icon"
                className="text-white hover:bg-white/20 rounded-xl"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Term */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  {isRTL ? "المصطلح" : "Term"}
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <input
                  type="text"
                  value={formData.term}
                  onChange={(e) => setFormData(prev => ({ ...prev, term: e.target.value }))}
                  placeholder={isRTL ? "اسم المصطلح" : "Term name"}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-[var(--brand-blue)] transition-all duration-200"
                  required
                />
              </div>

              {/* Definition */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  {isRTL ? "التعريف" : "Definition"}
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <textarea
                  value={formData.definition}
                  onChange={(e) => setFormData(prev => ({ ...prev, definition: e.target.value }))}
                  placeholder={isRTL ? "تعريف المصطلح" : "Definition of the term"}
                  rows={6}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-[var(--brand-blue)] transition-all duration-200 resize-none"
                  required
                />
              </div>
            </form>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-8 py-6 border-t border-gray-200 flex gap-4 justify-end">
            <Button
              type="button"
              onClick={handleClose}
              variant="outline"
              disabled={isLoading}
              className="px-6 py-3 rounded-xl"
            >
              {isRTL ? "إلغاء" : "Cancel"}
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isLoading || !formData.term.trim() || !formData.definition.trim()}
              className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  {isRTL ? "جاري الحفظ..." : "Saving..."}
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Save className="w-4 h-4" />
                  {isRTL ? "حفظ التعريف" : "Save Definition"}
                </div>
              )}
            </Button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
} 