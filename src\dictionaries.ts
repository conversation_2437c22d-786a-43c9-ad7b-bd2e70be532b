import "server-only";
import type { Locale } from "@/i18n-config"; // Use path alias
import { z } from "zod";

// Define the structure of your dictionary if needed
const DictionarySchema = z.object({
  page: z.object({
    getStarted: z.string(),
    saveChanges: z.string(),
    deployNow: z.string(),
    readDocs: z.string(),
    learn: z.string(),
    examples: z.string(),
    goToNextjs: z.string(),
  }),
  layout: z.object({
    title: z.string(),
    description: z.string(),
  }),
  auth: z.object({
    common: z.object({
      email: z.string(),
      password: z.string(),
      confirmPassword: z.string(),
      name: z.string(),
      forgotPassword: z.string(),
      rememberMe: z.string(),
      alreadyHaveAccount: z.string(),
      dontHaveAccount: z.string(),
      or: z.string(),
      continueWithGoogle: z.string(),
      passwordRequirements: z.string(),
      passwordMismatch: z.string(),
      invalidEmail: z.string(),
      requiredField: z.string(),
      signOut: z.string(),
    }),
    signin: z.object({
      title: z.string(),
      subtitle: z.string(),
      button: z.string(),
      success: z.string(),
      error: z.string(),
    }),

    reset: z.object({
      title: z.string(),
      subtitle: z.string(),
      button: z.string(),
      backToSignIn: z.string(),
      success: z.string(),
      error: z.string(),
    }),

  }),
  // Add other sections as needed
});

export type Dictionary = z.infer<typeof DictionarySchema>;

// We enumerate all dictionaries here for better linting and typescript support
// We also get the default import for cleaner types
const dictionaries: Record<Locale, () => Promise<unknown>> = {
  en: () => import("./dictionaries/en.json").then((module) => module.default),
  ar: () => import("./dictionaries/ar.json").then((module) => module.default),
};

function isObject(item: unknown): item is Record<string, unknown> {
  return item !== null && typeof item === "object" && !Array.isArray(item);
}

function deepMerge<T>(target: T, source: Partial<T>): T {
  if (!isObject(target) || !isObject(source)) return target;
  for (const key in source) {
    if (isObject(source[key]) && key in target && isObject(target[key])) {
      // @ts-expect-error: recursive merge
      target[key] = deepMerge(target[key], source[key]);
    } else if (source[key] !== undefined) {
      // @ts-expect-error: assignment
      target[key] = source[key];
    }
  }
  return target;
}

export const getDictionary = async (locale: Locale): Promise<Dictionary> => {
  const defaultDictRaw = await dictionaries.en();
  const dictRaw = (await dictionaries[locale]?.()) ?? defaultDictRaw;

  // Validate both dictionaries
  const defaultDict = DictionarySchema.parse(defaultDictRaw);
  let dict: Dictionary;
  try {
    dict = DictionarySchema.parse(dictRaw);
  } catch {
    // If validation fails, fallback to default
    dict = defaultDict;
  }

  // Merge with fallback for missing keys
  const merged = deepMerge(structuredClone(defaultDict), dict);
  return merged;
};