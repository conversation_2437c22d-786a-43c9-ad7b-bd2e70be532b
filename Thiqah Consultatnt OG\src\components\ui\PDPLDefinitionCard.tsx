"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { BookOpen, Calendar, Edit, Trash2, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Locale } from '@/i18n-config';
import { Timestamp } from 'firebase/firestore';

interface PDPLDefinitionCardProps {
  definition: {
    id?: string;
    term: string;
    definition: string;
    createdAt: Timestamp | Date | string;
    updatedAt: Timestamp | Date | string;
  };
  lang: Locale;
  onEdit?: (definitionId: string) => void;
  onDelete?: (definitionId: string) => void;
}

export function PDPLDefinitionCard({ definition, lang, onEdit, onDelete }: PDPLDefinitionCardProps) {
  const isRTL = lang === "ar";

  const formatDate = (timestamp: Timestamp | Date | string) => {
    if (!timestamp) return '';
    try {
      const date = (timestamp as Timestamp).toDate ? (timestamp as Timestamp).toDate() : new Date(timestamp as string);
      return date.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US');
    } catch {
      return '';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="group relative"
    >
      {/* Definition Card */}
      <div className="bg-gradient-to-br from-white via-blue-50/30 to-blue-100/20 rounded-2xl border border-blue-200/50 shadow-lg hover:shadow-xl transition-all duration-500 overflow-hidden">
        {/* Floating Badge */}
        <div className="absolute top-4 right-4 z-10">
          <div className="bg-[var(--brand-blue)] text-white px-3 py-1.5 rounded-full text-xs font-bold shadow-lg">
            {isRTL ? "تعريف مشترك" : "Shared Definition"}
          </div>
        </div>
        
        {/* Card Content */}
        <div className="p-6 relative">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-0 right-0 w-32 h-32 bg-[var(--brand-blue)] rounded-full -translate-y-1/2 translate-x-1/2"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-[var(--brand-blue)] rounded-full translate-y-1/2 -translate-x-1/2"></div>
          </div>

          <div className="relative z-10">
            {/* Term Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1 min-w-0">
                <h3 className="text-xl font-bold text-gray-900 mb-2 leading-tight group-hover:text-[var(--brand-blue)] transition-colors duration-300">
                  {definition.term}
                </h3>
                <div className="flex items-center gap-2 text-sm text-gray-600 mb-4">
                  <BookOpen className="w-4 h-4 text-[var(--brand-blue)]" />
                  <span className="font-medium">{isRTL ? "مشترك بين جميع الوثائق" : "Shared across all documents"}</span>
                </div>
              </div>
              
              {/* Admin Actions */}
              <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit?.(definition.id!)}
                  className="text-[var(--brand-blue)] hover:bg-blue-50 border border-blue-200/50"
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete?.(definition.id!)}
                  className="text-red-600 hover:bg-red-50 border border-red-200/50"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Definition Text */}
            <div className="bg-gradient-to-r from-blue-50 to-blue-50/50 rounded-xl p-4 border border-blue-200/50 mb-4">
              <p className="text-gray-800 leading-relaxed font-medium">
                {definition.definition}
              </p>
            </div>

            {/* Card Footer */}
            <div className="flex items-center justify-between text-xs text-gray-600">
              <div className="flex items-center gap-2">
                <Calendar className="w-3 h-3 text-[var(--brand-blue)]" />
                <span>{isRTL ? "تاريخ الإنشاء" : "Created"}: {formatDate(definition.createdAt)}</span>
              </div>
              <div className="flex items-center gap-2">
                <FileText className="w-3 h-3 text-[var(--brand-blue)]" />
                <span className="px-2 py-1 bg-blue-100 text-[var(--brand-blue)] rounded-full font-medium">
                  {isRTL ? "مصطلح مشترك" : "Shared Term"}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Elegant Hover Effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-[var(--brand-blue)]/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
      </div>
    </motion.div>
  );
} 