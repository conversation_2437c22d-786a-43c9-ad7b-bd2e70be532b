import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/Firebase/Authentication/authConfig';
import { 
  getAllUsers, 
  getUserProfile, 
  updateUserRole, 
  UserRole,
  UserProfile 
} from '@/Firebase/firestore/services/UserService';
import { registerWithEmailAndPassword } from '@/Firebase/Authentication/authConfig';
import { upsertUserProfile } from '@/Firebase/firestore/services/UserService';

// Helper function to verify authentication and consultant role
async function verifyConsultantAuth(request: NextRequest): Promise<{ user: UserProfile } | { error: string, status: number }> {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { error: 'Unauthorized', status: 401 };
  }

  // Token available for future authentication if needed
  // const token = authHeader.split(' ')[1];
  
  try {
    // In a real app, you'd verify the Firebase ID token here
    // For now, we'll use a simplified approach
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return { error: 'Unauthorized', status: 401 };
    }

    const userProfile = await getUserProfile(currentUser.uid);
    if (!userProfile || userProfile.role !== UserRole.CONSULTANT) {
      return { error: 'Forbidden: Consultant access required', status: 403 };
    }

    return { user: userProfile };
  } catch (error: unknown) {
    // Log error for debugging
    console.error('Authentication failed:', error);
    return { error: 'Authentication failed', status: 401 };
  }
}

// GET - Fetch all users
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const authResult = await verifyConsultantAuth(request);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const users = await getAllUsers();
    return NextResponse.json({ users });
  } catch (error: unknown) {
    console.error('Error fetching users:', error);
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 });
  }
}

// POST - Create new user
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const authResult = await verifyConsultantAuth(request);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { email, displayName, role, password } = await request.json();

    // Validate required fields
    if (!email || !displayName || !role || !password) {
      return NextResponse.json({ 
        error: 'Missing required fields: email, displayName, role, password' 
      }, { status: 400 });
    }

    // Validate role
    if (!Object.values(UserRole).includes(role)) {
      return NextResponse.json({ 
        error: `Invalid role. Must be one of: ${Object.values(UserRole).join(', ')}` 
      }, { status: 400 });
    }

    // Create user in Firebase Auth
    const newUser = await registerWithEmailAndPassword(email, password);

    // Create user profile data
    const userData = {
      email: email,
      displayName: displayName,
      role: role,
      disabled: false
    };

    const createdProfile = await upsertUserProfile(newUser.uid, userData);

    return NextResponse.json({ 
      message: 'User created successfully',
      user: createdProfile 
    }, { status: 201 });
  } catch (error: unknown) {
    console.error('Error creating user:', error);
    
    // Handle specific Firebase Auth errors
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    if (errorMessage.includes('email-already-in-use')) {
      return NextResponse.json({ error: 'Email already in use' }, { status: 400 });
    }
    
    if (errorMessage.includes('weak-password')) {
      return NextResponse.json({ error: 'Password is too weak' }, { status: 400 });
    }

    return NextResponse.json({ error: 'Failed to create user' }, { status: 500 });
  }
}

// PUT - Update user role
export async function PUT(request: NextRequest): Promise<NextResponse> {
  try {
    const authResult = await verifyConsultantAuth(request);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { uid, role } = await request.json();

    if (!uid || !role) {
      return NextResponse.json({ 
        error: 'Missing required fields: uid, role' 
      }, { status: 400 });
    }

    // Validate role
    if (!Object.values(UserRole).includes(role)) {
      return NextResponse.json({ 
        error: `Invalid role. Must be one of: ${Object.values(UserRole).join(', ')}` 
      }, { status: 400 });
    }

    // Prevent consultants from changing their own role
    if (uid === authResult.user.uid) {
      return NextResponse.json({ 
        error: 'Cannot change your own role' 
      }, { status: 400 });
    }

    await updateUserRole(uid, role as UserRole);

    // Get updated user profile
    const updatedProfile = await getUserProfile(uid);

    return NextResponse.json({ 
      message: 'User role updated successfully',
      user: updatedProfile 
    });
  } catch (error: unknown) {
    console.error('Error updating user role:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    if (errorMessage.includes('User with UID') && errorMessage.includes('not found')) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update user role' }, { status: 500 });
  }
}
