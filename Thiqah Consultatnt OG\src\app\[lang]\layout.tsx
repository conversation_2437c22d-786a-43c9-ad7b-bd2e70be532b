import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
// Use relative path for globals.css
import "../globals.css";
// Language switcher removed as requested
import { i18n, type Locale } from "@/i18n-config"; // Import i18n config and Locale type
import { getDictionary } from "@/dictionaries"; // Import the dictionary helper

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// Change static metadata export to async generateMetadata function
export async function generateMetadata(
  { params }: { params: Promise<{ lang: Locale }> }
): Promise<Metadata> {
  // Await params to get lang
  const { lang } = await params;
  const dictionary = await getDictionary(lang);
  return {
    title: dictionary.layout.title, // Use dictionary value
    description: dictionary.layout.description, // Use dictionary value
  };
}

// Add generateStaticParams to define the available locales
export async function generateStaticParams() {
  // Use locales from config
  return i18n.locales.map((locale: Locale) => ({ lang: locale })); // Add type annotation
}

// Make the component async and await params
export default async function RootLayout({
  children,
  params, // Get the params object
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ lang: Locale }>; // Update params type to Promise
}>) {
  // Await params to get lang
  const { lang } = await params;
  return (
    // Use lang directly
    <html lang={lang} dir={lang === "ar" ? "rtl" : "ltr"}>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {/* Language switcher header removed as requested */}
        {children}
      </body>
    </html>
  );
}
