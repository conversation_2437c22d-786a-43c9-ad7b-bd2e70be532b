"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  ChevronDown, 
  ChevronUp,
  FileText,
  Lightbulb,
  AlertCircle
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface SentenceAnalysis {
  sentence: string;
  domain: string;
  domainDescription: string;
  isCompliant: boolean;
  completenessScore: number;
  issues: string[];
  suggestions: string[];
}

interface SentenceAnalysisCardProps {
  analysis: SentenceAnalysis;
  index: number;
  isRTL: boolean;
}

export function SentenceAnalysisCard({ analysis, index, isRTL }: SentenceAnalysisCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600 bg-green-50 border-green-200";
    if (score >= 60) return "text-yellow-600 bg-yellow-50 border-yellow-200";
    return "text-red-600 bg-red-50 border-red-200";
  };

  const getComplianceIcon = (isCompliant: boolean, score: number) => {
    if (isCompliant && score >= 80) return <CheckCircle className="w-5 h-5 text-green-600" />;
    if (score >= 60) return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
    return <XCircle className="w-5 h-5 text-red-600" />;
  };

  const getDomainColor = (domain: string) => {
    const colors = [
      "bg-blue-100 text-blue-800 border-blue-200",
      "bg-purple-100 text-purple-800 border-purple-200",
      "bg-green-100 text-green-800 border-green-200",
      "bg-orange-100 text-orange-800 border-orange-200",
      "bg-pink-100 text-pink-800 border-pink-200",
      "bg-indigo-100 text-indigo-800 border-indigo-200",
      "bg-teal-100 text-teal-800 border-teal-200",
      "bg-red-100 text-red-800 border-red-200",
      "bg-yellow-100 text-yellow-800 border-yellow-200",
      "bg-gray-100 text-gray-800 border-gray-200"
    ];
    // Use domain string to generate a consistent color index
    const domainHash = domain.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[domainHash % colors.length];
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
    >
      <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-[var(--brand-blue)]">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between gap-4">
            <div className="flex items-start gap-3 flex-1">
              <div className="flex-shrink-0 mt-1">
                {getComplianceIcon(analysis.isCompliant, analysis.completenessScore)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-sm font-medium text-gray-500">
                    #{index + 1}
                  </span>
                  <Badge 
                    variant="outline" 
                    className={`text-xs ${getDomainColor(analysis.domain)}`}
                  >
                    {analysis.domain}
                  </Badge>
                </div>
                <p className={`text-sm text-gray-700 leading-relaxed ${
                  isRTL ? 'text-right' : 'text-left'
                }`}>
                  {analysis.sentence}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 flex-shrink-0">
              <div className={`px-3 py-1 rounded-full text-xs font-medium border ${
                getScoreColor(analysis.completenessScore)
              }`}>
                {analysis.completenessScore}%
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="p-1"
              >
                {isExpanded ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>

        {isExpanded && (
          <CardContent className="pt-0">
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="space-y-4"
            >
              {/* Domain Description */}
              <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">
                    {isRTL ? "وصف المجال" : "Domain Description"}
                  </span>
                </div>
                <p className="text-sm text-blue-800">
                  {analysis.domainDescription}
                </p>
              </div>

              {/* Issues */}
              {analysis.issues.length > 0 && (
                <div className="bg-red-50 rounded-lg p-3 border border-red-200">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="w-4 h-4 text-red-600" />
                    <span className="text-sm font-medium text-red-900">
                      {isRTL ? "المشاكل المحددة" : "Identified Issues"}
                    </span>
                  </div>
                  <ul className="space-y-1">
                    {analysis.issues.map((issue, idx) => (
                      <li key={idx} className="text-sm text-red-800 flex items-start gap-2">
                        <span className="text-red-500 mt-1">•</span>
                        <span>{issue}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Suggestions */}
              {analysis.suggestions.length > 0 && (
                <div className="bg-green-50 rounded-lg p-3 border border-green-200">
                  <div className="flex items-center gap-2 mb-2">
                    <Lightbulb className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium text-green-900">
                      {isRTL ? "اقتراحات التحسين" : "Enhancement Suggestions"}
                    </span>
                  </div>
                  <ul className="space-y-1">
                    {analysis.suggestions.map((suggestion, idx) => (
                      <li key={idx} className="text-sm text-green-800 flex items-start gap-2">
                        <span className="text-green-500 mt-1">•</span>
                        <span>{suggestion}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </motion.div>
          </CardContent>
        )}
      </Card>
    </motion.div>
  );
}
