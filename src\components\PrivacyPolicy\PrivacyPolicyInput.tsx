"use client";

import React, { useState } from "react";

import { FileText, Save, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

interface PrivacyPolicyInputProps {
  selectedLanguage: 'en' | 'ar';
  onSave: (content: string) => void;
  isLoading: boolean;
  isRTL: boolean;
}

export function PrivacyPolicyInput({ selectedLanguage, onSave, isLoading, isRTL }: PrivacyPolicyInputProps) {
  const [content, setContent] = useState("");
  const [wordCount, setWordCount] = useState(0);

  const handleContentChange = (value: string) => {
    setContent(value);
    // Count words (split by whitespace and filter empty strings)
    const words = value.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
  };

  const handleSave = () => {
    if (content.trim()) {
      onSave(content.trim());
    }
  };

  const minWords = 50;
  const isContentValid = wordCount >= minWords;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 bg-[var(--brand-blue)]/10 rounded-xl flex items-center justify-center">
          <FileText className="w-5 h-5 text-[var(--brand-blue)]" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            {isRTL ? "محتوى سياسة الخصوصية" : "Privacy Policy Content"}
          </h3>
          <p className="text-sm text-gray-600">
            {selectedLanguage === 'ar' 
              ? (isRTL ? "اكتب سياسة الخصوصية باللغة العربية" : "Write privacy policy in Arabic")
              : (isRTL ? "اكتب سياسة الخصوصية باللغة الإنجليزية" : "Write privacy policy in English")
            }
          </p>
        </div>
      </div>

      {/* Language Badge */}
      <div className="flex items-center gap-2">
        <span className="text-lg">
          {selectedLanguage === 'ar' ? '🇸🇦' : '🇺🇸'}
        </span>
        <span className="px-3 py-1 bg-[var(--brand-blue)]/10 text-[var(--brand-blue)] rounded-full text-sm font-medium">
          {selectedLanguage === 'ar' ? 'العربية' : 'English'}
        </span>
      </div>

      {/* Content Input */}
      <div className="space-y-3">
        <Label htmlFor="privacy-content" className="text-sm font-medium text-gray-700">
          {isRTL ? "نص سياسة الخصوصية" : "Privacy Policy Text"}
        </Label>
        <Textarea
          id="privacy-content"
          value={content}
          onChange={(e) => handleContentChange(e.target.value)}
          placeholder={
            selectedLanguage === 'ar'
              ? "اكتب سياسة الخصوصية الخاصة بنظامك هنا..."
              : "Write your system's privacy policy here..."
          }
          className={`min-h-[300px] resize-none ${
            selectedLanguage === 'ar' ? 'text-right' : 'text-left'
          } ${!isContentValid && content.length > 0 ? 'border-orange-300 focus:border-orange-500' : ''}`}
          dir={selectedLanguage === 'ar' ? 'rtl' : 'ltr'}
        />
        
        {/* Word Count and Validation */}
        <div className="flex items-center justify-between text-sm">
          <div className={`flex items-center gap-2 ${
            isContentValid ? 'text-green-600' : wordCount > 0 ? 'text-orange-600' : 'text-gray-500'
          }`}>
            {!isContentValid && wordCount > 0 && (
              <AlertCircle className="w-4 h-4" />
            )}
            <span>
              {isRTL 
                ? `عدد الكلمات: ${wordCount} (الحد الأدنى: ${minWords})`
                : `Word count: ${wordCount} (minimum: ${minWords})`
              }
            </span>
          </div>
          
          {isContentValid && (
            <span className="text-green-600 text-xs font-medium">
              {isRTL ? "✓ جاهز للحفظ" : "✓ Ready to save"}
            </span>
          )}
        </div>
      </div>

      {/* Guidelines */}
      <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h4 className="text-sm font-semibold text-gray-900 mb-2">
          {isRTL ? "إرشادات كتابة سياسة الخصوصية" : "Privacy Policy Guidelines"}
        </h4>
        <ul className="text-xs text-gray-600 space-y-1">
          <li>
            {isRTL 
              ? "• تأكد من تضمين معلومات الاتصال والبيانات المجمعة"
              : "• Include contact information and data collection details"
            }
          </li>
          <li>
            {isRTL 
              ? "• اذكر الغرض من جمع البيانات وكيفية استخدامها"
              : "• Specify the purpose of data collection and usage"
            }
          </li>
          <li>
            {isRTL 
              ? "• وضح حقوق المستخدمين في البيانات الشخصية"
              : "• Explain user rights regarding personal data"
            }
          </li>
          <li>
            {isRTL 
              ? "• تأكد من الامتثال لقانون حماية البيانات الشخصية السعودي"
              : "• Ensure compliance with Saudi Personal Data Protection Law"
            }
          </li>
        </ul>
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-4">
        <Button
          onClick={handleSave}
          disabled={!isContentValid || isLoading}
          className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white px-8"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
              {isRTL ? "جاري الحفظ..." : "Saving..."}
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              {isRTL ? "حفظ سياسة الخصوصية" : "Save Privacy Policy"}
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
