"use client";

import React, { useState, useEffect, useRef } from "react";
import { Trash2, <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ircle, Eye } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemsService } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";
import { auth } from "@/Firebase/Authentication/authConfig";
import { getUserProfile } from "@/Firebase/firestore/services/UserService";



interface SystemDataTableActionsProps {
  selectedRows: Set<string>;
  systemId: string;
  isRTL: boolean;
  onDataUpdate?: () => void;
  onClearSelection: () => void;
  onSelectAllSystem: () => void;
  totalSystemRecords: number;
}

export function SystemDataTableActions({
  selectedRows,
  systemId,
  isRTL,
  onDataUpdate,
  onClearSelection,
  onSelectAllSystem,
  totalSystemRecords
}: SystemDataTableActionsProps) {
  const { toast } = useToast();
  const [isBulkReviewing, setIsBulkReviewing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showActionsDropdown, setShowActionsDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowActionsDropdown(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Bulk delete selected attributes
  const handleBulkDelete = async () => {
    if (selectedRows.size === 0) return;

    setIsDeleting(true);
    try {
      const documentIds = Array.from(selectedRows);
      await SystemsService.deleteSystemDataBatch(systemId, documentIds);

      toast({
        title: isRTL ? "تم الحذف" : "Deleted Successfully",
        description: isRTL ?
          `تم حذف ${selectedRows.size} عنصر بنجاح` :
          `Successfully deleted ${selectedRows.size} items`,
      });

      // Clear selection and refresh data
      onClearSelection();
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Bulk delete error:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to delete selected items";
      toast({
        title: isRTL ? "خطأ في الحذف" : "Delete Error",
        description: isRTL ?
          `خطأ في حذف العناصر المحددة: ${errorMessage}` :
          `Error deleting selected items: ${errorMessage}`,
        variant: "destructive"
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  // Bulk mark as reviewed
  const handleBulkMarkAsReviewed = async () => {
    if (selectedRows.size === 0) return;

    setIsBulkReviewing(true);
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('User not authenticated');
      }

      const userProfile = await getUserProfile(user.uid);
      const reviewData = {
        isReviewed: true,
        reviewedBy: userProfile?.displayName || user.email || 'Unknown',
        reviewedAt: new Date().toISOString()
      };

      const updates = Array.from(selectedRows).map(rowId => ({
        documentId: rowId,
        data: reviewData
      }));

      await SystemsService.updateSystemDataBatch(systemId, updates);

      toast({
        title: isRTL ? "تم التمييز المجمع" : "Bulk Review Complete",
        description: isRTL ?
          `تم تمييز ${selectedRows.size} عنصر كمراجع` :
          `Marked ${selectedRows.size} items as reviewed`,
      });

      // Clear selection and refresh data
      onClearSelection();
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Bulk review error:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to mark items as reviewed";
      toast({
        title: isRTL ? "خطأ في التمييز المجمع" : "Bulk Review Error",
        description: isRTL ?
          `خطأ في تمييز العناصر: ${errorMessage}` :
          `Error marking items as reviewed: ${errorMessage}`,
        variant: "destructive"
      });
    } finally {
      setIsBulkReviewing(false);
    }
  };

  // Bulk remove review
  const handleBulkRemoveReview = async () => {
    if (selectedRows.size === 0) return;

    setIsBulkReviewing(true);
    try {
      const reviewData = {
        isReviewed: false,
        reviewedBy: undefined,
        reviewedAt: undefined
      };

      const updates = Array.from(selectedRows).map(rowId => ({
        documentId: rowId,
        data: reviewData
      }));

      await SystemsService.updateSystemDataBatch(systemId, updates);

      toast({
        title: isRTL ? "تم إزالة المراجعة المجمعة" : "Bulk Review Removal Complete",
        description: isRTL ? 
          `تم إزالة علامة المراجعة من ${selectedRows.size} عنصر` :
          `Removed review marks from ${selectedRows.size} items`,
      });

      // Clear selection and refresh data
      onClearSelection();
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Bulk remove review error:', error);
      toast({
        title: isRTL ? "خطأ في إزالة المراجعة" : "Remove Review Error",
        description: error instanceof Error ? error.message : "Failed to remove review marks",
        variant: "destructive"
      });
    } finally {
      setIsBulkReviewing(false);
    }
  };

  // Bulk mark as needs review
  const handleBulkMarkAsNeedsReview = async () => {
    if (selectedRows.size === 0) return;

    setIsBulkReviewing(true);
    try {
      const updates = Array.from(selectedRows).map(rowId => ({
        documentId: rowId,
        data: { needsReview: true }
      }));

      await SystemsService.updateSystemDataBatch(systemId, updates);

      toast({
        title: isRTL ? "تم التمييز المجمع" : "Bulk Marking Complete",
        description: isRTL ?
          `تم تمييز ${selectedRows.size} عنصر كيحتاج مراجعة` :
          `Marked ${selectedRows.size} items as needs review`,
      });

      // Clear selection and refresh data
      onClearSelection();
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Bulk needs review error:', error);
      toast({
        title: isRTL ? "خطأ في التمييز المجمع" : "Bulk Marking Error",
        description: error instanceof Error ? error.message : "Failed to mark items as needs review",
        variant: "destructive"
      });
    } finally {
      setIsBulkReviewing(false);
    }
  };

  // Bulk remove needs review
  const handleBulkRemoveNeedsReview = async () => {
    if (selectedRows.size === 0) return;

    setIsBulkReviewing(true);
    try {
      const updates = Array.from(selectedRows).map(rowId => ({
        documentId: rowId,
        data: { needsReview: false }
      }));

      await SystemsService.updateSystemDataBatch(systemId, updates);

      toast({
        title: isRTL ? "تم إزالة التمييز المجمع" : "Bulk Marking Removal Complete",
        description: isRTL ?
          `تم إزالة تمييز ${selectedRows.size} عنصر` :
          `Removed needs review marks from ${selectedRows.size} items`,
      });

      // Clear selection and refresh data
      onClearSelection();
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Bulk remove needs review error:', error);
      toast({
        title: isRTL ? "خطأ في إزالة التمييز المجمع" : "Bulk Marking Removal Error",
        description: error instanceof Error ? error.message : "Failed to remove needs review marks",
        variant: "destructive"
      });
    } finally {
      setIsBulkReviewing(false);
    }
  };

  // Bulk mark push to client yes
  const handleBulkMarkPushToClientYes = async () => {
    if (selectedRows.size === 0) return;

    setIsBulkReviewing(true);
    try {
      const updates = Array.from(selectedRows).map(rowId => ({
        documentId: rowId,
        data: { pushToClient: "Yes" }
      }));

      await SystemsService.updateSystemDataBatch(systemId, updates);

      toast({
        title: isRTL ? "تم التمييز المجمع" : "Bulk Marking Complete",
        description: isRTL ?
          `تم تمييز ${selectedRows.size} عنصر كمرسل للعميل` :
          `Marked ${selectedRows.size} items as push to client`,
      });

      // Clear selection and refresh data
      onClearSelection();
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Bulk push to client error:', error);
      toast({
        title: isRTL ? "خطأ في التمييز المجمع" : "Bulk Marking Error",
        description: error instanceof Error ? error.message : "Failed to mark items as push to client",
        variant: "destructive"
      });
    } finally {
      setIsBulkReviewing(false);
    }
  };

  // Bulk mark push to client no
  const handleBulkMarkPushToClientNo = async () => {
    if (selectedRows.size === 0) return;

    setIsBulkReviewing(true);
    try {
      const updates = Array.from(selectedRows).map(rowId => ({
        documentId: rowId,
        data: { pushToClient: "No" }
      }));

      await SystemsService.updateSystemDataBatch(systemId, updates);

      toast({
        title: isRTL ? "تم التمييز المجمع" : "Bulk Marking Complete",
        description: isRTL ?
          `تم تمييز ${selectedRows.size} عنصر كغير مرسل للعميل` :
          `Marked ${selectedRows.size} items as not push to client`,
      });

      // Clear selection and refresh data
      onClearSelection();
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error('Bulk push to client error:', error);
      toast({
        title: isRTL ? "خطأ في التمييز المجمع" : "Bulk Marking Error",
        description: error instanceof Error ? error.message : "Failed to mark items as not push to client",
        variant: "destructive"
      });
    } finally {
      setIsBulkReviewing(false);
    }
  };

  return (
    <>
      {/* Selection Controls */}
      <div className="flex items-center gap-3 mb-4">
        {/* Select All System Button */}
        <Button
          onClick={onSelectAllSystem}
          variant="outline"
          size="sm"
          className="text-xs px-3 py-1 border-blue-300 text-blue-700 hover:bg-blue-50"
        >
          <CheckCircle className="w-3 h-3 mr-1" />
          {isRTL ? `تحديد الكل (${totalSystemRecords})` : `Select All (${totalSystemRecords})`}
        </Button>

        {selectedRows.size > 0 && (
          <span className="text-sm text-gray-600">
            {isRTL ? `${selectedRows.size} محدد` : `${selectedRows.size} selected`}
          </span>
        )}
      </div>

      {/* Bulk Actions - Only show when items are selected */}
      {selectedRows.size > 0 && (
        <div className="flex items-center gap-3 px-4 py-2 bg-blue-50 border border-blue-200 rounded-lg mb-4">
          <span className="text-sm font-medium text-blue-900">
            {isRTL ? `${selectedRows.size} محدد` : `${selectedRows.size} selected`}
          </span>

          {/* Primary Actions */}
          <Button
            onClick={() => setShowDeleteConfirm(true)}
            disabled={isDeleting || isBulkReviewing}
            size="sm"
            className="bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1"
          >
            {isDeleting ? (
              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
            ) : (
              <Trash2 className="w-3 h-3 mr-1" />
            )}
            {isRTL ? "حذف" : "Delete"}
          </Button>

          {/* Actions Dropdown */}
          <div className="relative" ref={dropdownRef}>
            <Button
              onClick={() => setShowActionsDropdown(!showActionsDropdown)}
              disabled={isBulkReviewing}
              size="sm"
              variant="outline"
              className="text-xs px-3 py-1"
            >
              {isRTL ? "المزيد من الإجراءات" : "More Actions"}
              <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </Button>

            {showActionsDropdown && (
              <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                <div className="py-1">
                  {/* Review Actions */}
                  <div className="px-3 py-1 text-xs font-medium text-gray-500 border-b">
                    {isRTL ? "إجراءات المراجعة" : "Review Actions"}
                  </div>
                  <button
                    onClick={() => {
                      handleBulkMarkAsReviewed();
                      setShowActionsDropdown(false);
                    }}
                    disabled={isBulkReviewing}
                    className="w-full text-left px-3 py-2 text-xs hover:bg-gray-50 flex items-center"
                  >
                    <CheckCircle className="w-3 h-3 mr-2 text-green-600" />
                    {isRTL ? "تمييز كمراجع" : "Mark as Reviewed"}
                  </button>
                  <button
                    onClick={() => {
                      handleBulkRemoveReview();
                      setShowActionsDropdown(false);
                    }}
                    disabled={isBulkReviewing}
                    className="w-full text-left px-3 py-2 text-xs hover:bg-gray-50 flex items-center"
                  >
                    <XCircle className="w-3 h-3 mr-2 text-orange-600" />
                    {isRTL ? "إزالة المراجعة" : "Remove Review"}
                  </button>

                  {/* Needs Review Actions */}
                  <div className="px-3 py-1 text-xs font-medium text-gray-500 border-b border-t">
                    {isRTL ? "يحتاج مراجعة" : "Needs Review"}
                  </div>
                  <button
                    onClick={() => {
                      handleBulkMarkAsNeedsReview();
                      setShowActionsDropdown(false);
                    }}
                    disabled={isBulkReviewing}
                    className="w-full text-left px-3 py-2 text-xs hover:bg-gray-50 flex items-center"
                  >
                    <Eye className="w-3 h-3 mr-2 text-amber-600" />
                    {isRTL ? "يحتاج مراجعة" : "Mark Needs Review"}
                  </button>
                  <button
                    onClick={() => {
                      handleBulkRemoveNeedsReview();
                      setShowActionsDropdown(false);
                    }}
                    disabled={isBulkReviewing}
                    className="w-full text-left px-3 py-2 text-xs hover:bg-gray-50 flex items-center"
                  >
                    <XCircle className="w-3 h-3 mr-2 text-gray-600" />
                    {isRTL ? "لا يحتاج مراجعة" : "Remove Needs Review"}
                  </button>

                  {/* Push to Client Actions */}
                  <div className="px-3 py-1 text-xs font-medium text-gray-500 border-b border-t">
                    {isRTL ? "إرسال للعميل" : "Push to Client"}
                  </div>
                  <button
                    onClick={() => {
                      handleBulkMarkPushToClientYes();
                      setShowActionsDropdown(false);
                    }}
                    disabled={isBulkReviewing}
                    className="w-full text-left px-3 py-2 text-xs hover:bg-gray-50 flex items-center"
                  >
                    <CheckCircle className="w-3 h-3 mr-2 text-blue-600" />
                    {isRTL ? "نعم" : "Yes"}
                  </button>
                  <button
                    onClick={() => {
                      handleBulkMarkPushToClientNo();
                      setShowActionsDropdown(false);
                    }}
                    disabled={isBulkReviewing}
                    className="w-full text-left px-3 py-2 text-xs hover:bg-gray-50 flex items-center"
                  >
                    <XCircle className="w-3 h-3 mr-2 text-red-600" />
                    {isRTL ? "لا" : "No"}
                  </button>
                </div>
              </div>
            )}
          </div>

          <Button
            onClick={onClearSelection}
            variant="outline"
            size="sm"
            className="text-xs px-3 py-1"
          >
            {isRTL ? "إلغاء التحديد" : "Clear Selection"}
          </Button>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {isRTL ? "تأكيد الحذف" : "Confirm Delete"}
            </h3>
            <p className="text-gray-600 mb-6">
              {isRTL 
                ? `هل أنت متأكد من حذف ${selectedRows.size} عنصر؟ لا يمكن التراجع عن هذا الإجراء.`
                : `Are you sure you want to delete ${selectedRows.size} items? This action cannot be undone.`
              }
            </p>
            <div className="flex gap-3 justify-end">
              <Button
                onClick={() => setShowDeleteConfirm(false)}
                variant="outline"
                disabled={isDeleting}
              >
                {isRTL ? "إلغاء" : "Cancel"}
              </Button>
              <Button
                onClick={handleBulkDelete}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isDeleting ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4 mr-2" />
                )}
                {isRTL ? "حذف" : "Delete"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
