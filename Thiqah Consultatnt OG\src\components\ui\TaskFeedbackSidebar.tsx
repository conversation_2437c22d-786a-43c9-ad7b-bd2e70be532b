"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  MessageSquare,
  Send,
  CheckCircle,
  XCircle,
  Trash2,
  User,
  MessageCircle,
  Check
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { TasksService, TaskFeedback } from "@/Firebase/firestore/services/TasksService";
import { auth } from "@/Firebase/Authentication/authConfig";
import { getUserProfile } from "@/Firebase/firestore/services/UserService";
import { Timestamp } from "firebase/firestore";

interface TaskFeedbackSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  taskId: string;
  feedback: TaskFeedback[];
  onFeedbackUpdate: () => void;
  isRTL: boolean;
}

export function TaskFeedbackSidebar({
  isOpen,
  onClose,
  taskId,
  feedback,
  onFeedbackUpdate,
  isRTL
}: TaskFeedbackSidebarProps) {
  const [newFeedback, setNewFeedback] = useState("");
  const [replyTexts, setReplyTexts] = useState<{ [feedbackId: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmittingReply, setIsSubmittingReply] = useState<{ [replyId: string]: boolean }>({});
  const [currentUser, setCurrentUser] = useState<typeof auth.currentUser>(null);
  const { toast } = useToast();

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      setCurrentUser(user);
    });
    return () => unsubscribe();
  }, []);

  const formatDate = (timestamp: Timestamp) => {
    const date = timestamp.toDate();
    return date.toLocaleDateString(isRTL ? "ar-SA" : "en-US", {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleSubmitFeedback = async () => {
    if (!newFeedback.trim() || !currentUser) return;

    setIsSubmitting(true);
    try {
      const userProfile = await getUserProfile(currentUser.uid);
      const displayName = userProfile?.displayName || currentUser?.displayName || currentUser?.email || 'Unknown';

      await TasksService.addTaskFeedback(taskId, {
        content: newFeedback.trim(),
        createdBy: currentUser.uid,
        createdByName: displayName
      });

      setNewFeedback("");
      onFeedbackUpdate();
      
      toast({
        title: isRTL ? "تم إضافة التعليق" : "Feedback added",
        description: isRTL ? "تم إضافة التعليق بنجاح" : "Feedback added successfully",
      });
    } catch (error) {
      console.error('Error adding feedback:', error);
      toast({
        title: isRTL ? "خطأ في الإضافة" : "Add error",
        description: isRTL ? "فشل في إضافة التعليق" : "Failed to add feedback",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitReply = async (feedbackId: string) => {
    const replyText = replyTexts[feedbackId];
    if (!replyText?.trim() || !currentUser) return;

    setIsSubmittingReply(prev => ({ ...prev, [feedbackId]: true }));
    try {
      const userProfile = await getUserProfile(currentUser.uid);
      const displayName = userProfile?.displayName || currentUser?.displayName || currentUser?.email || 'Unknown';

      await TasksService.addFeedbackReply(taskId, feedbackId, {
        content: replyText.trim(),
        createdBy: currentUser.uid,
        createdByName: displayName
      });

      setReplyTexts(prev => ({ ...prev, [feedbackId]: "" }));
      onFeedbackUpdate();
      
      toast({
        title: isRTL ? "تم إضافة الرد" : "Reply added",
        description: isRTL ? "تم إضافة الرد بنجاح" : "Reply added successfully",
      });
    } catch (error) {
      console.error('Error adding reply:', error);
      toast({
        title: isRTL ? "خطأ في الإضافة" : "Add error",
        description: isRTL ? "فشل في إضافة الرد" : "Failed to add reply",
        variant: "destructive",
      });
    } finally {
      setIsSubmittingReply(prev => ({ ...prev, [feedbackId]: false }));
    }
  };

  const handleUpdateReplyStatus = async (feedbackId: string, replyId: string, status: 'done' | 'not_done') => {
    if (!currentUser) return;

    try {
      const userProfile = await getUserProfile(currentUser.uid);
      const displayName = userProfile?.displayName || currentUser?.displayName || currentUser?.email || 'Unknown';

      await TasksService.updateReplyStatus(taskId, feedbackId, replyId, status, currentUser.uid, displayName);
      onFeedbackUpdate();
      
      toast({
        title: isRTL ? "تم تحديث الحالة" : "Status updated",
        description: isRTL ? `تم تحديث الحالة إلى ${status === 'done' ? 'منجز' : 'غير منجز'}` : `Status updated to ${status === 'done' ? 'Done' : 'Not Done'}`,
      });
    } catch (error) {
      console.error('Error updating reply status:', error);
      toast({
        title: isRTL ? "خطأ في التحديث" : "Update error",
        description: isRTL ? "فشل في تحديث الحالة" : "Failed to update status",
        variant: "destructive",
      });
    }
  };

  const handleResolveFeedback = async (feedbackId: string) => {
    if (!currentUser) return;

    try {
      const userProfile = await getUserProfile(currentUser.uid);
      const displayName = userProfile?.displayName || currentUser?.displayName || currentUser?.email || 'Unknown';

      await TasksService.resolveFeedback(taskId, feedbackId, currentUser.uid, displayName);
      onFeedbackUpdate();
      
      toast({
        title: isRTL ? "تم حل التعليق" : "Feedback resolved",
        description: isRTL ? "تم وضع علامة على التعليق كمحلول" : "Feedback marked as resolved",
      });
    } catch (error) {
      console.error('Error resolving feedback:', error);
      toast({
        title: isRTL ? "خطأ في الحل" : "Resolve error",
        description: isRTL ? "فشل في حل التعليق" : "Failed to resolve feedback",
        variant: "destructive",
      });
    }
  };

  const handleDeleteFeedback = async (feedbackId: string) => {
    try {
      await TasksService.deleteFeedback(taskId, feedbackId);
      onFeedbackUpdate();
      
      toast({
        title: isRTL ? "تم حذف التعليق" : "Feedback deleted",
        description: isRTL ? "تم حذف التعليق بنجاح" : "Feedback deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting feedback:', error);
      toast({
        title: isRTL ? "خطأ في الحذف" : "Delete error",
        description: isRTL ? "فشل في حذف التعليق" : "Failed to delete feedback",
        variant: "destructive",
      });
    }
  };

  const handleDeleteReply = async (feedbackId: string, replyId: string) => {
    try {
      await TasksService.deleteFeedbackReply(taskId, feedbackId, replyId);
      onFeedbackUpdate();
      
      toast({
        title: isRTL ? "تم حذف الرد" : "Reply deleted",
        description: isRTL ? "تم حذف الرد بنجاح" : "Reply deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting reply:', error);
      toast({
        title: isRTL ? "خطأ في الحذف" : "Delete error",
        description: isRTL ? "فشل في حذف الرد" : "Failed to delete reply",
        variant: "destructive",
      });
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40"
            onClick={onClose}
          />

          {/* Sidebar */}
          <motion.div
            initial={{ x: isRTL ? -400 : 400 }}
            animate={{ x: 0 }}
            exit={{ x: isRTL ? -400 : 400 }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className={`fixed top-0 ${isRTL ? 'left-0' : 'right-0'} h-full w-96 max-w-[90vw] bg-white shadow-2xl z-50 flex flex-col overflow-hidden`}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 flex-shrink-0">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                  <MessageSquare className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-bold text-white">
                    {isRTL ? "التعليقات" : "Feedback"}
                  </h2>
                  <p className="text-white/80 text-xs">
                    {feedback.length} {isRTL ? "تعليق" : "comments"}
                  </p>
                </div>
              </div>
              <Button
                onClick={onClose}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20 rounded-lg p-2"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Content */}
            <div className="flex-1 flex flex-col overflow-hidden">
              {/* Add New Feedback */}
              <div className="p-3 border-b border-gray-200 bg-gray-50 flex-shrink-0">
                <div className="space-y-2">
                  <Textarea
                    placeholder={isRTL ? "اكتب تعليقك هنا..." : "Write your feedback here..."}
                    value={newFeedback}
                    onChange={(e) => setNewFeedback(e.target.value)}
                    className="min-h-16 text-sm border-gray-200 focus:border-[var(--brand-blue)] rounded-lg resize-none"
                  />
                  <Button
                    onClick={handleSubmitFeedback}
                    disabled={!newFeedback.trim() || isSubmitting}
                    size="sm"
                    className="w-full bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 rounded-lg"
                  >
                    <Send className="w-3 h-3 mr-2" />
                    {isSubmitting ? (isRTL ? "جاري الإرسال..." : "Sending...") : (isRTL ? "إرسال التعليق" : "Send Feedback")}
                  </Button>
                </div>
              </div>

              {/* Feedback List */}
              <div className="flex-1 overflow-y-auto p-3">
                <div className="space-y-3">
                  {feedback.length === 0 ? (
                    <div className="text-center py-8">
                      <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <MessageCircle className="w-6 h-6 text-gray-400" />
                      </div>
                      <h3 className="text-base font-medium text-gray-900 mb-1">
                        {isRTL ? "لا توجد تعليقات بعد" : "No feedback yet"}
                      </h3>
                      <p className="text-gray-500 text-xs">
                        {isRTL ? "كن أول من يضيف تعليق على هذه المهمة" : "Be the first to add feedback to this task"}
                      </p>
                    </div>
                  ) : (
                    feedback.map((feedbackItem) => (
                      <FeedbackThread
                        key={feedbackItem.id}
                        feedback={feedbackItem}
                        replyText={replyTexts[feedbackItem.id] || ""}
                        onReplyTextChange={(text) => setReplyTexts(prev => ({ ...prev, [feedbackItem.id]: text }))}
                        onSubmitReply={() => handleSubmitReply(feedbackItem.id)}
                        onUpdateReplyStatus={handleUpdateReplyStatus}
                        onResolveFeedback={handleResolveFeedback}
                        onDeleteFeedback={handleDeleteFeedback}
                        onDeleteReply={handleDeleteReply}
                        isSubmittingReply={isSubmittingReply[feedbackItem.id] || false}
                        currentUser={currentUser}
                        formatDate={formatDate}
                        isRTL={isRTL}
                      />
                    ))
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}

interface FeedbackThreadProps {
  feedback: TaskFeedback;
  replyText: string;
  onReplyTextChange: (text: string) => void;
  onSubmitReply: () => void;
  onUpdateReplyStatus: (feedbackId: string, replyId: string, status: 'done' | 'not_done') => void;
  onResolveFeedback: (feedbackId: string) => void;
  onDeleteFeedback: (feedbackId: string) => void;
  onDeleteReply: (feedbackId: string, replyId: string) => void;
  isSubmittingReply: boolean;
  currentUser: typeof auth.currentUser;
  formatDate: (timestamp: Timestamp) => string;
  isRTL: boolean;
}

function FeedbackThread({
  feedback,
  replyText,
  onReplyTextChange,
  onSubmitReply,
  onUpdateReplyStatus,
  onResolveFeedback,
  onDeleteFeedback,
  onDeleteReply,
  isSubmittingReply,
  currentUser,
  formatDate,
  isRTL
}: FeedbackThreadProps) {
  const [showReplyBox, setShowReplyBox] = useState(false);

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-3 ${feedback.isResolved ? 'opacity-75' : ''}`}>
      {/* Main Feedback */}
      <div className="space-y-2">
        <div className="flex items-start justify-between gap-2">
          <div className="flex items-start gap-2 flex-1 min-w-0">
            <div className="w-6 h-6 bg-[var(--brand-blue)]/10 rounded-full flex items-center justify-center flex-shrink-0">
              <User className="w-3 h-3 text-[var(--brand-blue)]" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1 flex-wrap">
                <span className="font-medium text-gray-900 text-sm truncate">{feedback.createdByName}</span>
                <span className="text-xs text-gray-500 flex-shrink-0">{formatDate(feedback.createdAt)}</span>
                {feedback.isResolved && (
                  <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full flex-shrink-0">
                    {isRTL ? "محلول" : "Resolved"}
                  </span>
                )}
              </div>
              <p className="text-gray-700 text-sm leading-relaxed break-words">{feedback.content}</p>
            </div>
          </div>
          <div className="flex items-center gap-1 flex-shrink-0">
            {!feedback.isResolved && (
              <Button
                onClick={() => onResolveFeedback(feedback.id)}
                size="sm"
                variant="ghost"
                className="text-green-600 hover:text-green-700 hover:bg-green-50 p-1 h-5 w-5"
              >
                <CheckCircle className="w-3 h-3" />
              </Button>
            )}
            {currentUser?.uid === feedback.createdBy && (
              <Button
                onClick={() => onDeleteFeedback(feedback.id)}
                size="sm"
                variant="ghost"
                className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 h-5 w-5"
              >
                <Trash2 className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>

        {/* Replies */}
        {feedback.replies.length > 0 && (
          <div className="ml-6 space-y-2 border-l-2 border-gray-100 pl-3">
            {feedback.replies.map((reply) => (
              <div key={reply.id} className="bg-gray-50 rounded-lg p-2">
                <div className="flex items-start justify-between mb-1 gap-2">
                  <div className="flex items-center gap-2 flex-wrap min-w-0 flex-1">
                    <span className="font-medium text-gray-900 text-xs truncate">{reply.createdByName}</span>
                    <span className="text-xs text-gray-500 flex-shrink-0">{formatDate(reply.createdAt)}</span>
                    {reply.status && (
                      <span className={`text-xs px-1.5 py-0.5 rounded-full flex-shrink-0 ${
                        reply.status === 'done'
                          ? 'bg-green-100 text-green-700'
                          : 'bg-red-100 text-red-700'
                      }`}>
                        {reply.status === 'done' ? (isRTL ? "منجز" : "Done") : (isRTL ? "غير منجز" : "Not Done")}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-0.5 flex-shrink-0">
                    <Button
                      onClick={() => onUpdateReplyStatus(feedback.id, reply.id, 'done')}
                      size="sm"
                      variant="ghost"
                      className="text-green-600 hover:text-green-700 hover:bg-green-50 p-0.5 h-4 w-4"
                    >
                      <Check className="w-2.5 h-2.5" />
                    </Button>
                    <Button
                      onClick={() => onUpdateReplyStatus(feedback.id, reply.id, 'not_done')}
                      size="sm"
                      variant="ghost"
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 p-0.5 h-4 w-4"
                    >
                      <XCircle className="w-2.5 h-2.5" />
                    </Button>
                    {currentUser?.uid === reply.createdBy && (
                      <Button
                        onClick={() => onDeleteReply(feedback.id, reply.id)}
                        size="sm"
                        variant="ghost"
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 p-0.5 h-4 w-4"
                      >
                        <Trash2 className="w-2 h-2" />
                      </Button>
                    )}
                  </div>
                </div>
                <p className="text-gray-700 text-xs leading-relaxed break-words">{reply.content}</p>
                {reply.statusUpdatedAt && (
                  <p className="text-xs text-gray-500 mt-1">
                    {isRTL ? "تم التحديث بواسطة" : "Updated by"} {reply.statusUpdatedByName} {isRTL ? "في" : "on"} {formatDate(reply.statusUpdatedAt)}
                  </p>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Reply Box */}
        {!feedback.isResolved && (
          <div className="ml-6 mt-2">
            {showReplyBox ? (
              <div className="space-y-2">
                <Textarea
                  placeholder={isRTL ? "اكتب ردك هنا..." : "Write your reply here..."}
                  value={replyText}
                  onChange={(e) => onReplyTextChange(e.target.value)}
                  className="min-h-12 text-xs border-gray-200 focus:border-[var(--brand-blue)] rounded-lg resize-none"
                />
                <div className="flex gap-1">
                  <Button
                    onClick={onSubmitReply}
                    disabled={!replyText.trim() || isSubmittingReply}
                    size="sm"
                    className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white rounded-lg text-xs px-2 py-1"
                  >
                    <Send className="w-2.5 h-2.5 mr-1" />
                    {isSubmittingReply ? (isRTL ? "جاري الإرسال..." : "Sending...") : (isRTL ? "رد" : "Reply")}
                  </Button>
                  <Button
                    onClick={() => {
                      setShowReplyBox(false);
                      onReplyTextChange("");
                    }}
                    size="sm"
                    variant="outline"
                    className="rounded-lg text-xs px-2 py-1"
                  >
                    {isRTL ? "إلغاء" : "Cancel"}
                  </Button>
                </div>
              </div>
            ) : (
              <Button
                onClick={() => setShowReplyBox(true)}
                size="sm"
                variant="outline"
                className="text-[var(--brand-blue)] border-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/10 rounded-lg text-xs px-2 py-1"
              >
                <MessageCircle className="w-2.5 h-2.5 mr-1" />
                {isRTL ? "رد" : "Reply"}
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
