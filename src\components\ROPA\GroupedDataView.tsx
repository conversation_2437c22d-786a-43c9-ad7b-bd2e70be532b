"use client";

import React, { useState } from "react";
import { ChevronDown, ChevronRight, Edit, Trash2, Table, Database, Move, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

import { Badge } from "@/components/ui/badge";
import { SystemData } from "@/Firebase/firestore/SystemsService";

interface PersonalDataGroup {
  id: string;
  name: string;
  description: string;
  attributes: string[];
  purpose: string;
  legalBasis: string;
  retentionPeriod: string;
  dataSubjects: string[];
  createdAt: Date;
  updatedAt: Date;
  attributeReasons?: Array<{
    attributeId: string;
    reason: string;
  }>;
}

interface GroupedDataViewProps {
  groups: PersonalDataGroup[];
  personalData: SystemData[];
  isLoading: boolean;
  lang: string;
  systemId: string;
  expandedGroup: string | null;
  setExpandedGroup: (groupId: string | null) => void;
  onEditGroup: (groupId: string) => void;
  onDeleteGroup: (groupId: string) => void;
  onTransferAttribute?: (attributeId: string, fromGroupId: string, toGroupId: string) => void;
}

export function GroupedDataView({
  groups,
  personalData,
  isLoading,
  lang,
  expandedGroup,
  setExpandedGroup,
  onEditGroup,
  onDeleteGroup,
  onTransferAttribute
}: GroupedDataViewProps) {
  const isRTL = lang === "ar";
  const [transferMode, setTransferMode] = useState<{attributeId: string, fromGroupId: string} | null>(null);

  // Create a lookup map for personal data by table.column
  const dataLookup = new Map();
  personalData.forEach(item => {
    const key = `${item.tableName}.${item.columnName}`;
    dataLookup.set(key, item);
  });

  const handleGroupToggle = (groupId: string) => {
    setExpandedGroup(expandedGroup === groupId ? null : groupId);
  };

  const getPersonalDataTypeColor = (type: string) => {
    switch (type) {
      case 'direct':
        return 'bg-blue-100 text-blue-800';
      case 'indirect':
        return 'bg-purple-100 text-purple-800';
      case 'pseudonymous':
        return 'bg-orange-100 text-orange-800';
      case 'anonymous':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getSpecialCategoryColor = (category: string) => {
    switch (category) {
      case 'PII':
        return 'bg-green-100 text-green-800';
      case 'PHI':
        return 'bg-red-100 text-red-800';
      case 'PCI':
        return 'bg-yellow-100 text-yellow-800';
      case 'Genetic':
        return 'bg-pink-100 text-pink-800';
      case 'Biometric':
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getConfidentialityColor = (level: string) => {
    switch (level) {
      case 'Public':
        return 'bg-green-100 text-green-800';
      case 'Confidential':
        return 'bg-yellow-100 text-yellow-800';
      case 'Secret':
        return 'bg-orange-100 text-orange-800';
      case 'Top Secret':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="h-20 bg-gray-200 rounded-lg animate-pulse"></div>
        ))}
      </div>
    );
  }

  if (groups.length === 0) {
    return (
      <div className="text-center py-12">
        <Database className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {isRTL ? "لا توجد مجموعات" : "No Groups"}
        </h3>
        <p className="text-gray-600">
          {isRTL 
            ? "استخدم زر 'تجميع الكيانات' لإنشاء مجموعات تلقائياً" 
            : "Use 'Group Entities' button to create groups automatically"
          }
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-8">
        <h3 className="text-xl font-bold text-gray-900">
          {isRTL ? "المجموعات المنشأة" : "Created Groups"}
        </h3>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-[var(--brand-blue)] rounded-full"></div>
          <span className="text-base font-semibold text-gray-700">
            {groups.length} {isRTL ? "مجموعة" : "groups"}
          </span>
        </div>
      </div>

      {/* Check if any group is expanded */}
      {expandedGroup ? (
        // Full Width Expanded View - Takes entire page width
        <div className="w-full">
          {groups.map((group) => {
            const isExpanded = expandedGroup === group.id;
            const groupAttributes = group.attributes.map(attr => dataLookup.get(attr)).filter(Boolean);

            if (!isExpanded) return null;

            return (
              <div key={group.id} className="w-full">
                {/* Compact Card Header */}
                <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-t-3xl border border-gray-200/60 shadow-xl overflow-hidden">
                  <div
                    className="cursor-pointer p-6 hover:bg-gradient-to-br hover:from-[var(--brand-blue)]/5 hover:to-[var(--brand-blue)]/10 transition-all duration-300"
                    onClick={() => handleGroupToggle(group.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="relative">
                          <div className="w-12 h-12 bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/90 to-[var(--brand-blue)]/80 rounded-2xl flex items-center justify-center shadow-lg">
                            <Database className="w-6 h-6 text-white" />
                          </div>
                          <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg border-2 border-white">
                            <span className="text-xs font-bold text-white">{group.attributes.length}</span>
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center gap-3 mb-1">
                            <h3 className="text-lg font-bold text-gray-900">
                              {group.name}
                            </h3>
                            <ChevronDown className="w-4 h-4 text-[var(--brand-blue)]" />
                          </div>
                          <div className="px-3 py-1 bg-gradient-to-r from-[var(--brand-blue)]/10 to-[var(--brand-blue)]/5 rounded-full border border-[var(--brand-blue)]/20">
                            <span className="text-[var(--brand-blue)] font-medium text-xs">
                              {group.attributes.length} {isRTL ? "عنصر بيانات" : "data attributes"}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            onEditGroup(group.id);
                          }}
                          className="h-10 w-10 p-0 hover:bg-[var(--brand-blue)]/10 hover:text-[var(--brand-blue)] rounded-xl"
                        >
                          <Edit className="w-5 h-5" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            onDeleteGroup(group.id);
                          }}
                          className="h-10 w-10 p-0 hover:bg-red-50 hover:text-red-600 rounded-xl"
                        >
                          <Trash2 className="w-5 h-5" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Full Width Expanded Content */}
                <div className="bg-gradient-to-br from-gray-50/30 to-white border-x border-b border-gray-200/60 rounded-b-3xl shadow-xl">
                  <div className="p-8">
                    {/* Enhanced Group Summary */}
                    <div className="mb-6 p-4 bg-gradient-to-r from-[var(--brand-blue)]/5 to-[var(--brand-blue)]/10 rounded-2xl border border-[var(--brand-blue)]/20">
                      <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2 text-base">
                        <Database className="w-4 h-4 text-[var(--brand-blue)]" />
                        {isRTL ? "وصف المجموعة التفصيلي" : "Detailed Group Description"}
                      </h4>
                      <p className="text-gray-700 leading-relaxed text-sm">
                        {group.description}
                      </p>
                    </div>

                    {/* Full Width Attributes Table */}
                    <div className="bg-white rounded-2xl border border-gray-200/60 shadow-lg overflow-hidden">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="bg-gradient-to-r from-[var(--brand-blue)]/5 to-[var(--brand-blue)]/10 border-b border-gray-200">
                              <th className="text-left p-3 font-semibold text-[var(--brand-blue)] text-xs">
                                {isRTL ? "الجدول" : "Table"}
                              </th>
                              <th className="text-left p-3 font-semibold text-[var(--brand-blue)] text-xs">
                                {isRTL ? "العمود" : "Column"}
                              </th>
                              <th className="text-left p-3 font-semibold text-[var(--brand-blue)] text-xs">
                                {isRTL ? "سبب التجميع" : "Grouping Reason"}
                              </th>
                              <th className="text-left p-3 font-semibold text-[var(--brand-blue)] text-xs">
                                {isRTL ? "نوع البيانات" : "Data Type"}
                              </th>
                              <th className="text-left p-3 font-semibold text-[var(--brand-blue)] text-xs">
                                {isRTL ? "نوع البيانات الشخصية" : "Personal Data Type"}
                              </th>
                              <th className="text-left p-3 font-semibold text-[var(--brand-blue)] text-xs">
                                {isRTL ? "الفئة الخاصة" : "Special Category"}
                              </th>
                              <th className="text-left p-3 font-semibold text-[var(--brand-blue)] text-xs">
                                {isRTL ? "مستوى السرية" : "Confidentiality"}
                              </th>
                              <th className="text-left p-3 font-semibold text-[var(--brand-blue)] text-xs">
                                {isRTL ? "الإجراءات" : "Actions"}
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {groupAttributes.map((attr, index) => {
                              // Find the 8-word reason for this attribute
                              const reason = group.attributeReasons?.find(r => r.attributeId === attr.id)?.reason || `stores ${attr.columnName} data for ${group.name.toLowerCase()} business operations`;

                              return (
                                <tr key={index} className="border-b border-gray-100 hover:bg-gradient-to-r hover:from-[var(--brand-blue)]/5 hover:to-transparent transition-colors duration-300">
                                  <td className="p-3">
                                    <div className="flex items-center gap-2">
                                      <Table className="w-4 h-4 text-[var(--brand-blue)]" />
                                      <span className="font-medium text-gray-900 text-sm">{attr.tableName}</span>
                                    </div>
                                  </td>
                                  <td className="p-3">
                                    <span className="text-gray-900 font-medium text-sm">{attr.columnName}</span>
                                  </td>
                                  <td className="p-3">
                                    <div className="flex items-center gap-2">
                                      <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full"></div>
                                      <span className="text-xs text-gray-700 italic font-medium">{reason}</span>
                                    </div>
                                  </td>
                                  <td className="p-3">
                                    <Badge variant="outline" className="text-xs font-medium border-gray-300">
                                      {attr.dataType}
                                    </Badge>
                                  </td>
                                  <td className="p-3">
                                    {attr.personalDataType && (
                                      <Badge className={`text-xs font-medium ${getPersonalDataTypeColor(attr.personalDataType)}`}>
                                        {attr.personalDataType === 'direct' ? (isRTL ? "مباشر" : "Direct") :
                                         attr.personalDataType === 'indirect' ? (isRTL ? "غير مباشر" : "Indirect") :
                                         attr.personalDataType === 'pseudonymous' ? (isRTL ? "مستعار" : "Pseudonymous") :
                                         (isRTL ? "مجهول" : "Anonymous")}
                                      </Badge>
                                    )}
                                  </td>
                                  <td className="p-3">
                                    {attr.specialCategoryType && attr.specialCategoryType !== 'none' && (
                                      <Badge className={`text-xs font-medium ${getSpecialCategoryColor(attr.specialCategoryType)}`}>
                                        {attr.specialCategoryType}
                                      </Badge>
                                    )}
                                  </td>
                                  <td className="p-3">
                                    {attr.confidentialityLevel && (
                                      <Badge className={`text-xs font-medium ${getConfidentialityColor(attr.confidentialityLevel)}`}>
                                        {isRTL ? (
                                          attr.confidentialityLevel === 'Public' ? 'عام' :
                                          attr.confidentialityLevel === 'Confidential' ? 'سري' :
                                          attr.confidentialityLevel === 'Secret' ? 'سري جداً' : 'سري للغاية'
                                        ) : attr.confidentialityLevel}
                                      </Badge>
                                    )}
                                  </td>
                                  <td className="p-5">
                                    {groups.length > 1 && onTransferAttribute && (
                                      <div className="flex items-center gap-1">
                                        {transferMode?.attributeId === attr.id ? (
                                          <div className="flex items-center gap-1">
                                            <select
                                              className="text-xs p-1 border border-gray-300 rounded"
                                              onChange={(e) => {
                                                if (e.target.value && transferMode) {
                                                  onTransferAttribute(attr.id, transferMode.fromGroupId, e.target.value);
                                                  setTransferMode(null);
                                                }
                                              }}
                                              defaultValue=""
                                            >
                                              <option value="">
                                                {isRTL ? "اختر المجموعة" : "Select group"}
                                              </option>
                                              {groups
                                                .filter(g => g.id !== group.id)
                                                .map(g => (
                                                  <option key={g.id} value={g.id}>
                                                    {g.name}
                                                  </option>
                                                ))
                                              }
                                            </select>
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => setTransferMode(null)}
                                              className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
                                            >
                                              <X className="w-3 h-3" />
                                            </Button>
                                          </div>
                                        ) : (
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => setTransferMode({attributeId: attr.id, fromGroupId: group.id})}
                                            className="h-6 w-6 p-0 text-gray-400 hover:text-[var(--brand-blue)]"
                                            title={isRTL ? "نقل إلى مجموعة أخرى" : "Transfer to another group"}
                                          >
                                            <Move className="w-3 h-3" />
                                          </Button>
                                        )}
                                      </div>
                                    )}
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        // Grid Layout for Collapsed Cards
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-8">
          {groups.map((group) => {
            // const groupAttributes = group.attributes.map(attr => dataLookup.get(attr)).filter(Boolean);

            return (
              <div key={group.id} className="group relative">
                <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-3xl border border-gray-200/60 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-1">
                  <div
                    className="cursor-pointer p-6 hover:bg-gradient-to-br hover:from-[var(--brand-blue)]/5 hover:to-[var(--brand-blue)]/10 transition-all duration-300"
                    onClick={() => handleGroupToggle(group.id)}
                  >
                    {/* Header Section */}
                    <div className="flex items-start justify-between mb-4">
                      {/* Icon and Title */}
                      <div className="flex items-start gap-4">
                        <div className="relative">
                          <div className="w-12 h-12 bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/90 to-[var(--brand-blue)]/80 rounded-2xl flex items-center justify-center shadow-lg">
                            <Database className="w-6 h-6 text-white" />
                          </div>
                          <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg border-2 border-white">
                            <span className="text-xs font-bold text-white">{group.attributes.length}</span>
                          </div>
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="text-base font-bold text-gray-900 truncate">
                              {group.name}
                            </h3>
                            <ChevronRight className="w-4 h-4 text-gray-400 flex-shrink-0" />
                          </div>

                          {/* Attribute Count Badge */}
                          <div className="flex items-center gap-2 mb-3">
                            <div className="px-2 py-1 bg-gradient-to-r from-[var(--brand-blue)]/10 to-[var(--brand-blue)]/5 rounded-full border border-[var(--brand-blue)]/20">
                              <span className="text-[var(--brand-blue)] font-medium text-xs">
                                {group.attributes.length} {isRTL ? "عنصر" : "attributes"}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            onEditGroup(group.id);
                          }}
                          className="h-10 w-10 p-0 hover:bg-[var(--brand-blue)]/10 hover:text-[var(--brand-blue)] rounded-xl"
                        >
                          <Edit className="w-5 h-5" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            onDeleteGroup(group.id);
                          }}
                          className="h-10 w-10 p-0 hover:bg-red-50 hover:text-red-600 rounded-xl"
                        >
                          <Trash2 className="w-5 h-5" />
                        </Button>
                      </div>
                    </div>

                    {/* Description */}
                    <div className="mb-4">
                      <p className="text-gray-600 leading-relaxed line-clamp-3 text-sm">
                        {group.description}
                      </p>
                    </div>

                    {/* View Details Button */}
                    <div className="flex items-center justify-center">
                      <div className="px-4 py-2 bg-gradient-to-r from-gray-100 to-gray-50 rounded-xl border border-gray-200 text-gray-700 font-medium text-xs hover:from-[var(--brand-blue)]/10 hover:to-[var(--brand-blue)]/5 hover:border-[var(--brand-blue)]/20 hover:text-[var(--brand-blue)] transition-all duration-300">
                        {isRTL ? "عرض التفاصيل" : "View Details"}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
