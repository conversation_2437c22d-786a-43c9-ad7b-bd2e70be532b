"use client";

import React from "react";
import Image from "next/image";
import { Locale } from "@/i18n-config";
import { motion } from "framer-motion";

interface FuturisticHeroProps {
  userName: string | null;
  lang: Locale;
}

export function FuturisticHero({ userName, lang }: FuturisticHeroProps) {
  const isRTL = lang === "ar";
  const direction = isRTL ? "rtl" : "ltr";
  
  return (
    <div 
      className="relative min-h-screen w-full overflow-hidden fixed inset-0"
      style={{ direction }}
    >
      {/* Animated Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#1a1a20] via-[var(--brand-dark-gray)] to-[#1a1a20] z-0">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_10%_20%,rgba(35,169,219,0.4)_0%,transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_80%_80%,rgba(35,169,219,0.4)_0%,transparent_50%)]"></div>
        </div>
      </div>
      
      {/* Animated Grid Pattern */}
      <div className="absolute inset-0 z-0 opacity-10">
        <div className="h-full w-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMyM0E5REIiIGZpbGwtb3BhY2l0eT0iMC40Ij48cGF0aCBkPSJNMzYgMzRoLTJ2LTRoMnY0em0wLTZ2LTRoLTJ2NGgyek0yNCAzNGgtMnYtNGgydjR6bTAtNnYtNGgtMnY0aDJ6Ii8+PC9nPjwvZz48L3N2Zz4=')]"></div>
      </div>
      
      {/* Floating Thiqah Logo Watermark */}
      <div className="absolute inset-0 z-0 flex items-center justify-center opacity-5">
        <div className={`animate-float-slow ${isRTL ? "ml-0" : "mr-0"}`}>
          <Image 
            src="/thiqah-logo.png" 
            alt="Thiqah Logo" 
            width={600} 
            height={600}
            className="opacity-30"
          />
        </div>
      </div>
      
      {/* Decorative Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 rounded-full bg-[var(--brand-blue)]/10 blur-xl animate-float"></div>
      <div className="absolute bottom-20 right-10 w-40 h-40 rounded-full bg-[var(--brand-blue)]/10 blur-xl animate-float" style={{animationDelay: "1s"}}></div>
      <div className="absolute top-1/3 right-1/4 w-24 h-24 rounded-full bg-[var(--brand-blue)]/5 blur-xl animate-float" style={{animationDelay: "2s"}}></div>
      
      {/* Animated Infographic Elements */}
      <div className={`absolute top-28 ${isRTL ? 'left-12' : 'right-12'} hidden lg:block`}>
        <motion.div 
          className="w-48 h-48 relative"
          initial={{ opacity: 0, rotate: -10 }}
          animate={{ opacity: 1, rotate: 0 }}
          transition={{ duration: 0.8, delay: 1.5 }}
        >
          <svg viewBox="0 0 100 100" className="w-full h-full">
            <motion.circle 
              cx="50" 
              cy="50" 
              r="45" 
              fill="none" 
              stroke="rgba(255,255,255,0.1)" 
              strokeWidth="2"
            />
            <motion.path
              d="M50 5 A 45 45 0 0 1 95 50"
              fill="none"
              stroke="var(--brand-blue)"
              strokeWidth="4"
              strokeLinecap="round"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 0.85 }}
              transition={{ duration: 2, delay: 2 }}
            />
            <motion.text 
              x="50" 
              y="50" 
              textAnchor="middle" 
              dominantBaseline="middle" 
              fill="white" 
              fontSize="12"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 2.5 }}
            >
              {isRTL ? "85%" : "85%"}
            </motion.text>
            <motion.text 
              x="50" 
              y="65" 
              textAnchor="middle" 
              dominantBaseline="middle" 
              fill="white" 
              fontSize="6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.7 }}
              transition={{ duration: 1, delay: 2.5 }}
            >
              {isRTL ? "إنجاز المشاريع" : "PROJECT COMPLETION"}
            </motion.text>
          </svg>
        </motion.div>
      </div>
      
      <div className={`absolute bottom-40 ${isRTL ? 'right-12' : 'left-12'} hidden lg:block`}>
        <motion.div 
          className="w-40 h-40 relative"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 1.8 }}
        >
          <svg viewBox="0 0 100 100" className="w-full h-full">
            <rect x="10" y="30" width="80" height="50" rx="3" fill="rgba(255,255,255,0.03)" stroke="rgba(255,255,255,0.1)" strokeWidth="1" />
            <motion.rect 
              x="20" 
              y="40" 
              width="15" 
              height="30" 
              fill="rgba(35,169,219,0.6)"
              initial={{ height: 0, y: 70 }}
              animate={{ height: 30, y: 40 }}
              transition={{ duration: 1, delay: 2.2 }}
            />
            <motion.rect 
              x="42.5" 
              y="35" 
              width="15" 
              height="35" 
              fill="rgba(35,169,219,0.8)"
              initial={{ height: 0, y: 70 }}
              animate={{ height: 35, y: 35 }}
              transition={{ duration: 1, delay: 2.4 }}
            />
            <motion.rect 
              x="65" 
              y="25" 
              width="15" 
              height="45" 
              fill="rgba(35,169,219,1)"
              initial={{ height: 0, y: 70 }}
              animate={{ height: 45, y: 25 }}
              transition={{ duration: 1, delay: 2.6 }}
            />
            <motion.text 
              x="50" 
              y="90" 
              textAnchor="middle" 
              dominantBaseline="middle" 
              fill="white" 
              fontSize="6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.7 }}
              transition={{ duration: 1, delay: 2.7 }}
            >
              {isRTL ? "تقدم المهام" : "TASK PROGRESS"}
            </motion.text>
          </svg>
        </motion.div>
      </div>
      
      {/* Futuristic Lines */}
      <div className="absolute inset-0 z-0">
        <motion.div 
          className="absolute top-0 left-0 h-[1px] bg-gradient-to-r from-transparent via-[var(--brand-blue)]/30 to-transparent"
          initial={{ width: 0 }}
          animate={{ width: "100%" }}
          transition={{ duration: 2, delay: 0.5 }}
        />
        <motion.div 
          className="absolute bottom-0 right-0 h-[1px] bg-gradient-to-l from-transparent via-[var(--brand-blue)]/30 to-transparent"
          initial={{ width: 0 }}
          animate={{ width: "100%" }}
          transition={{ duration: 2, delay: 0.7 }}
        />
        <motion.div 
          className="absolute top-0 right-0 w-[1px] bg-gradient-to-b from-transparent via-[var(--brand-blue)]/30 to-transparent"
          initial={{ height: 0 }}
          animate={{ height: "100%" }}
          transition={{ duration: 2, delay: 0.9 }}
        />
        <motion.div 
          className="absolute bottom-0 left-0 w-[1px] bg-gradient-to-t from-transparent via-[var(--brand-blue)]/30 to-transparent"
          initial={{ height: 0 }}
          animate={{ height: "100%" }}
          transition={{ duration: 2, delay: 1.1 }}
        />
      </div>
      
      {/* Main Content */}
      <div className="relative z-10 container mx-auto px-4 py-16 h-screen flex flex-col justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="max-w-4xl mx-auto"
        >
          {/* Welcome Message */}
          <div className="text-center mb-8">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="mb-2"
            >
              <Image 
                src="/thiqah-logo.png" 
                alt="Thiqah Logo" 
                width={120} 
                height={120}
                className="mx-auto"
              />
            </motion.div>
            
            <motion.h1 
              className="text-4xl md:text-6xl font-bold text-white mb-4"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.7, delay: 0.2, type: "spring", stiffness: 100 }}
            >
              {isRTL ? 'مرحباً' : 'Welcome'} {userName ? userName : ''}
            </motion.h1>
            
            <motion.p 
              className="text-xl text-[var(--brand-blue)] mb-3"
              initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              {isRTL 
                ? 'منصة ثقة لإدارة المشاريع الاستشارية بكفاءة' 
                : 'Thiqah Portal for Efficient Consultancy Management'}
            </motion.p>
            
            <motion.p 
              className="text-md text-white/70 max-w-2xl mx-auto mb-8"
              initial={{ opacity: 0, x: isRTL ? -20 : 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              {isRTL 
                ? 'تتبع جميع تفاصيل المشروع، المخرجات، والجداول الزمنية بدقة عالية مع دعم الذكاء الاصطناعي' 
                : 'Track all project details, deliverables, and timelines with precision, powered by AI'}
            </motion.p>
          </div>
          
          {/* Feature Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[
              {
                title: isRTL ? "تتبع المخرجات" : "Track Deliverables",
                description: isRTL 
                  ? "إدارة ومتابعة جميع مخرجات المشروع بدقة ووضوح" 
                  : "Manage and monitor all project deliverables with precision",
                delay: 0.6,
                icon: (
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                  </svg>
                ),
                animationPath: "M0,0 L20,0 L20,20 L0,20 Z"
              },
              {
                title: isRTL ? "خطة المشروع" : "Project Plan",
                description: isRTL 
                  ? "تخطيط وإدارة مراحل المشروع والجداول الزمنية" 
                  : "Plan and manage project phases and timelines",
                delay: 0.8,
                icon: (
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                ),
                animationPath: "M0,10 Q10,0 20,10 Q30,20 40,10"
              },
              {
                title: isRTL ? "إدارة الفريق" : "Team Management",
                description: isRTL 
                  ? "تنسيق عمل الفريق وتوزيع المهام ومتابعة الإنجاز" 
                  : "Coordinate team efforts and track task completion",
                delay: 1.0,
                icon: (
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                ),
                animationPath: "M0,20 C10,10 10,30 20,20 C30,10 30,30 40,20"
              },
              {
                title: isRTL ? "دعم الذكاء الاصطناعي" : "AI Assistance",
                description: isRTL 
                  ? "الاستفادة من الذكاء الاصطناعي في تحليل وإدارة المشاريع" 
                  : "Leverage AI for project analysis and management",
                delay: 1.2,
                icon: (
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                ),
                animationPath: "M0,20 Q20,0 40,20"
              }
            ].map((card, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ 
                  duration: 0.7, 
                  delay: card.delay,
                  type: "spring",
                  stiffness: 80
                }}
                whileHover={{ 
                  y: -10,
                  transition: { duration: 0.3 } 
                }}
                className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6 hover:bg-white/10 transition-all duration-300 hover:shadow-lg hover:shadow-[var(--brand-blue)]/20 group relative overflow-hidden"
              >
                {/* Animated path background */}
                <motion.div 
                  className="absolute -bottom-6 -left-6 w-full h-full opacity-10 z-0"
                  initial={{ pathLength: 0, opacity: 0 }}
                  animate={{ 
                    pathLength: 1, 
                    opacity: 0.2,
                    transition: { duration: 2, repeat: Infinity, repeatType: "reverse" } 
                  }}
                >
                  <svg width="100%" height="100%" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                    <motion.path
                      d={card.animationPath}
                      fill="none"
                      stroke="var(--brand-blue)"
                      strokeWidth="2"
                      initial={{ pathLength: 0 }}
                      animate={{ 
                        pathLength: 1,
                        transition: { duration: 2, repeat: Infinity, repeatType: "reverse", delay: index * 0.2 } 
                      }}
                    />
                  </svg>
                </motion.div>
                
                <div className="relative z-10">
                  <div className="h-14 w-14 rounded-full bg-gradient-to-br from-[var(--brand-blue)] to-[var(--brand-blue)]/30 flex items-center justify-center mb-5 group-hover:scale-110 transition-transform duration-300 shadow-lg shadow-[var(--brand-blue)]/10">
                    {card.icon}
                  </div>
                  <h3 className="text-xl font-bold text-white mb-3">{card.title}</h3>
                  <p className="text-gray-300">{card.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
          
          {/* CTA Button */}
          <motion.div 
            className="mt-14 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 1.4 }}
          >
            <motion.button 
              className="px-8 py-4 bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 text-white rounded-full font-bold text-lg shadow-lg shadow-[var(--brand-blue)]/20 relative overflow-hidden group"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.98 }}
            >
              <span className="relative z-10">
                {isRTL ? 'بدء إدارة المشاريع' : 'Start Managing Projects'}
              </span>
              <motion.div 
                className="absolute inset-0 bg-gradient-to-r from-[var(--brand-blue)]/90 to-[var(--brand-blue)]/60"
                initial={{ x: "-100%" }}
                whileHover={{ x: "0%" }}
                transition={{ duration: 0.4 }}
              />
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
      
      {/* Brands/Partners & Tools Section */}
      <div className="absolute bottom-0 left-0 right-0 bg-black/40 backdrop-blur-sm py-6 z-10">
        <div className="container mx-auto">
          <motion.div 
            className="flex flex-col justify-center items-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.5 }}
          >
            <motion.div
              className="w-full max-w-3xl mx-auto mb-6 flex flex-col md:flex-row justify-between items-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 1.7 }}
            >
              <div className="flex flex-col items-center md:items-start mb-4 md:mb-0">
                <h4 className="text-white text-lg font-semibold mb-1">
                  {isRTL ? 'الإتمام الفعال للمشاريع' : 'Efficient Project Completion'}
                </h4>
                <p className="text-white/70 text-sm">
                  {isRTL ? 'منصة ثقة تساعدك على تحقيق النجاح في مشاريعك' : 'Thiqah platform helps you achieve project success'}
                </p>
              </div>
              
              <div className={`flex ${isRTL ? 'space-x-reverse space-x-3' : 'space-x-3'}`}>
                <motion.div 
                  className="h-8 w-8 rounded-full bg-[var(--brand-blue)]/20 flex items-center justify-center"
                  whileHover={{ scale: 1.2, backgroundColor: 'rgba(35,169,219,0.4)' }}
                >
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z" />
                  </svg>
                </motion.div>
                <motion.div 
                  className="h-8 w-8 rounded-full bg-[var(--brand-blue)]/20 flex items-center justify-center"
                  whileHover={{ scale: 1.2, backgroundColor: 'rgba(35,169,219,0.4)' }}
                >
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                  </svg>
                </motion.div>
                <motion.div 
                  className="h-8 w-8 rounded-full bg-[var(--brand-blue)]/20 flex items-center justify-center"
                  whileHover={{ scale: 1.2, backgroundColor: 'rgba(35,169,219,0.4)' }}
                >
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                  </svg>
                </motion.div>
              </div>
            </motion.div>
            
            <p className="text-white/70 text-sm font-medium w-full text-center mb-4">
              {isRTL ? 'الأدوات والتقنيات المتكاملة' : 'Integrated Tools & Technologies'}
            </p>
            
            <div className="flex flex-wrap justify-center items-center gap-5 md:gap-8">
              {[
                { name: 'Jira', logo: '🔍' },
                { name: 'MS Project', logo: '📅' },
                { name: 'Slack', logo: '💬' },
                { name: 'GitHub', logo: '🔧' },
                { name: 'Tableau', logo: '📈' },
                { name: 'AI Assistant', logo: '🤖' }
              ].map((tool, index) => (
                <motion.div 
                  key={tool.name} 
                  className="h-12 backdrop-blur-sm bg-white/10 rounded-full px-4 flex items-center justify-center border border-[var(--brand-blue)]/20 group"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1.8 + (index * 0.1) }}
                  whileHover={{ 
                    backgroundColor: 'rgba(255,255,255,0.15)', 
                    borderColor: 'rgba(35,169,219,0.5)',
                    y: -5,
                    transition: { duration: 0.2 }
                  }}
                >
                  <span className={`${isRTL ? 'ml-2' : 'mr-2'} text-lg`}>{tool.logo}</span>
                  <span className="text-white/90 text-sm font-medium group-hover:text-white">
                    {tool.name}
                  </span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
