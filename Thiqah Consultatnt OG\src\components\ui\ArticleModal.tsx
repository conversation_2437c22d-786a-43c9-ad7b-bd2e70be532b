"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, BookOpen, ChevronDown, Scale, Calendar } from 'lucide-react';
import { Button } from './button';
import { Locale } from '@/i18n-config';
import { PDPLPoint } from '@/Firebase/firestore/services/PDPLService';
import { Timestamp } from 'firebase/firestore';

interface ArticleModalProps {
  isOpen: boolean;
  onClose: () => void;
  article: {
    id?: string;
    articleNumber: string;
    title: string;
    description: string;
    points?: PDPLPoint[];
    linkedArticles?: string[];
    createdAt: Timestamp | Date | string;
    updatedAt: Timestamp | Date | string;
  };
  lang: Locale;
  selectedPointId?: string;
}

export function ArticleModal({ isOpen, onClose, article, lang, selectedPointId }: ArticleModalProps) {
  const [expandedPoints, setExpandedPoints] = useState<Set<string>>(
    selectedPointId ? new Set([selectedPointId]) : new Set()
  );
  const isRTL = lang === "ar";

  const togglePointExpansion = (pointId: string) => {
    setExpandedPoints(prev => {
      const newSet = new Set(prev);
      if (newSet.has(pointId)) {
        newSet.delete(pointId);
      } else {
        newSet.add(pointId);
      }
      return newSet;
    });
  };

  const formatDate = (date: Timestamp | Date | string) => {
    if (!date) return '';
    try {
      const dateObj = (date as Timestamp).toDate ? (date as Timestamp).toDate() : new Date(date as string);
      return dateObj.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US');
    } catch {
      return '';
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />

        {/* Modal Content */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.3 }}
          className="relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 text-white p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <Scale className="w-5 h-5" />
                </div>
                <div>
                  <h2 className="text-xl font-bold">
                    {article.articleNumber}
                  </h2>
                  <p className="text-blue-100 text-sm">
                    {article.title}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-white hover:bg-white/20 rounded-full p-2"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
            {article.points && article.points.length > 0 ? (
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-6">
                  <BookOpen className="w-5 h-5 text-[var(--brand-blue)]" />
                  <h3 className="text-lg font-bold text-gray-900">
                    {isRTL ? "نقاط المادة" : "Article Points"} ({article.points.length})
                  </h3>
                </div>

                {article.points.map((point, index) => (
                  <div key={point.id} className="bg-gradient-to-r from-blue-50 to-blue-50/50 rounded-xl border border-blue-200/50 overflow-hidden">
                    <div
                      className="p-4 cursor-pointer hover:bg-blue-100/50 transition-colors"
                      onClick={() => togglePointExpansion(point.id)}
                    >
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-[var(--brand-blue)] text-white rounded-full flex items-center justify-center text-sm font-bold mt-0.5 flex-shrink-0">
                          {point.order || index + 1}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <p className="text-gray-800 leading-relaxed font-medium">
                              {expandedPoints.has(point.id) ? point.content : `${point.content.substring(0, 150)}${point.content.length > 150 ? '...' : ''}`}
                            </p>
                            <motion.div
                              animate={{ rotate: expandedPoints.has(point.id) ? 180 : 0 }}
                              transition={{ duration: 0.2 }}
                              className="ml-2"
                            >
                              <ChevronDown className="w-5 h-5 text-gray-500" />
                            </motion.div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <AnimatePresence>
                      {expandedPoints.has(point.id) && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="px-4 pb-4"
                        >
                          {/* Full content when expanded */}
                          {expandedPoints.has(point.id) && (
                            <div className="ml-12 mb-4">
                              <p className="text-gray-800 leading-relaxed">
                                {point.content}
                              </p>
                            </div>
                          )}
                          
                          {/* Sub-points */}
                          {point.subPoints && point.subPoints.length > 0 && (
                            <div className="ml-12 space-y-3 pl-4 border-l-2 border-blue-200">
                              <h4 className="text-sm font-semibold text-gray-700 mb-2">
                                {isRTL ? "النقاط الفرعية:" : "Sub-points:"}
                              </h4>
                              {point.subPoints.map((subPoint, subIndex) => (
                                <div key={subPoint.id} className="flex items-start gap-3">
                                  <div className="w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5 flex-shrink-0">
                                    {subPoint.order || String.fromCharCode(97 + subIndex)}
                                  </div>
                                  <p className="text-gray-700 leading-relaxed text-sm">
                                    {subPoint.content}
                                  </p>
                                </div>
                              ))}
                            </div>
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <BookOpen className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium mb-2">
                  {isRTL ? "لا توجد نقاط محددة" : "No Points Defined"}
                </p>
                <p className="text-sm">
                  {isRTL ? "لا توجد نقاط محددة لهذه المادة" : "No points are defined for this article"}
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Scale className="w-4 h-4" />
                <span className="font-medium">
                  {isRTL ? "النظام الأساسي لحماية البيانات الشخصية" : "Personal Data Protection Law Framework"}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>
                  {isRTL ? "آخر تحديث" : "Last Updated"}: {formatDate(article.updatedAt)}
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
