"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Plus, Trash2, MessageSquare } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { SystemsService, SystemContextPoint } from "@/Firebase/firestore/SystemsService";

interface ContextModalProps {
  isOpen: boolean;
  onClose: () => void;
  systemId: string;
  isRTL: boolean;
}

export function ContextModal({ isOpen, onClose, systemId, isRTL }: ContextModalProps) {
  const [contextPoints, setContextPoints] = useState<SystemContextPoint[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [newContent, setNewContent] = useState("");
  const [newTag, setNewTag] = useState("general");
  
  const { toast } = useToast();

  const loadContextPoints = useCallback(async () => {
    try {
      setIsLoading(true);
      const points = await SystemsService.getSystemContextPoints(systemId);
      setContextPoints(points);
    } catch {
      console.error('Error loading context points');
    } finally {
      setIsLoading(false);
    }
  }, [systemId]);

  useEffect(() => {
    if (isOpen && systemId) {
      loadContextPoints();
    }
  }, [isOpen, systemId, loadContextPoints]);

  const handleQuickAdd = async () => {
    if (!newContent.trim()) {
      toast({
        title: isRTL ? "يرجى كتابة المحتوى" : "Please enter content",
        variant: "destructive",
      });
      return;
    }

    try {
      await SystemsService.addSystemContextPoint(systemId, newContent.trim(), newTag);
      setNewContent("");
      setNewTag("general");
      await loadContextPoints();
      toast({
        title: isRTL ? "تم الحفظ!" : "Saved!",
        description: isRTL ? "تم إضافة المعلومة" : "Information added",
      });
    } catch {
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: "Failed to save",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (pointId: string) => {
    try {
      await SystemsService.deleteSystemContextPoint(systemId, pointId);
      await loadContextPoints();
      toast({
        title: isRTL ? "تم الحذف" : "Deleted",
      });
    } catch {
      toast({
        title: isRTL ? "خطأ" : "Error",
        variant: "destructive",
      });
    }
  };

  const getTagLabel = (tag: string) => {
    const tags: Record<string, string> = {
      general: isRTL ? "عام" : "General",
      data: isRTL ? "بيانات" : "Data",
      access: isRTL ? "وصول" : "Access",
      security: isRTL ? "أمان" : "Security",
      compliance: isRTL ? "امتثال" : "Compliance",
      business: isRTL ? "أعمال" : "Business"
    };
    return tags[tag] || tag;
  };

  const getTagColor = (tag: string) => {
    const colors: Record<string, string> = {
      general: "bg-gray-100 text-gray-700",
      data: "bg-blue-100 text-blue-700", 
      access: "bg-green-100 text-green-700",
      security: "bg-red-100 text-red-700",
      compliance: "bg-orange-100 text-orange-700",
      business: "bg-purple-100 text-purple-700"
    };
    return colors[tag] || "bg-gray-100 text-gray-700";
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`max-w-3xl ${isRTL ? "rtl" : "ltr"}`}>
        {/* Simple Header */}
        <DialogHeader>
          <DialogTitle className="text-xl font-bold flex items-center gap-2">
            <MessageSquare className="w-5 h-5 text-[var(--brand-blue)]" />
            {isRTL ? "معلومات النظام" : "System Information"}
          </DialogTitle>
        </DialogHeader>

        {/* Super Simple Add Section - Always Visible */}
        <div className="bg-[var(--brand-blue)]/5 rounded-lg p-4 space-y-3">
          <div className="flex gap-2">
            <select
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              className="px-3 py-2 border rounded-lg text-sm bg-white min-w-[120px]"
            >
              <option value="general">{isRTL ? "عام" : "General"}</option>
              <option value="data">{isRTL ? "بيانات" : "Data"}</option>
              <option value="access">{isRTL ? "وصول" : "Access"}</option>
              <option value="security">{isRTL ? "أمان" : "Security"}</option>
              <option value="compliance">{isRTL ? "امتثال" : "Compliance"}</option>
              <option value="business">{isRTL ? "أعمال" : "Business"}</option>
            </select>
            <Button
              onClick={handleQuickAdd}
              disabled={!newContent.trim()}
              className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white px-6"
            >
              <Plus className="w-4 h-4 mr-1" />
              {isRTL ? "إضافة" : "Add"}
            </Button>
          </div>
          
          <textarea
            value={newContent}
            onChange={(e) => setNewContent(e.target.value)}
            placeholder={isRTL ? "اكتب أي معلومة مفيدة عن النظام..." : "Write any helpful information about the system..."}
            className="w-full h-20 p-3 border rounded-lg resize-none text-sm"
            maxLength={200}
          />
          
          <div className="text-xs text-gray-500 text-right">
            {newContent.length}/200 {isRTL ? "حرف" : "characters"}
          </div>
        </div>

        {/* Simple List */}
        <div className="max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="text-center py-8 text-gray-500">
              {isRTL ? "جاري التحميل..." : "Loading..."}
            </div>
          ) : contextPoints.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <MessageSquare className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p>{isRTL ? "لا توجد معلومات بعد" : "No information yet"}</p>
              <p className="text-sm">{isRTL ? "أضف أول معلومة أعلاه" : "Add your first note above"}</p>
            </div>
          ) : (
            <div className="space-y-3">
              {contextPoints.map((point) => (
                <div
                  key={point.id}
                  className="bg-white border rounded-lg p-4 hover:shadow-sm transition-shadow"
                >
                  <div className="flex justify-between items-start mb-2">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getTagColor(point.tag)}`}>
                      {getTagLabel(point.tag)}
                    </span>
                    <Button
                      onClick={() => handleDelete(point.id!)}
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 text-gray-400 hover:text-red-600"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                  <p className="text-gray-800 text-sm leading-relaxed">{point.content}</p>
                  <div className="text-xs text-gray-400 mt-2">
                    {point.createdAt.toDate().toLocaleDateString(
                      isRTL ? "ar-SA" : "en-US",
                      { 
                        month: 'short', 
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      }
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Simple Footer */}
        <div className="flex justify-between items-center pt-4 border-t">
          <span className="text-sm text-gray-500">
            {contextPoints.length} {isRTL ? "معلومة" : "notes"}
          </span>
          <Button onClick={onClose} variant="outline" size="sm">
            {isRTL ? "إغلاق" : "Close"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 