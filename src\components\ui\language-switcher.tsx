'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { i18n, type Locale } from '@/i18n-config'; // Using alias @/
import { Button } from "@/components/ui/button"; // Assuming Button component exists

export default function LanguageSwitcher() {
  const pathname = usePathname();

  const getPathWithLocale = (locale: Locale) => {
    if (!pathname) return '/';
    const segments = pathname.split('/');
    segments[1] = locale;
    return segments.join('/');
  };

  return (
    <div className="flex gap-2">
      {i18n.locales.map((locale) => {
        const isActive = pathname.split('/')[1] === locale;
        return (
          <Link key={locale} href={getPathWithLocale(locale)}>
            <Button variant={isActive ? "secondary" : "ghost"} size="sm">
              {locale.toUpperCase()}
            </Button>
          </Link>
        );
      })}
    </div>
  );
} 