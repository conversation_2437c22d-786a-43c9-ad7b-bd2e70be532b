"use client";

import React from "react";
import { motion } from "framer-motion";
import { System } from "@/Firebase/firestore/SystemsService";
import { Locale } from "@/i18n-config";
import { User, Mail, Database, Trash2, Edit, Building, UserCheck } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

interface SystemCardProps {
  system: System;
  lang: Locale;
  onEdit?: (system: System) => void;
  onDelete?: (systemId: string) => void;
}

export function SystemCard({ system, lang, onEdit, onDelete }: SystemCardProps) {
  const isRTL = lang === "ar";
  const router = useRouter();

  const formatDate = (timestamp: { toDate?: () => Date } | Date | string | null | undefined) => {
    if (!timestamp) return "";
    const date = timestamp && typeof timestamp === 'object' && 'toDate' in timestamp && timestamp.toDate
      ? timestamp.toDate() 
      : new Date(timestamp as string | Date);
    return date.toLocaleDateString(lang === "ar" ? "ar-SA" : "en-US");
  };

  const handleCardClick = () => {
    router.push(`/${lang}/Thiqah/DataClassification/${system.id}`);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      onClick={handleCardClick}
      className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 overflow-hidden cursor-pointer transform hover:scale-[1.02]"
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-[var(--brand-blue)]/10 to-[var(--brand-blue)]/5 px-6 py-4 border-b border-[var(--brand-blue)]/20">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-gray-800 truncate">
            {system.name}
          </h3>
          <div className="flex items-center gap-2">
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(system);
                }}
                className="text-[var(--brand-blue)] hover:text-[var(--brand-blue)]/80 hover:bg-[var(--brand-blue)]/10"
              >
                <Edit size={16} />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(system.id!);
                }}
                className="text-red-600 hover:text-red-800 hover:bg-red-50"
              >
                <Trash2 size={16} />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-4">
        {/* Responsible Owner */}
        <div className="flex items-center gap-3">
          <div className="flex-shrink-0 w-10 h-10 bg-[var(--brand-blue)]/10 rounded-full flex items-center justify-center">
            <User className="w-5 h-5 text-[var(--brand-blue)]" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-500">
              {isRTL ? "المسؤول" : "Responsible Owner"}
            </p>
            <p className="text-base font-semibold text-gray-900 truncate">
              {system.responsibleOwner}
            </p>
          </div>
        </div>

        {/* DBA */}
        <div className="flex items-center gap-3">
          <div className="flex-shrink-0 w-10 h-10 bg-[var(--brand-blue)]/10 rounded-full flex items-center justify-center">
            <Database className="w-5 h-5 text-[var(--brand-blue)]" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-500">
              {isRTL ? "مدير قاعدة البيانات" : "Database Administrator"}
            </p>
            <p className="text-base font-semibold text-gray-900 truncate">
              {system.dba}
            </p>
          </div>
        </div>

        {/* Email */}
        <div className="flex items-center gap-3">
          <div className="flex-shrink-0 w-10 h-10 bg-[var(--brand-blue)]/10 rounded-full flex items-center justify-center">
            <Mail className="w-5 h-5 text-[var(--brand-blue)]" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-500">
              {isRTL ? "البريد الإلكتروني" : "Email"}
            </p>
            <p className="text-base font-semibold text-gray-900 truncate">
              {system.email}
            </p>
          </div>
        </div>

        {/* Responsible Consultant */}
        {system.consultantName && (
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
              <UserCheck className="w-5 h-5 text-green-600" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-500">
                {isRTL ? "المستشار المسؤول" : "Responsible Consultant"}
              </p>
              <p className="text-base font-semibold text-gray-900 truncate">
                {system.consultantName}
              </p>
            </div>
          </div>
        )}

        {/* Group */}
        {system.group && (
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0 w-10 h-10 bg-[var(--brand-blue)]/10 rounded-full flex items-center justify-center">
              <Building className="w-5 h-5 text-[var(--brand-blue)]" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-500">
                {isRTL ? "المجموعة" : "Group"}
              </p>
              <p className="text-base font-semibold text-gray-900">
                {isRTL ? `المجموعة ${system.group}` : `Group ${system.group}`}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-100">
        <p className="text-xs text-gray-500">
          {isRTL ? "تم الإنشاء في:" : "Created on:"} {formatDate(system.createdAt)}
        </p>
      </div>
    </motion.div>
  );
} 