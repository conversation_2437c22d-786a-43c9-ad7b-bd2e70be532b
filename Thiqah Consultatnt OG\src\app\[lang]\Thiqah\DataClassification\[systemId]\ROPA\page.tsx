"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Locale } from "@/i18n-config";
import { useRouter } from "next/navigation";
import { ArrowLeft, ArrowRight, ClipboardList, Users, Settings, FileText, Database } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemsService, System } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";
import { PersonalDataGroupsTab } from "@/components/ROPA/PersonalDataGroupsTab";
import { ServicesTab } from "@/components/ROPA/ServicesTab";
import { QuestionnaireTab } from "@/components/ROPA/QuestionnaireTab";
import { ROPADetailsTab } from "@/components/ROPA/ROPADetailsTab";

interface ROPAPageProps {
  params: Promise<{ lang: Locale; systemId: string }>;
}

export default function ROPAPage({ params }: ROPAPageProps) {
  const [lang, setLang] = useState<string>('');
  const [systemId, setSystemId] = useState<string>('');
  const [system, setSystem] = useState<System | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'personal-data-groups' | 'services' | 'questionnaire' | 'ropa-details'>('personal-data-groups');

  const router = useRouter();
  const { toast } = useToast();

  // Initialize params
  useEffect(() => {
    params.then(({ lang, systemId }) => {
      setLang(lang);
      setSystemId(systemId);
    });
  }, [params]);

  const loadSystemDetails = useCallback(async () => {
    if (!systemId) return;
    
    try {
      setIsLoading(true);
      const systems = await SystemsService.getSystems();
      const foundSystem = systems.find(s => s.id === systemId);

      if (foundSystem) {
        setSystem(foundSystem);
      } else {
        const isRTL = lang === "ar";
        toast({
          title: isRTL ? "النظام غير موجود" : "System not found",
          description: isRTL ? "لم يتم العثور على النظام المطلوب" : "The requested system was not found",
          variant: "destructive",
        });
        router.push(`/${lang}/Thiqah/DataClassification`);
      }
    } catch (error) {
      console.error('Error loading system:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في تحميل النظام" : "Error loading system",
        description: isRTL ? "فشل في تحميل تفاصيل النظام" : "Failed to load system details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [systemId, lang, toast, router]);

  useEffect(() => {
    if (systemId) {
      loadSystemDetails();
    }
  }, [systemId, loadSystemDetails]);

  const handleGoBack = () => {
    router.push(`/${lang}/Thiqah/DataClassification/${systemId}`);
  };

  if (!lang || !systemId) {
    return <div>Loading...</div>;
  }

  const isRTL = lang === "ar";

  if (isLoading) {
    return (
      <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
        <div className="relative min-h-screen bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
          <div className="relative z-10 flex flex-col justify-center items-center min-h-screen px-8 py-16">
            <div className="animate-pulse text-center">
              <div className="w-24 h-24 bg-white/20 rounded-3xl mx-auto mb-8"></div>
              <div className="h-12 bg-white/20 rounded-lg w-96 mx-auto mb-4"></div>
              <div className="h-6 bg-white/20 rounded-lg w-64 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Tab configuration
  const tabs = [
    {
      id: 'personal-data-groups' as const,
      label: isRTL ? "مجموعات البيانات الشخصية" : "Personal Data Groups",
      icon: Users,
      description: isRTL ? "تجميع البيانات الشخصية" : "Group personal data attributes"
    },
    {
      id: 'services' as const,
      label: isRTL ? "الخدمات" : "Services",
      icon: Settings,
      description: isRTL ? "خدمات النظام" : "System services"
    },
    {
      id: 'questionnaire' as const,
      label: isRTL ? "الاستبيان" : "Questionnaire",
      icon: FileText,
      description: isRTL ? "استبيان ROPA" : "ROPA questionnaire"
    },
    {
      id: 'ropa-details' as const,
      label: isRTL ? "تفاصيل ROPA" : "ROPA Details",
      icon: Database,
      description: isRTL ? "تفاصيل سجل المعالجة" : "Processing record details"
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Header */}
      <div className="relative bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
        </div>

        <div className="relative z-10 px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            {/* Back Button */}
            <Button
              onClick={handleGoBack}
              className="bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 hover:text-white shadow-lg"
            >
              {isRTL ? <ArrowRight className="w-4 h-4 mr-2" /> : <ArrowLeft className="w-4 h-4 mr-2" />}
              {isRTL ? "العودة" : "Back"}
            </Button>
          </div>

          {/* Page Header */}
          <div className="flex items-center gap-6 mb-6">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center shadow-lg border border-white/20">
              <ClipboardList className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-1 tracking-tight">
                {isRTL ? "سجل أنشطة المعالجة" : "RoPA - Record of Processing Activities"}
              </h1>
              <p className="text-white/90 text-lg">
                {system?.name}
              </p>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex flex-wrap gap-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-3 px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                    isActive
                      ? "bg-white text-[var(--brand-blue)] shadow-lg"
                      : "bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="text-sm font-semibold">{tab.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="px-8 py-8">
        {activeTab === 'personal-data-groups' && (
          <PersonalDataGroupsTab 
            systemId={systemId} 
            lang={lang} 
            system={system}
          />
        )}
        {activeTab === 'services' && (
          <ServicesTab 
            systemId={systemId} 
            lang={lang} 
            system={system}
          />
        )}
        {activeTab === 'questionnaire' && (
          <QuestionnaireTab 
            systemId={systemId} 
            lang={lang} 
            system={system}
          />
        )}
        {activeTab === 'ropa-details' && (
          <ROPADetailsTab 
            systemId={systemId} 
            lang={lang} 
            system={system}
          />
        )}
      </div>
    </div>
  );
}
