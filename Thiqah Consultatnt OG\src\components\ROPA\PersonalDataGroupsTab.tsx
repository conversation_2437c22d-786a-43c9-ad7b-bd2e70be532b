"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Users, Plus, Edit, Database, Eye, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

import { System, SystemData, SystemsService } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";
import { PersonalDataVisualization } from "@/components/ROPA/PersonalDataVisualization";
import { GroupedDataView } from "@/components/ROPA/GroupedDataView";
import { EditGroupModal } from "@/components/ROPA/EditGroupModal";

interface PersonalDataGroup {
  id: string;
  name: string;
  description: string;
  attributes: string[];
  purpose: string;
  legalBasis: string;
  retentionPeriod: string;
  dataSubjects: string[];
  createdAt: Date;
  updatedAt: Date;
  attributeReasons?: Array<{
    attributeId: string;
    reason: string;
  }>;
}

interface PersonalDataGroupsTabProps {
  systemId: string;
  lang: string;
  system: System | null;
}

export function PersonalDataGroupsTab({ systemId, lang }: PersonalDataGroupsTabProps) {
  const [groups, setGroups] = useState<PersonalDataGroup[]>([]);
  const [personalData, setPersonalData] = useState<SystemData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingPersonalData, setIsLoadingPersonalData] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [isGrouping, setIsGrouping] = useState(false);
  const [viewMode, setViewMode] = useState<'ungrouped' | 'grouped'>('ungrouped');
  const [expandedGroup, setExpandedGroup] = useState<string | null>(null);
  const [editingGroup, setEditingGroup] = useState<PersonalDataGroup | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const { toast } = useToast();
  const isRTL = lang === "ar";

  const loadPersonalData = useCallback(async () => {
    try {
      setIsLoadingPersonalData(true);
      const allData = await SystemsService.getSystemData(systemId);

      // Filter for personal data with audit trail = "No"
      const filteredData = allData.filter(item =>
        item.hasPersonalData === true &&
        item.auditTrail === "No"
      );

      setPersonalData(filteredData);
    } catch (error) {
      console.error('Error loading personal data:', error);
      toast({
        title: isRTL ? "خطأ في التحميل" : "Loading Error",
        description: isRTL ? "فشل في تحميل البيانات الشخصية" : "Failed to load personal data",
        variant: "destructive"
      });
    } finally {
      setIsLoadingPersonalData(false);
    }
  }, [systemId, toast, isRTL]);

  const loadPersonalDataGroups = useCallback(async () => {
    try {
      setIsLoading(true);

      // Load existing groups from Firebase by checking groupTag field
      const groupedData = personalData.filter(item => item.groupTag);

      if (groupedData.length > 0) {
        // Group by groupTag
        const groupsMap = new Map<string, SystemData[]>();
        groupedData.forEach(item => {
          const groupName = item.groupTag;
          if (groupName && !groupsMap.has(groupName)) {
            groupsMap.set(groupName, []);
          }
          if (groupName) {
            groupsMap.get(groupName)!.push(item);
          }
        });

        // For existing groups, load them with current descriptions
        console.log('Existing groups found, loading with current descriptions');

        // Create groups from existing data
        const existingGroups: PersonalDataGroup[] = Array.from(groupsMap.entries()).map(([groupName, attributes], index) => ({
          id: `existing-group-${index + 1}`,
          name: groupName,
          description: `This ${groupName} group contains ${attributes.length} personal data attributes including ${attributes.slice(0, 3).map(attr => attr.columnName).join(', ')}${attributes.length > 3 ? ` and ${attributes.length - 3} more attributes` : ''}. Click "Group Entities" to regenerate with enhanced AI descriptions and detailed explanations for each attribute.`,
          attributes: attributes.map(attr => `${attr.tableName}.${attr.columnName}`),
          purpose: "Data grouping",
          legalBasis: "To be determined",
          retentionPeriod: "To be determined",
          dataSubjects: ["To be determined"],
          createdAt: new Date(),
          updatedAt: new Date()
        }));

        setGroups(existingGroups);
        if (existingGroups.length > 0) {
          setViewMode('grouped');
        }
      } else {
        setGroups([]);
      }
    } catch (error) {
      console.error('Error loading personal data groups:', error);
      toast({
        title: isRTL ? "خطأ في التحميل" : "Loading Error",
        description: isRTL ? "فشل في تحميل مجموعات البيانات" : "Failed to load data groups",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [personalData, toast, isRTL]);

  useEffect(() => {
    loadPersonalData();
  }, [loadPersonalData]);

  useEffect(() => {
    if (personalData.length > 0) {
      loadPersonalDataGroups();
    }
  }, [personalData, loadPersonalDataGroups]);

  const saveGroupsToFirebase = async (groups: PersonalDataGroup[]) => {
    try {
      // Update each record with its group tag
      for (const group of groups) {
        for (const attributeId of group.attributes) {
          await SystemsService.updateSystemDataDocument(systemId, attributeId, {
            groupTag: group.name
          });
        }
      }
      console.log('Groups saved to Firebase successfully');
    } catch (error) {
      console.error('Error saving groups to Firebase:', error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: isRTL ? "فشل في حفظ المجموعات" : "Failed to save groups",
        variant: "destructive"
      });
    }
  };

  const handleCreateGroup = () => {
    setIsCreating(true);
    // TODO: Implement create group modal
    toast({
      title: isRTL ? "إنشاء مجموعة" : "Create Group",
      description: isRTL ? "قريباً..." : "Coming soon...",
    });
    setIsCreating(false);
  };

  const handleGroupEntities = async () => {
    if (personalData.length === 0) {
      toast({
        title: isRTL ? "لا توجد بيانات" : "No Data",
        description: isRTL ? "لا توجد بيانات شخصية للتجميع" : "No personal data available for grouping",
        variant: "destructive"
      });
      return;
    }

    // Prevent multiple simultaneous calls
    if (isGrouping) {
      console.log('Grouping already in progress, ignoring duplicate call');
      return;
    }

    try {
      console.log('Starting entity grouping for', personalData.length, 'attributes');
      setIsGrouping(true);

      toast({
        title: isRTL ? "تجميع الكيانات" : "Grouping Entities",
        description: isRTL ?
          `جاري تجميع ${personalData.length} عنصر من البيانات الشخصية...` :
          `Grouping ${personalData.length} personal data attributes...`,
      });

      // Get system context if available
      let systemContext = '';
      try {
        const contextData = await SystemsService.getSystemContext(systemId);
        if (contextData) {
          systemContext = contextData.context || '';
        }
      } catch (error) {
        console.warn('Could not load system context:', error);
      }

      const response = await fetch('/api/ai/ropa/entity-grouping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          personalData: personalData,
          systemId: systemId,
          systemContext: systemContext
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Entity grouping failed');
      }

      if (result.success) {
        console.log('Entity grouping completed successfully:', result);

        // Create a lookup map for personal data by ID
        const dataLookup = new Map();
        personalData.forEach(item => {
          dataLookup.set(item.id, item);
        });

        // Convert AI groups to our PersonalDataGroup format
        const aiGroups: PersonalDataGroup[] = result.groups.map((group: { name: string; description: string; attributeIds: string[]; attributeReasons: Array<{ attributeId: string; reason: string }> }, index: number) => {
          // Map attribute IDs back to full attribute names
          const attributes = group.attributeIds
            .map((id: string) => {
              const item = dataLookup.get(id);
              return item ? `${item.tableName}.${item.columnName}` : null;
            })
            .filter((attr: string | null) => attr !== null);

          return {
            id: `ai-group-${index + 1}`,
            name: group.name,
            description: group.description, // Use AI-generated description directly
            attributes: attributes,
            purpose: "Data grouping",
            legalBasis: "To be determined",
            retentionPeriod: "To be determined",
            dataSubjects: ["To be determined"],
            createdAt: new Date(),
            updatedAt: new Date(),
            attributeReasons: group.attributeReasons || [] // Store the 8-word reasons
          };
        });

        setGroups(aiGroups);
        setViewMode('grouped'); // Switch to grouped view after successful grouping

        // Save group tags to Firebase
        await saveGroupsToFirebase(result.groups);

        toast({
          title: isRTL ? "تم التجميع بنجاح" : "Grouping Completed",
          description: isRTL ?
            `تم إنشاء ${result.groupsCreated} مجموعة من ${result.recordsProcessed} عنصر` :
            `Created ${result.groupsCreated} groups from ${result.recordsProcessed} attributes`,
        });
      } else {
        throw new Error('Entity grouping failed');
      }

    } catch (error) {
      console.error('Entity grouping error:', error);
      toast({
        title: isRTL ? "خطأ في التجميع" : "Grouping Error",
        description: isRTL ?
          "فشل في تجميع الكيانات. يرجى المحاولة مرة أخرى." :
          "Failed to group entities. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGrouping(false);
    }
  };

  const handleEditGroup = (groupId: string) => {
    const group = groups.find(g => g.id === groupId);
    if (group) {
      setEditingGroup(group);
      setIsEditModalOpen(true);
    }
  };

  const handleSaveGroupEdit = async (
    updatedGroup: PersonalDataGroup,
    attributeTransfers: Array<{fromGroup: string, toGroup: string, attributeId: string}>
  ) => {
    try {
      // Update the group in state
      const updatedGroups = groups.map(g =>
        g.id === updatedGroup.id ? updatedGroup : g
      );

      // Process attribute transfers
      for (const transfer of attributeTransfers) {
        // Update Firebase with new group tag
        const targetGroup = groups.find(g => g.id === transfer.toGroup);
        if (targetGroup) {
          await SystemsService.updateSystemDataDocument(systemId, transfer.attributeId, {
            groupTag: targetGroup.name
          });

          // Update groups in state
          const sourceGroupIndex = updatedGroups.findIndex(g => g.id === transfer.fromGroup);
          const targetGroupIndex = updatedGroups.findIndex(g => g.id === transfer.toGroup);

          if (sourceGroupIndex !== -1 && targetGroupIndex !== -1) {
            // Find the attribute to transfer
            const attr = personalData.find(item => item.id === transfer.attributeId);
            if (attr) {
              const attrKey = `${attr.tableName}.${attr.columnName}`;

              // Remove from source group
              updatedGroups[sourceGroupIndex].attributes = updatedGroups[sourceGroupIndex].attributes
                .filter(a => a !== attrKey);

              // Add to target group
              if (!updatedGroups[targetGroupIndex].attributes.includes(attrKey)) {
                updatedGroups[targetGroupIndex].attributes.push(attrKey);
              }
            }
          }
        }
      }

      setGroups(updatedGroups);

      toast({
        title: isRTL ? "تم الحفظ بنجاح" : "Saved Successfully",
        description: isRTL ?
          `تم تحديث المجموعة ونقل ${attributeTransfers.length} عنصر` :
          `Group updated and ${attributeTransfers.length} attributes transferred`,
      });

      // Reload personal data to reflect changes
      await loadPersonalData();

    } catch (error) {
      console.error('Error saving group edit:', error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: isRTL ? "فشل في حفظ التغييرات" : "Failed to save changes",
        variant: "destructive"
      });
    }
  };

  const handleTransferAttribute = async (attributeId: string, fromGroupId: string, toGroupId: string) => {
    try {
      // Find the target group
      const targetGroup = groups.find(g => g.id === toGroupId);
      if (!targetGroup) return;

      // Update Firebase with new group tag
      await SystemsService.updateSystemDataDocument(systemId, attributeId, {
        groupTag: targetGroup.name
      });

      // Update groups in state
      const updatedGroups = groups.map(group => {
        if (group.id === fromGroupId) {
          // Remove attribute from source group
          const attr = personalData.find(item => item.id === attributeId);
          if (attr) {
            const attrKey = `${attr.tableName}.${attr.columnName}`;
            return {
              ...group,
              attributes: group.attributes.filter(a => a !== attrKey)
            };
          }
        } else if (group.id === toGroupId) {
          // Add attribute to target group
          const attr = personalData.find(item => item.id === attributeId);
          if (attr) {
            const attrKey = `${attr.tableName}.${attr.columnName}`;
            if (!group.attributes.includes(attrKey)) {
              return {
                ...group,
                attributes: [...group.attributes, attrKey]
              };
            }
          }
        }
        return group;
      });

      setGroups(updatedGroups);

      toast({
        title: isRTL ? "تم النقل بنجاح" : "Transfer Successful",
        description: isRTL ? "تم نقل العنصر إلى المجموعة الجديدة" : "Attribute transferred to new group",
      });

      // Reload personal data to reflect changes
      await loadPersonalData();

    } catch (error) {
      console.error('Error transferring attribute:', error);
      toast({
        title: isRTL ? "خطأ في النقل" : "Transfer Error",
        description: isRTL ? "فشل في نقل العنصر" : "Failed to transfer attribute",
        variant: "destructive"
      });
    }
  };

  const handleDeleteGroup = () => {
    // TODO: Implement delete group functionality
    toast({
      title: isRTL ? "حذف المجموعة" : "Delete Group",
      description: isRTL ? "قريباً..." : "Coming soon...",
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
              <Users className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-white">
                {isRTL ? "مجموعات البيانات الشخصية" : "Personal Data Groups"}
              </h3>
              <p className="text-white/80">
                {isRTL
                  ? `${personalData.length} عنصر بيانات شخصية`
                  : `${personalData.length} personal data attributes`
                }
              </p>
            </div>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={handleGroupEntities}
              disabled={isGrouping || personalData.length === 0}
              className="bg-green-500/20 backdrop-blur-sm border border-green-300/50 text-white hover:bg-green-500/30"
            >
              {isGrouping ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Database className="w-4 h-4 mr-2" />
              )}
              {isRTL ? "تجميع الكيانات" : "Group Entities"}
            </Button>
            <Button
              onClick={handleCreateGroup}
              disabled={isCreating || personalData.length === 0}
              className="bg-white/20 backdrop-blur-sm border border-white/30 text-white hover:bg-white/30"
            >
              <Plus className="w-4 h-4 mr-2" />
              {isRTL ? "إنشاء مجموعة" : "Create Group"}
            </Button>
          </div>
        </div>

        {/* View Mode Toggle */}
        <div className="flex justify-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-1 border border-white/20">
            <div className="flex gap-1">
              <button
                onClick={() => setViewMode('ungrouped')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 ${
                  viewMode === 'ungrouped'
                    ? 'bg-white text-[var(--brand-blue)] shadow-md'
                    : 'text-white/80 hover:text-white hover:bg-white/10'
                }`}
              >
                <Eye className="w-4 h-4" />
                <span className="font-medium">
                  {isRTL ? "البيانات غير المجمعة" : "Ungrouped Data"}
                </span>
                <span className="text-xs opacity-75">
                  ({personalData.filter(item => !item.groupTag).length})
                </span>
              </button>
              <button
                onClick={() => setViewMode('grouped')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 ${
                  viewMode === 'grouped'
                    ? 'bg-white text-[var(--brand-blue)] shadow-md'
                    : 'text-white/80 hover:text-white hover:bg-white/10'
                }`}
              >
                <Edit className="w-4 h-4" />
                <span className="font-medium">
                  {isRTL ? "المجموعات" : "Groups"}
                </span>
                <span className="text-xs opacity-75">
                  ({groups.length} {isRTL ? "مجموعة" : "groups"})
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="p-8">
        {viewMode === 'ungrouped' ? (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {isRTL ? "البيانات الشخصية غير المجمعة" : "Ungrouped Personal Data"}
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {isRTL
                ? "البيانات الشخصية التي لا تحتوي على مسار مراجعة ولم يتم تجميعها بعد"
                : "Personal data without audit trail that hasn't been grouped yet"
              }
            </p>
            <PersonalDataVisualization
              personalData={personalData.filter(item => !item.groupTag)}
              isLoading={isLoadingPersonalData}
              lang={lang}
              systemId={systemId}
            />
          </div>
        ) : (
          <GroupedDataView
            groups={groups}
            personalData={personalData}
            isLoading={isLoading}
            lang={lang}
            systemId={systemId}
            expandedGroup={expandedGroup}
            setExpandedGroup={setExpandedGroup}
            onEditGroup={handleEditGroup}
            onDeleteGroup={handleDeleteGroup}
            onTransferAttribute={handleTransferAttribute}
          />
        )}
      </div>

      {/* Edit Group Modal */}
      <EditGroupModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setEditingGroup(null);
        }}
        group={editingGroup}
        allGroups={groups}
        personalData={personalData}
        onSave={handleSaveGroupEdit}
        lang={lang}
      />

    </div>
  );
}
