"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Scale, Calendar, Edit, Trash2, FileText, BookOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Locale } from '@/i18n-config';
import { Timestamp } from 'firebase/firestore';
import { PDPLPoint } from '@/Firebase/firestore/services/PDPLService';

interface PDPLArticleCardProps {
  article: {
    id?: string;
    articleNumber: string;
    title: string;
    description: string;
    points?: PDPLPoint[];
    linkedArticles?: string[];
    createdAt: Timestamp | Date | string;
    updatedAt: Timestamp | Date | string;
    provisions?: string[];
    subProvisions?: string[];
    documentType?: string;
  };
  lang: Locale;
  onEdit?: (article: {
    id?: string;
    articleNumber: string;
    title: string;
    description: string;
    points?: PDPLPoint[];
    linkedArticles?: string[];
    createdAt: Timestamp | Date | string;
    updatedAt: Timestamp | Date | string;
  }) => void;
  onDelete?: (articleId: string) => void;
  onOpenModal?: (article: {
    id?: string;
    articleNumber: string;
    title: string;
    description: string;
    points?: PDPLPoint[];
    linkedArticles?: string[];
    createdAt: Timestamp | Date | string;
    updatedAt: Timestamp | Date | string;
  }, pointId?: string) => void;
}

export function PDPLArticleCard({ article, lang, onEdit, onDelete, onOpenModal }: PDPLArticleCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const isRTL = lang === "ar";

  const handlePointClick = (pointId: string) => {
    if (onOpenModal) {
      onOpenModal(article, pointId);
    }
  };

  const formatDate = (timestamp: Timestamp | Date | string) => {
    if (!timestamp) return '';
    try {
      const date = (timestamp as Timestamp).toDate ? (timestamp as Timestamp).toDate() : new Date(timestamp as string);
      return date.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US');
    } catch {
      return '';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="relative group"
    >
      {/* Legal Document Card */}
      <div className="bg-gradient-to-br from-white via-blue-50/30 to-blue-100/20 rounded-xl border border-blue-200/30 shadow-lg hover:shadow-xl transition-all duration-500 overflow-hidden">
        {/* Legal Document Header */}
        <div className="relative bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/90 text-white px-6 py-4">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-0 right-0 w-24 h-24 bg-white rounded-full -translate-y-1/2 translate-x-1/2"></div>
            <div className="absolute bottom-0 left-0 w-16 h-16 bg-white rounded-full translate-y-1/2 -translate-x-1/2"></div>
          </div>
          
          <div className="relative z-10 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
                <Scale className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="text-white/80 text-xs font-medium uppercase tracking-wider">
                  {isRTL ? "النظام الأساسي لحماية البيانات الشخصية" : "Personal Data Protection Law"}
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-xl font-bold tracking-tight">
                    {isRTL ? `المادة ${article.articleNumber}` : `Article ${article.articleNumber}`}
                  </div>
                  {/* Article Number Badge */}
                  <div className="px-2 py-1 bg-white/20 backdrop-blur-sm rounded-full border border-white/30">
                    <span className="text-xs font-bold text-white">
                      #{article.articleNumber.match(/\d+/)?.[0] || '?'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit?.(article)}
                className="text-white/80 hover:text-white hover:bg-white/20 border border-white/30 backdrop-blur-sm"
              >
                <Edit className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete?.(article.id || '')}
                className="text-white/80 hover:text-white hover:bg-red-500/20 border border-white/30 backdrop-blur-sm"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Article Content */}
        <div className="p-6">
          {/* Article Title */}
          <div className="mb-4">
            <h3 className="text-xl font-bold text-gray-900 mb-2 leading-tight">
              {article.title}
            </h3>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <FileText className="w-4 h-4" />
              <span className="capitalize font-medium">{article.documentType?.replace('_', ' ') || 'Document'}</span>
              <span className="text-gray-400">•</span>
              <span>{isRTL ? "تاريخ الإنشاء" : "Created"} {formatDate(article.createdAt)}</span>
            </div>
          </div>

          {/* Article Points - Compact View */}
          <div className="space-y-4">
            {article.points && article.points.length > 0 ? (
              <div className="bg-gradient-to-r from-blue-50 to-blue-50/50 rounded-lg p-4 border border-blue-200/50">
                <div className="flex items-center gap-2 mb-3">
                  <BookOpen className="w-4 h-4 text-[var(--brand-blue)]" />
                  <span className="text-sm font-semibold text-gray-700">
                    {isRTL ? "النقاط الرئيسية" : "Main Points"} ({article.points.length})
                  </span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {article.points.map((point, index) => (
                    <button
                      key={point.id}
                      onClick={() => handlePointClick(point.id)}
                      className="w-8 h-8 bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/80 text-white rounded-full flex items-center justify-center text-sm font-bold transition-all duration-200 hover:scale-110 shadow-md hover:shadow-lg"
                      title={isRTL ? `النقطة ${index + 1}: ${point.content.substring(0, 50)}...` : `Point ${index + 1}: ${point.content.substring(0, 50)}...`}
                    >
                      {index + 1}
                    </button>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-3">
                  {isRTL ? "انقر على رقم النقطة لفتح نافذة التفاصيل" : "Click on a point number to open article modal"}
                </p>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <BookOpen className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p className="text-sm">
                  {isRTL ? "لا توجد نقاط محددة لهذه المادة" : "No points defined for this article"}
                </p>
              </div>
            )}

            {/* Expandable Section for Provisions */}
            {((article.provisions?.length || 0) > 0 || (article.subProvisions?.length || 0) > 0) && (
              <div className="border-t border-gray-200 pt-4">
                <Button
                  variant="ghost"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="w-full flex items-center justify-between p-3 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                >
                  <span className="font-semibold text-gray-900 flex items-center gap-2">
                    <Scale className="w-4 h-4 text-[var(--brand-blue)]" />
                    {isRTL ? "الأحكام التفصيلية" : "Detailed Legal Provisions"}
                  </span>
                  <motion.div
                    animate={{ rotate: isExpanded ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronDown className="w-5 h-5 text-gray-600" />
                  </motion.div>
                </Button>

                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="mt-4 space-y-4"
                    >
                      {/* Provisions */}
                      {(article.provisions?.length || 0) > 0 && (
                        <div className="bg-gradient-to-r from-blue-50 to-blue-50/50 rounded-lg p-4 border border-blue-200/50">
                          <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <BookOpen className="w-4 h-4 text-[var(--brand-blue)]" />
                            {isRTL ? "الأحكام الأساسية" : "Main Provisions"}
                          </h4>
                          <div className="space-y-2">
                            {article.provisions?.map((provision, index) => (
                              <div key={index} className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-[var(--brand-blue)] text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5 flex-shrink-0">
                                  {index + 1}
                                </div>
                                <p className="text-gray-700 leading-relaxed flex-1">
                                  {provision}
                                </p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Sub-provisions */}
                      {(article.subProvisions?.length || 0) > 0 && (
                        <div className="bg-gradient-to-r from-gray-50 to-gray-50/50 rounded-lg p-4 border border-gray-200/50">
                          <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <FileText className="w-4 h-4 text-gray-600" />
                            {isRTL ? "الأحكام الفرعية" : "Sub-provisions"}
                          </h4>
                          <div className="space-y-2">
                            {article.subProvisions?.map((subProvision, index) => (
                              <div key={index} className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-gray-600 text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5 flex-shrink-0">
                                  {String.fromCharCode(97 + index)}
                                </div>
                                <p className="text-gray-700 leading-relaxed flex-1">
                                  {subProvision}
                                </p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
          </div>
        </div>

        {/* Legal Document Footer */}
        <div className="border-t border-gray-200 bg-gradient-to-r from-gray-50 to-gray-50/50 px-6 py-3">
          <div className="flex items-center justify-between text-xs text-gray-600">
            <div className="flex items-center gap-2">
              <Scale className="w-3 h-3" />
              <span className="font-medium">
                {isRTL ? "النظام الأساسي لحماية البيانات الشخصية" : "Personal Data Protection Law Framework"}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="w-3 h-3" />
              <span>
                {isRTL ? "آخر تحديث" : "Last Updated"}: {formatDate(article.updatedAt)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}