# Dependencies
node_modules/

# Build outputs (Vercel will build these)
.next/
build/
dist/

# Custom build folders
Builds/

# Development files
.cursor/
.DS_Store
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files (should be set in Vercel dashboard)
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/

# TypeScript build info
tsconfig.tsbuildinfo

# Testing
coverage/

# Cache directories
.npm
.eslintcache

# Vercel CLI
.vercel 