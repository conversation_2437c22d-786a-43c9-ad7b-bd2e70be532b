import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/Firebase/Authentication/authConfig';
import { 
  getUserProfile, 
  UserRole 
} from '@/Firebase/firestore/services/UserService';
import { firestore } from '@/Firebase/firestore/firestoreConfig';
import { doc, updateDoc, deleteDoc } from 'firebase/firestore';

// Helper function to verify authentication and consultant role
async function verifyConsultantAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { error: 'Unauthorized', status: 401 };
  }

  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return { error: 'Unauthorized', status: 401 };
    }

    const userProfile = await getUserProfile(currentUser.uid);
    if (!userProfile || userProfile.role !== UserRole.CONSULTANT) {
      return { error: 'Forbidden: Consultant access required', status: 403 };
    }

    return { user: userProfile };
  } catch (error) {
    // Log error for debugging
    console.error('Authentication failed:', error);
    return { error: 'Authentication failed', status: 401 };
  }
}

// GET - Get specific user
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ uid: string }> }
) {
  try {
    const { uid } = await params;
    
    const authResult = await verifyConsultantAuth(request);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const userProfile = await getUserProfile(uid);
    if (!userProfile) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({ user: userProfile });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

// PATCH - Update user status (enable/disable)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ uid: string }> }
) {
  try {
    const { uid } = await params;
    
    const authResult = await verifyConsultantAuth(request);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { disabled, displayName, email } = await request.json();

    // Prevent consultants from disabling themselves
    if (uid === authResult.user.uid && disabled === true) {
      return NextResponse.json({ 
        error: 'Cannot disable your own account' 
      }, { status: 400 });
    }

    // Check if user exists
    const userProfile = await getUserProfile(uid);
    if (!userProfile) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Update user document
    const userRef = doc(firestore, 'users', uid);
    const userData: Partial<{
      disabled: boolean;
      displayName: string;
      email: string;
    }> = {};

    if (typeof disabled === 'boolean') {
      userData.disabled = disabled;
    }
    if (displayName) {
      userData.displayName = displayName;
    }
    if (email) {
      userData.email = email;
    }

    await updateDoc(userRef, userData);

    // Get updated user profile
    const updatedProfile = await getUserProfile(uid);

    return NextResponse.json({ 
      message: 'User updated successfully',
      user: updatedProfile 
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
  }
}

// DELETE - Delete user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ uid: string }> }
) {
  try {
    const { uid } = await params;
    
    const authResult = await verifyConsultantAuth(request);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Prevent consultants from deleting themselves
    if (uid === authResult.user.uid) {
      return NextResponse.json({ 
        error: 'Cannot delete your own account' 
      }, { status: 400 });
    }

    // Check if user exists
    const userProfile = await getUserProfile(uid);
    if (!userProfile) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Delete user document from Firestore
    const userRef = doc(firestore, 'users', uid);
    await deleteDoc(userRef);

    // Note: In a production app, you'd also want to delete the user from Firebase Auth
    // This requires admin SDK which should be done server-side

    return NextResponse.json({ 
      message: 'User deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 });
  }
}
