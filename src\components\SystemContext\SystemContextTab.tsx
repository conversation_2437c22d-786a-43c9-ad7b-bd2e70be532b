"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { MessageSquare, Settings } from "lucide-react";

import { SystemsService, System, SystemService, SystemContextPoint, SystemContext } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";
import { SystemContextPoints } from "./SystemContextPoints";
import { ServiceManagement } from "./ServiceManagement";

interface SystemContextTabProps {
  systemId: string;
  lang: string;
  system: System | null;
}

export function SystemContextTab({ systemId, lang, system }: SystemContextTabProps) {

  const [, setSystemContext] = useState<SystemContext | null>(null);
  const [contextPoints, setContextPoints] = useState<SystemContextPoint[]>([]);
  const [services, setServices] = useState<SystemService[]>([]);
  const [isLoadingContext, setIsLoadingContext] = useState(true);
  const [isLoadingServices, setIsLoadingServices] = useState(true);
  const [activeSection, setActiveSection] = useState<'context' | 'services'>('context');

  const { toast } = useToast();
  const isRTL = lang === "ar";

  // Load system context and context points
  const loadSystemContext = useCallback(async () => {
    if (!systemId) return;

    try {
      setIsLoadingContext(true);

      // Load both legacy context and context points
      const [context, points] = await Promise.all([
        SystemsService.getSystemContext(systemId),
        SystemsService.getSystemContextPoints(systemId)
      ]);

      setSystemContext(context);

      // If we have context points from the new method, use those
      // Otherwise, try to get them from the legacy context object
      if (points && points.length > 0) {
        setContextPoints(points);
      } else if (context && context.contextPoints && context.contextPoints.length > 0) {
        // Use legacy context points if available
        setContextPoints(context.contextPoints);
      } else {
        setContextPoints([]);
      }
    } catch (error) {
      console.error('Error loading system context:', error);
      toast({
        title: isRTL ? "خطأ في التحميل" : "Loading Error",
        description: isRTL ? "فشل في تحميل سياق النظام" : "Failed to load system context",
        variant: "destructive"
      });
    } finally {
      setIsLoadingContext(false);
    }
  }, [systemId, toast, isRTL]);

  // Load system services
  const loadSystemServices = useCallback(async () => {
    if (!systemId) return;
    
    try {
      setIsLoadingServices(true);
      const systemServices = await SystemsService.getSystemServices(systemId);
      setServices(systemServices);
    } catch (error) {
      console.error('Error loading system services:', error);
      toast({
        title: isRTL ? "خطأ في التحميل" : "Loading Error",
        description: isRTL ? "فشل في تحميل خدمات النظام" : "Failed to load system services",
        variant: "destructive"
      });
    } finally {
      setIsLoadingServices(false);
    }
  }, [systemId, toast, isRTL]);

  useEffect(() => {
    if (systemId) {
      loadSystemContext();
      loadSystemServices();
    }
  }, [systemId, loadSystemContext, loadSystemServices]);

  const sections = [
    {
      id: 'context' as const,
      label: isRTL ? "سياق النظام" : "System Context",
      icon: MessageSquare,
      description: isRTL ? "إدارة نقاط السياق" : "Manage context points"
    },
    {
      id: 'services' as const,
      label: isRTL ? "الخدمات" : "Services",
      icon: Settings,
      description: isRTL ? "إدارة خدمات النظام" : "Manage system services"
    }
  ];

  if (isLoadingContext && isLoadingServices) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-96 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {system?.name} {isRTL ? "السياق" : "Context"}
          </h2>
          <p className="text-gray-600">
            {isRTL 
              ? "إدارة سياق النظام والخدمات المقدمة" 
              : "Manage system context and provided services"
            }
          </p>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="flex justify-center">
        <div className="bg-white rounded-2xl p-1 shadow-lg border border-gray-200">
          <div className="flex gap-1">
            {sections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`flex items-center gap-2 px-6 py-3 rounded-xl transition-all duration-300 ${
                  activeSection === section.id
                    ? 'bg-[var(--brand-blue)] text-white shadow-md'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <section.icon className="w-4 h-4" />
                <span className="font-medium">{section.label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Section Content */}
      <motion.div
        key={activeSection}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeSection === 'context' && (
          <SystemContextPoints
            systemId={systemId}
            lang={lang}
            contextPoints={contextPoints}
            onContextUpdate={loadSystemContext}
          />
        )}
        {activeSection === 'services' && (
          <ServiceManagement
            systemId={systemId}
            lang={lang}
            services={services}
            onServicesUpdate={loadSystemServices}
          />
        )}
      </motion.div>
    </div>
  );
}
