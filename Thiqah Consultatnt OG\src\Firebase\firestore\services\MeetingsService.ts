import {
  collection,
  doc,
  addDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  Timestamp,
  where,
  getDoc
} from 'firebase/firestore';
import { firestore as db } from '../firestoreConfig';

export enum MeetingStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum MeetingType {
  INTERNAL = 'internal',
  CLIENT = 'client',
  VENDOR = 'vendor',
  STAKEHOLDER = 'stakeholder',
  REVIEW = 'review',
  OTHER = 'other'
}

export interface MeetingAttendee {
  name: string;
  email: string;
  role?: string;
  isConsultant?: boolean;
  consultantId?: string; // For linking to consultant users
}

export interface Meeting {
  id?: string;
  title: string;
  description?: string;
  meetingDate: Timestamp;
  duration: number; // in minutes
  location?: string;
  meetingType: MeetingType;
  status: MeetingStatus;
  organizer: {
    name: string;
    email: string;
    uid: string;
  };
  attendees: MeetingAttendee[];
  summary?: string;
  agenda?: string[];
  notes?: string;
  attachments?: string[]; // URLs to attached files
  createdBy: string;
  createdByName: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  completedAt?: Timestamp;
}

const meetingsCollection = collection(db, 'meetings');

export class MeetingsService {
  /**
   * Creates a new meeting
   */
  static async createMeeting(meetingData: Omit<Meeting, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = Timestamp.now();

    // Clean the data to remove undefined fields
    const cleanedData = Object.fromEntries(
      Object.entries(meetingData).filter(([, value]) => value !== undefined)
    );

    const newMeeting = {
      ...cleanedData,
      createdAt: now,
      updatedAt: now
    };

    const docRef = await addDoc(meetingsCollection, newMeeting);
    return docRef.id;
  }

  /**
   * Retrieves all meetings ordered by meeting date
   */
  static async getMeetings(): Promise<Meeting[]> {
    const q = query(meetingsCollection, orderBy('meetingDate', 'desc'));
    const querySnapshot = await getDocs(q);

    const meetings: Meeting[] = [];
    querySnapshot.forEach((doc) => {
      meetings.push({
        id: doc.id,
        ...doc.data()
      } as Meeting);
    });

    return meetings;
  }

  /**
   * Retrieves meetings for a specific date range
   */
  static async getMeetingsByDateRange(startDate: Date, endDate: Date): Promise<Meeting[]> {
    const startTimestamp = Timestamp.fromDate(startDate);
    const endTimestamp = Timestamp.fromDate(endDate);
    
    const q = query(
      meetingsCollection,
      where('meetingDate', '>=', startTimestamp),
      where('meetingDate', '<=', endTimestamp),
      orderBy('meetingDate', 'asc')
    );
    
    const querySnapshot = await getDocs(q);
    const meetings: Meeting[] = [];
    
    querySnapshot.forEach((doc) => {
      meetings.push({
        id: doc.id,
        ...doc.data()
      } as Meeting);
    });
    
    return meetings;
  }

  /**
   * Retrieves meetings for a specific month
   */
  static async getMeetingsForMonth(year: number, month: number): Promise<Meeting[]> {
    const startDate = new Date(year, month, 1);
    const endDate = new Date(year, month + 1, 0, 23, 59, 59);
    
    return this.getMeetingsByDateRange(startDate, endDate);
  }

  /**
   * Gets a single meeting by ID
   */
  static async getMeeting(meetingId: string): Promise<Meeting | null> {
    const meetingRef = doc(meetingsCollection, meetingId);
    const docSnap = await getDoc(meetingRef);

    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data()
      } as Meeting;
    }

    return null;
  }

  /**
   * Updates a meeting
   */
  static async updateMeeting(meetingId: string, updates: Partial<Meeting>): Promise<void> {
    const meetingRef = doc(meetingsCollection, meetingId);
    const updateData = {
      ...updates,
      updatedAt: Timestamp.now()
    };

    if (updates.status === MeetingStatus.COMPLETED && !updates.completedAt) {
      updateData.completedAt = Timestamp.now();
    }

    await updateDoc(meetingRef, updateData);
  }



  /**
   * Deletes a meeting
   */
  static async deleteMeeting(meetingId: string): Promise<void> {
    const meetingRef = doc(meetingsCollection, meetingId);
    await deleteDoc(meetingRef);
  }

  /**
   * Gets meeting statistics
   */
  static async getMeetingStatistics(): Promise<{
    total: number;
    scheduled: number;
    completed: number;
    cancelled: number;
    thisMonth: number;
    inProgress: number;
  }> {
    const meetings = await this.getMeetings();

    const now = new Date();
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    const thisMonthMeetings = meetings.filter(m => {
      const meetingDate = m.meetingDate.toDate();
      return meetingDate >= thisMonthStart && meetingDate <= thisMonthEnd;
    });

    return {
      total: meetings.length,
      scheduled: meetings.filter(m => m.status === MeetingStatus.SCHEDULED).length,
      completed: meetings.filter(m => m.status === MeetingStatus.COMPLETED).length,
      cancelled: meetings.filter(m => m.status === MeetingStatus.CANCELLED).length,
      thisMonth: thisMonthMeetings.length,
      inProgress: meetings.filter(m => m.status === MeetingStatus.IN_PROGRESS).length,
    };
  }
}
