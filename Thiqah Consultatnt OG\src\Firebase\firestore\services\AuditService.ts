/**
 * AuditService - Manages audit findings and observations
 * 
 * This service provides comprehensive functionality for managing audit observations,
 * including CRUD operations, risk management, and statistical reporting.
 * 
 * Features:
 * - Create, read, update, delete observations
 * - Manage observation-related risks
 * - Track observation status and ratings
 * - Generate statistical reports
 * - Support for multiple rating levels (Low, Medium, High, Critical)
 */

import {
  collection,
  doc,
  addDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  Timestamp,
  where,
  getDoc
} from 'firebase/firestore';
import { firestore as db } from '../firestoreConfig';

export enum ObservationRating {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface ObservationRisk {
  id: string;
  description: string;
  impact?: string;
  probability?: string;
  mitigation?: string;
  createdAt: Timestamp;
}

export interface ManagementResponse {
  id: string;
  response: string;
  responsiblePerson: string;
  targetCompletionDate: Timestamp;
  dateType: 'monthly' | 'quarterly';
  quarter?: 1 | 2 | 3 | 4;
  year: number;
  month?: number;
  createdBy: string;
  createdByName: string;
  createdAt: Timestamp;
  status?: 'pending' | 'in_progress' | 'completed' | 'overdue';
}

export interface KPIUpdate {
  id: string;
  value: number;
  targetValue: number;
  unit: string;
  updateDate: Timestamp;
  notes?: string;
  updatedBy: string;
  updatedByName: string;
  createdAt: Timestamp;
}

export interface ResolutionKPI {
  id: string;
  name: string;
  description: string;
  unit: string;
  targetValue: number;
  currentValue: number;
  measurementFrequency: 'weekly' | 'monthly' | 'quarterly';
  kpiType: 'percentage' | 'number' | 'currency' | 'time';
  targetDate: Timestamp;
  dateType: 'monthly' | 'quarterly';
  quarter?: 1 | 2 | 3 | 4;
  year: number;
  month?: number;
  linkedObservationIds?: string[];
  linkedManagementResponseIds?: string[];
  updates: KPIUpdate[];
  createdBy: string;
  createdByName: string;
  createdAt: Timestamp;
  lastUpdated?: Timestamp;
  status: 'on_track' | 'at_risk' | 'behind' | 'achieved' | 'overdue';
}

export interface Observation {
  id?: string;
  name: string;
  observationNumber: string;
  rating: ObservationRating;
  description?: string;
  backgroundInformation?: string;
  recommendations?: string;
  managementResponses?: ManagementResponse[];
  resolutionKPIs?: ResolutionKPI[];
  risks?: ObservationRisk[];
  createdBy: string;
  createdByName: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  status?: 'open' | 'in_progress' | 'resolved' | 'closed';
  assignees?: string[];
  dueDate?: Timestamp;
  resolvedAt?: Timestamp;
  notes?: string;
}

const observationsCollection = collection(db, 'observations');

export class AuditService {
  /**
   * Creates a new observation
   */
  static async createObservation(observationData: Omit<Observation, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = Timestamp.now();
    const newObservation = {
      ...observationData,
      createdAt: now,
      updatedAt: now,
      status: 'open' as const,
      risks: observationData.risks || []
    };

    const docRef = await addDoc(observationsCollection, newObservation);
    return docRef.id;
  }

  /**
   * Retrieves all observations ordered by creation date
   */
  static async getObservations(): Promise<Observation[]> {
    const q = query(observationsCollection, orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);

    const observations: Observation[] = [];
    querySnapshot.forEach((doc) => {
      observations.push({
        id: doc.id,
        ...doc.data()
      } as Observation);
    });

    return observations;
  }

  /**
   * Retrieves a single observation by ID
   */
  static async getObservation(observationId: string): Promise<Observation | null> {
    const docRef = doc(observationsCollection, observationId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data()
      } as Observation;
    }

    return null;
  }

  /**
   * Updates an observation
   */
  static async updateObservation(observationId: string, updates: Partial<Observation>): Promise<void> {
    const observationRef = doc(observationsCollection, observationId);
    
    // Filter out undefined values to prevent Firebase errors
    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([, value]) => value !== undefined)
    );
    
    const updateData: Partial<Observation> & { updatedAt: Timestamp } = {
      ...cleanUpdates,
      updatedAt: Timestamp.now()
    };

    if (updates.status === 'resolved' && !updates.resolvedAt) {
      updateData.resolvedAt = Timestamp.now();
    }

    await updateDoc(observationRef, updateData);
  }

  /**
   * Deletes an observation
   */
  static async deleteObservation(observationId: string): Promise<void> {
    const observationRef = doc(observationsCollection, observationId);
    await deleteDoc(observationRef);
  }

  /**
   * Adds a risk to an observation
   */
  static async addObservationRisk(observationId: string, risk: Omit<ObservationRisk, 'id' | 'createdAt'>): Promise<void> {
    const observation = await this.getObservation(observationId);
    if (!observation) {
      throw new Error('Observation not found');
    }

    const newRisk: ObservationRisk = {
      ...risk,
      id: `risk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: Timestamp.now()
    };

    const updatedRisks = [...(observation.risks || []), newRisk];

    await this.updateObservation(observationId, {
      risks: updatedRisks
    });
  }

  /**
   * Removes a risk from an observation
   */
  static async removeObservationRisk(observationId: string, riskId: string): Promise<void> {
    const observation = await this.getObservation(observationId);
    if (!observation) {
      throw new Error('Observation not found');
    }

    const updatedRisks = (observation.risks || []).filter(risk => risk.id !== riskId);

    await this.updateObservation(observationId, {
      risks: updatedRisks
    });
  }

  /**
   * Get observation statistics
   */
  static async getObservationStatistics(): Promise<{
    total: number;
    open: number;
    inProgress: number;
    resolved: number;
    closed: number;
    byRating: {
      low: number;
      medium: number;
      high: number;
      critical: number;
    };
  }> {
    const observations = await this.getObservations();
    
    const stats = {
      total: observations.length,
      open: 0,
      inProgress: 0,
      resolved: 0,
      closed: 0,
      byRating: {
        low: 0,
        medium: 0,
        high: 0,
        critical: 0
      }
    };

    observations.forEach(observation => {
      // Count by status
      switch (observation.status) {
        case 'open':
          stats.open++;
          break;
        case 'in_progress':
          stats.inProgress++;
          break;
        case 'resolved':
          stats.resolved++;
          break;
        case 'closed':
          stats.closed++;
          break;
        default:
          stats.open++; // Default to open if status is undefined
      }

      // Count by rating
      switch (observation.rating) {
        case ObservationRating.LOW:
          stats.byRating.low++;
          break;
        case ObservationRating.MEDIUM:
          stats.byRating.medium++;
          break;
        case ObservationRating.HIGH:
          stats.byRating.high++;
          break;
        case ObservationRating.CRITICAL:
          stats.byRating.critical++;
          break;
      }
    });

    return stats;
  }

  /**
   * Get observations by rating
   */
  static async getObservationsByRating(rating: ObservationRating): Promise<Observation[]> {
    const q = query(
      observationsCollection,
      where('rating', '==', rating),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);

    const observations: Observation[] = [];
    querySnapshot.forEach((doc) => {
      observations.push({
        id: doc.id,
        ...doc.data()
      } as Observation);
    });

    return observations;
  }

  /**
   * Get observations by status
   */
  static async getObservationsByStatus(status: 'open' | 'in_progress' | 'resolved' | 'closed'): Promise<Observation[]> {
    const q = query(
      observationsCollection,
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);

    const observations: Observation[] = [];
    querySnapshot.forEach((doc) => {
      observations.push({
        id: doc.id,
        ...doc.data()
      } as Observation);
    });

    return observations;
  }

  /**
   * Adds a management response to an observation
   */
  static async addManagementResponse(
    observationId: string, 
    response: Omit<ManagementResponse, 'id' | 'createdAt'>
  ): Promise<void> {
    const observation = await this.getObservation(observationId);
    if (!observation) {
      throw new Error('Observation not found');
    }

    const newResponse: ManagementResponse = {
      ...response,
      id: `response_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: Timestamp.now(),
      status: 'pending'
    };

    const updatedResponses = [...(observation.managementResponses || []), newResponse];

    await this.updateObservation(observationId, {
      managementResponses: updatedResponses
    });
  }

  /**
   * Updates a management response
   */
  static async updateManagementResponse(
    observationId: string, 
    responseId: string, 
    updates: Partial<ManagementResponse>
  ): Promise<void> {
    const observation = await this.getObservation(observationId);
    if (!observation) {
      throw new Error('Observation not found');
    }

    const updatedResponses = (observation.managementResponses || []).map(response => 
      response.id === responseId ? { ...response, ...updates } : response
    );

    await this.updateObservation(observationId, {
      managementResponses: updatedResponses
    });
  }

  /**
   * Removes a management response from an observation
   */
  static async removeManagementResponse(observationId: string, responseId: string): Promise<void> {
    const observation = await this.getObservation(observationId);
    if (!observation) {
      throw new Error('Observation not found');
    }

    const updatedResponses = (observation.managementResponses || []).filter(response => response.id !== responseId);

    await this.updateObservation(observationId, {
      managementResponses: updatedResponses
    });
  }

  /**
   * Adds a KPI to an observation
   */
  static async addResolutionKPI(
    observationId: string, 
    kpi: Omit<ResolutionKPI, 'id' | 'createdAt' | 'updates' | 'currentValue' | 'lastUpdated' | 'status'>
  ): Promise<void> {
    const observation = await this.getObservation(observationId);
    if (!observation) {
      throw new Error('Observation not found');
    }

    // Calculate initial status based on target date
    const now = new Date();
    const targetDate = kpi.targetDate.toDate();
    const isOverdue = now > targetDate;
    
    // Create clean KPI object without undefined values
    const newKPI: ResolutionKPI = {
      ...kpi,
      id: `kpi_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: Timestamp.now(),
      updates: [],
      currentValue: 0,
      status: isOverdue ? 'overdue' : 'behind'
    };

    // Only add optional fields if they have values
    if (kpi.dateType === 'quarterly' && kpi.quarter !== undefined) {
      newKPI.quarter = kpi.quarter;
    }
    if (kpi.dateType === 'monthly' && kpi.month !== undefined) {
      newKPI.month = kpi.month;
    }
    if (kpi.linkedObservationIds && kpi.linkedObservationIds.length > 0) {
      newKPI.linkedObservationIds = kpi.linkedObservationIds;
    }
    if (kpi.linkedManagementResponseIds && kpi.linkedManagementResponseIds.length > 0) {
      newKPI.linkedManagementResponseIds = kpi.linkedManagementResponseIds;
    }

    const updatedKPIs = [...(observation.resolutionKPIs || []), newKPI];

    await this.updateObservation(observationId, {
      resolutionKPIs: updatedKPIs
    });
  }

  /**
   * Updates a KPI value
   */
  static async updateKPIValue(
    observationId: string, 
    kpiId: string, 
    update: Omit<KPIUpdate, 'id' | 'createdAt'>
  ): Promise<void> {
    const observation = await this.getObservation(observationId);
    if (!observation) {
      throw new Error('Observation not found');
    }

    const newUpdate: KPIUpdate = {
      ...update,
      id: `update_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: Timestamp.now()
    };

    const updatedKPIs = (observation.resolutionKPIs || []).map(kpi => {
      if (kpi.id === kpiId) {
        const updatedKPI = {
          ...kpi,
          currentValue: update.value,
          lastUpdated: Timestamp.now(),
          updates: [...kpi.updates, newUpdate]
        };

        // Calculate status based on progress and deadline
        const progress = (updatedKPI.currentValue / updatedKPI.targetValue) * 100;
        const now = new Date();
        const targetDate = updatedKPI.targetDate.toDate();
        const isOverdue = now > targetDate;

        if (progress >= 100) {
          updatedKPI.status = 'achieved';
        } else if (isOverdue) {
          updatedKPI.status = 'overdue';
        } else if (progress >= 80) {
          updatedKPI.status = 'on_track';
        } else if (progress >= 50) {
          updatedKPI.status = 'at_risk';
        } else {
          updatedKPI.status = 'behind';
        }

        return updatedKPI;
      }
      return kpi;
    });

    await this.updateObservation(observationId, {
      resolutionKPIs: updatedKPIs
    });
  }

  /**
   * Removes a KPI from an observation
   */
  static async removeResolutionKPI(observationId: string, kpiId: string): Promise<void> {
    const observation = await this.getObservation(observationId);
    if (!observation) {
      throw new Error('Observation not found');
    }

    const updatedKPIs = (observation.resolutionKPIs || []).filter(kpi => kpi.id !== kpiId);

    await this.updateObservation(observationId, {
      resolutionKPIs: updatedKPIs
    });
  }

  /**
   * Updates a KPI definition
   */
  static async updateKPIDefinition(
    observationId: string, 
    kpiId: string, 
    updates: Partial<ResolutionKPI>
  ): Promise<void> {
    const observation = await this.getObservation(observationId);
    if (!observation) {
      throw new Error('Observation not found');
    }

    // Filter out undefined values to avoid Firebase errors
    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([, value]) => value !== undefined)
    );

    const updatedKPIs = (observation.resolutionKPIs || []).map(kpi => {
      if (kpi.id === kpiId) {
        const updatedKPI = { ...kpi, ...cleanUpdates };
        
        // Only include optional fields if they have meaningful values
        if (updatedKPI.linkedObservationIds && updatedKPI.linkedObservationIds.length === 0) {
          delete updatedKPI.linkedObservationIds;
        }
        if (updatedKPI.linkedManagementResponseIds && updatedKPI.linkedManagementResponseIds.length === 0) {
          delete updatedKPI.linkedManagementResponseIds;
        }
        
        return updatedKPI;
      }
      return kpi;
    });

    await this.updateObservation(observationId, {
      resolutionKPIs: updatedKPIs
    });
  }
} 