"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  X,
  CalendarDays,
  Clock,
  MapPin,
  Users,
  FileText,
  Edit,
  Target,
  Settings,
  Trash2,
  AlertTriangle
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Meeting, MeetingsService } from "@/Firebase/firestore/services/MeetingsService";
import { TasksService, TaskPriority, TaskStatus } from "@/Firebase/firestore/services/TasksService";
import { useToast } from "@/components/ui/use-toast";
import { auth } from "@/Firebase/Authentication/authConfig";
import { Timestamp } from "firebase/firestore";
import { EditMeetingModal } from "@/components/ui/EditMeetingModal";

interface MeetingDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  meeting: Meeting;
  onUpdate: () => void;
  isRTL: boolean;
}

export function MeetingDetailsModal({ isOpen, onClose, meeting, onUpdate, isRTL }: MeetingDetailsModalProps) {
  const [isEditingSummary, setIsEditingSummary] = useState(false);
  const [summary, setSummary] = useState(meeting.summary || "");
  const [isLoading, setIsLoading] = useState(false);
  const [isAddingTask, setIsAddingTask] = useState(false);
  const [isEditingMeeting, setIsEditingMeeting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [newTask, setNewTask] = useState({
    title: "",
    description: "",
    assigneeEmail: "",
    priority: TaskPriority.MEDIUM,
    dueDate: ""
  });
  const { toast } = useToast();

  useEffect(() => {
    setSummary(meeting.summary || "");
  }, [meeting.summary]);

  const handleSaveSummary = async () => {
    try {
      setIsLoading(true);
      await MeetingsService.updateMeeting(meeting.id!, { summary });
      setIsEditingSummary(false);
      onUpdate();
      toast({
        title: isRTL ? "تم حفظ الملخص" : "Summary Saved",
        description: isRTL ? "تم حفظ ملخص الاجتماع بنجاح" : "Meeting summary has been saved successfully",
      });
    } catch (error) {
      console.error("Error saving summary:", error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Error Saving",
        description: isRTL ? "حدث خطأ أثناء حفظ الملخص" : "An error occurred while saving the summary",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTask = async () => {
    if (!newTask.title || !newTask.assigneeEmail) return;

    try {
      setIsLoading(true);
      const currentUser = auth.currentUser;
      if (!currentUser) throw new Error("User not authenticated");

      // Find the assignee from meeting attendees
      const assignee = meeting.attendees.find(a => a.email === newTask.assigneeEmail);
      if (!assignee) throw new Error("Assignee not found");

      const taskData = {
        title: newTask.title,
        description: `${newTask.description}\n\nRelated to meeting: ${meeting.title}\nMeeting Date: ${meeting.meetingDate.toDate().toLocaleDateString()}`,
        status: TaskStatus.PENDING,
        priority: newTask.priority,
        assignees: [{
          uid: assignee.consultantId || assignee.email,
          displayName: assignee.name,
          email: assignee.email,
          role: assignee.role || 'Attendee'
        }],
        createdBy: currentUser.uid,
        createdByName: currentUser.displayName || currentUser.email?.split('@')[0] || 'User',
        dueDate: newTask.dueDate ? Timestamp.fromDate(new Date(newTask.dueDate)) : undefined,
        tags: ['meeting-related', meeting.meetingType, 'action-item']
      };

      await TasksService.createTask(taskData);

      setIsAddingTask(false);
      setNewTask({
        title: "",
        description: "",
        assigneeEmail: "",
        priority: TaskPriority.MEDIUM,
        dueDate: ""
      });

      toast({
        title: isRTL ? "تم إنشاء المهمة" : "Task Created",
        description: isRTL ? "تم إنشاء المهمة المرتبطة بالاجتماع بنجاح" : "Meeting-related task has been created successfully",
      });
    } catch (error) {
      console.error("Error creating task:", error);
      toast({
        title: isRTL ? "خطأ في إنشاء المهمة" : "Error Creating Task",
        description: isRTL ? "حدث خطأ أثناء إنشاء المهمة" : "An error occurred while creating the task",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteMeeting = async () => {
    try {
      setIsLoading(true);
      await MeetingsService.deleteMeeting(meeting.id!);

      toast({
        title: isRTL ? "تم حذف الاجتماع" : "Meeting Deleted",
        description: isRTL ? "تم حذف الاجتماع بنجاح" : "Meeting has been deleted successfully",
      });

      onUpdate();
      onClose();
    } catch (error) {
      console.error("Error deleting meeting:", error);
      toast({
        title: isRTL ? "خطأ في الحذف" : "Error Deleting",
        description: isRTL ? "حدث خطأ أثناء حذف الاجتماع" : "An error occurred while deleting the meeting",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-gray-900/20 backdrop-blur-sm"
        onClick={onClose}
      />
      
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="relative bg-white rounded-2xl shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-[var(--brand-blue)]/5 to-[var(--brand-blue)]/10">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-[var(--brand-blue)]/10 rounded-xl flex items-center justify-center">
              <CalendarDays className="w-6 h-6 text-[var(--brand-blue)]" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{meeting.title}</h2>
              <div className="flex items-center gap-4 mt-1">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <CalendarDays className="w-4 h-4" />
                  <span>
                    {meeting.meetingDate.toDate().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock className="w-4 h-4" />
                  <span>
                    {meeting.meetingDate.toDate().toLocaleTimeString(isRTL ? 'ar-SA' : 'en-US', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })} ({meeting.duration} {isRTL ? 'دقيقة' : 'min'})
                  </span>
                </div>
                {meeting.location && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <MapPin className="w-4 h-4" />
                    <span>{meeting.location}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => setIsAddingTask(true)}
              className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100"
            >
              <Target className="w-4 h-4 mr-2" />
              {isRTL ? "إضافة مهمة" : "Add Task"}
            </Button>

            <Button
              variant="outline"
              onClick={() => setIsEditingMeeting(true)}
              className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100"
            >
              <Settings className="w-4 h-4 mr-2" />
              {isRTL ? "تحرير الاجتماع" : "Edit Meeting"}
            </Button>

            <Button
              variant="outline"
              onClick={() => setShowDeleteConfirm(true)}
              className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {isRTL ? "حذف الاجتماع" : "Delete Meeting"}
            </Button>

            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-140px)] p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Meeting Info */}
            <div className="lg:col-span-1 space-y-6">
              {/* Meeting Details */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {isRTL ? "تفاصيل الاجتماع" : "Meeting Details"}
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {isRTL ? "النوع" : "Type"}
                    </label>
                    <Badge variant="outline" className="ml-2">
                      {meeting.meetingType}
                    </Badge>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {isRTL ? "الحالة" : "Status"}
                    </label>
                    <Badge variant="outline" className="ml-2">
                      {meeting.status}
                    </Badge>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {isRTL ? "المنظم" : "Organizer"}
                    </label>
                    <p className="text-gray-900">{meeting.organizer.name}</p>
                    <p className="text-sm text-gray-600">{meeting.organizer.email}</p>
                  </div>
                  
                  {meeting.description && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        {isRTL ? "الوصف" : "Description"}
                      </label>
                      <p className="text-gray-900 mt-1">{meeting.description}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Attendees */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Users className="w-5 h-5 text-[var(--brand-blue)]" />
                  {isRTL ? "المشاركون" : "Attendees"} ({meeting.attendees.length})
                </h3>
                
                <div className="space-y-3">
                  {meeting.attendees.map((attendee, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${
                        attendee.isConsultant ? 'bg-[var(--brand-blue)]' : 'bg-gray-500'
                      }`}>
                        {attendee.name.charAt(0).toUpperCase()}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{attendee.name}</p>
                        <p className="text-sm text-gray-600">{attendee.email}</p>
                        {attendee.role && (
                          <p className="text-xs text-gray-500">{attendee.role}</p>
                        )}
                      </div>
                      {attendee.isConsultant && (
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                          {isRTL ? "استشاري" : "Consultant"}
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Agenda */}
              {meeting.agenda && meeting.agenda.length > 0 && (
                <div className="bg-gray-50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <FileText className="w-5 h-5 text-[var(--brand-blue)]" />
                    {isRTL ? "جدول الأعمال" : "Agenda"}
                  </h3>
                  
                  <div className="space-y-2">
                    {meeting.agenda.map((item, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <span className="w-6 h-6 bg-[var(--brand-blue)] text-white rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
                          {index + 1}
                        </span>
                        <p className="text-gray-900 flex-1">{item}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Summary and Action Points */}
            <div className="lg:col-span-2 space-y-6">
              {/* Meeting Summary */}
              <div className="bg-white border border-gray-200 rounded-xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {isRTL ? "ملخص الاجتماع" : "Meeting Summary"}
                  </h3>
                  {!isEditingSummary && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsEditingSummary(true)}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      {isRTL ? "تحرير" : "Edit"}
                    </Button>
                  )}
                </div>
                
                {isEditingSummary ? (
                  <div className="space-y-4">
                    <Textarea
                      value={summary}
                      onChange={(e) => setSummary(e.target.value)}
                      placeholder={isRTL ? "أدخل ملخص الاجتماع..." : "Enter meeting summary..."}
                      rows={6}
                    />
                    <div className="flex items-center gap-3">
                      <Button
                        onClick={handleSaveSummary}
                        disabled={isLoading}
                        className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
                      >
                        {isLoading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />}
                        {isRTL ? "حفظ" : "Save"}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setIsEditingSummary(false);
                          setSummary(meeting.summary || "");
                        }}
                      >
                        {isRTL ? "إلغاء" : "Cancel"}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-gray-900">
                    {summary ? (
                      <p className="whitespace-pre-wrap">{summary}</p>
                    ) : (
                      <p className="text-gray-500 italic">
                        {isRTL ? "لم يتم إضافة ملخص بعد" : "No summary added yet"}
                      </p>
                    )}
                  </div>
                )}
              </div>

              {/* Add Task Form */}
              {isAddingTask && (
                <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    {isRTL ? "إضافة مهمة جديدة" : "Add New Task"}
                  </h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL ? "عنوان المهمة" : "Task Title"} *
                      </label>
                      <Input
                        value={newTask.title}
                        onChange={(e) => setNewTask(prev => ({ ...prev, title: e.target.value }))}
                        placeholder={isRTL ? "أدخل عنوان المهمة" : "Enter task title"}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL ? "وصف المهمة" : "Task Description"}
                      </label>
                      <Textarea
                        value={newTask.description}
                        onChange={(e) => setNewTask(prev => ({ ...prev, description: e.target.value }))}
                        placeholder={isRTL ? "وصف تفصيلي للمهمة" : "Detailed task description"}
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {isRTL ? "المكلف" : "Assignee"} *
                        </label>
                        <Select value={newTask.assigneeEmail} onValueChange={(value) => setNewTask(prev => ({ ...prev, assigneeEmail: value }))}>
                          <SelectTrigger>
                            <SelectValue placeholder={isRTL ? "اختر المكلف" : "Select assignee"} />
                          </SelectTrigger>
                          <SelectContent>
                            {meeting.attendees.map((attendee, index) => (
                              <SelectItem key={index} value={attendee.email}>
                                <div className="flex items-center gap-2">
                                  <span>{attendee.name}</span>
                                  {attendee.isConsultant && (
                                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                                      {isRTL ? "استشاري" : "Consultant"}
                                    </Badge>
                                  )}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {isRTL ? "الأولوية" : "Priority"}
                        </label>
                        <Select value={newTask.priority} onValueChange={(value) => setNewTask(prev => ({ ...prev, priority: value as TaskPriority }))}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={TaskPriority.LOW}>{isRTL ? "منخفضة" : "Low"}</SelectItem>
                            <SelectItem value={TaskPriority.MEDIUM}>{isRTL ? "متوسطة" : "Medium"}</SelectItem>
                            <SelectItem value={TaskPriority.HIGH}>{isRTL ? "عالية" : "High"}</SelectItem>
                            <SelectItem value={TaskPriority.URGENT}>{isRTL ? "عاجلة" : "Urgent"}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {isRTL ? "تاريخ الاستحقاق" : "Due Date"}
                        </label>
                        <Input
                          type="date"
                          value={newTask.dueDate}
                          onChange={(e) => setNewTask(prev => ({ ...prev, dueDate: e.target.value }))}
                        />
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <Button
                        onClick={handleCreateTask}
                        disabled={isLoading || !newTask.title || !newTask.assigneeEmail}
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        {isLoading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />}
                        {isRTL ? "إنشاء المهمة" : "Create Task"}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setIsAddingTask(false)}
                      >
                        {isRTL ? "إلغاء" : "Cancel"}
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Action Points Note */}
              {!isAddingTask && (
                <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-blue-900 mb-2">
                    {isRTL ? "نقاط العمل" : "Action Points"}
                  </h3>
                  <p className="text-blue-800">
                    {isRTL
                      ? "يمكن إضافة نقاط العمل كمهام منفصلة باستخدام زر 'إضافة مهمة' أعلاه. هذا يتيح تتبع أفضل وإدارة أكثر فعالية للمهام المرتبطة بالاجتماع."
                      : "Action points can be added as separate tasks using the 'Add Task' button above. This allows for better tracking and more effective management of meeting-related tasks."
                    }
                  </p>
                </div>
              )}

              {/* Notes */}
              {meeting.notes && (
                <div className="bg-gray-50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    {isRTL ? "ملاحظات" : "Notes"}
                  </h3>
                  <p className="text-gray-900 whitespace-pre-wrap">{meeting.notes}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Edit Meeting Modal */}
      <EditMeetingModal
        isOpen={isEditingMeeting}
        onClose={() => setIsEditingMeeting(false)}
        meeting={meeting}
        onUpdate={() => {
          onUpdate();
          setIsEditingMeeting(false);
        }}
        isRTL={isRTL}
      />

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 z-[60] flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-gray-900/50 backdrop-blur-sm"
            onClick={() => setShowDeleteConfirm(false)}
          />

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="relative bg-white rounded-2xl shadow-xl max-w-md w-full p-6"
          >
            <div className="flex items-center gap-4 mb-4">
              <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900">
                  {isRTL ? "تأكيد الحذف" : "Confirm Deletion"}
                </h3>
                <p className="text-sm text-gray-600">
                  {isRTL ? "هذا الإجراء لا يمكن التراجع عنه" : "This action cannot be undone"}
                </p>
              </div>
            </div>

            <p className="text-gray-700 mb-6">
              {isRTL
                ? `هل أنت متأكد من أنك تريد حذف الاجتماع "${meeting.title}"؟ سيتم حذف جميع البيانات المرتبطة بهذا الاجتماع نهائياً.`
                : `Are you sure you want to delete the meeting "${meeting.title}"? All data associated with this meeting will be permanently removed.`
              }
            </p>

            <div className="flex items-center gap-3 justify-end">
              <Button
                variant="outline"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isLoading}
              >
                {isRTL ? "إلغاء" : "Cancel"}
              </Button>
              <Button
                onClick={handleDeleteMeeting}
                disabled={isLoading}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                ) : (
                  <Trash2 className="w-4 h-4 mr-2" />
                )}
                {isRTL ? "حذف نهائياً" : "Delete Permanently"}
              </Button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
}
