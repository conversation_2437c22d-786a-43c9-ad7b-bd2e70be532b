import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow processing up to 2 hours for large page analysis with 1M context
export const maxDuration = 300; // 5 minutes

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Schema for page classification response
const PageClassificationSchema = z.object({
  records: z.array(z.object({
    recordId: z.string(),
    confidentialityLevel: z.enum(["Public", "Confidential", "Secret", "Top Secret"]),
    confidentialityReasoning: z.string(),
    hasPersonalData: z.boolean(),
    personalDataReasoning: z.string()
  }))
});

// Schema for request body
const RequestSchema = z.object({
  pageData: z.array(z.object({
    id: z.string(),
    schemaName: z.string().optional(),
    tableName: z.string(),
    columnName: z.string(),
    dataType: z.string(),
    maxLength: z.number().nullable().optional(),
    isNullable: z.boolean().nullable().optional(),
    tableType: z.enum(["system_table", "data_table"]).optional(),
    dataCategory: z.enum(["customers", "development_team"]).optional()
  })),
  systemId: z.string(),
  pageNumber: z.number(),
  startIndex: z.number(),
  endIndex: z.number(),
  systemContext: z.string().optional()
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    const { pageData, systemId, pageNumber, startIndex, endIndex, systemContext } = RequestSchema.parse(body);

    if (!pageData || pageData.length === 0) {
      return NextResponse.json(
        { error: 'No page data provided for classification' },
        { status: 400 }
      );
    }

    // Get system information
    let systemName = '';
    try {
      // Import SystemsService dynamically to avoid circular dependencies
      const { SystemsService } = await import('@/Firebase/firestore/SystemsService');
      const systems = await SystemsService.getSystems();
      const currentSystem = systems.find(sys => sys.id === systemId);

      if (currentSystem) {
        systemName = currentSystem.name;
        console.log(`System identified: "${systemName}"`);
      } else {
        console.log('System not found, using generic context');
      }
    } catch (error) {
      console.error('Error fetching system info:', error);
      console.log('Failed to fetch system info, using generic context');
    }

    // Get comprehensive system context from Firebase
    console.log('System context received:', systemContext ? `"${systemContext}"` : 'null/undefined');
    let businessContext = '';
    let detailedSystemContext = '';

    try {
      // Import SystemsService to get all context points
      const { SystemsService } = await import('@/Firebase/firestore/SystemsService');
      const contextPoints = await SystemsService.getSystemContextPoints(systemId);

      if (contextPoints && contextPoints.length > 0) {
        // Group context points by tag for better organization
        const contextByTag = contextPoints.reduce((acc, point) => {
          if (!acc[point.tag]) {
            acc[point.tag] = [];
          }
          acc[point.tag].push(point.content);
          return acc;
        }, {} as Record<string, string[]>);

        // Build comprehensive context
        const contextSections = [];

        if (contextByTag['System Description']) {
          contextSections.push(`SYSTEM DESCRIPTION:
${contextByTag['System Description'].join('\n\n')}`);
        }

        if (contextByTag['System Personas']) {
          contextSections.push(`SYSTEM PERSONAS & USERS:
${contextByTag['System Personas'].join('\n\n')}`);
        }

        if (contextByTag['System Service Brief']) {
          contextSections.push(`SYSTEM SERVICES & FUNCTIONALITY:
${contextByTag['System Service Brief'].join('\n\n')}`);
        }

        detailedSystemContext = contextSections.join('\n\n');

        businessContext = `

COMPREHENSIVE BUSINESS CONTEXT FOR THIS SYSTEM:
${detailedSystemContext}

This detailed context describes the specific business functions, user personas, services, and processes that this database supports. Use this context extensively to understand the business purpose of each database field.`;

        console.log(`Using comprehensive Firebase system context with ${contextPoints.length} context points`);
      } else if (systemContext && systemContext.trim()) {
        // Fallback to legacy context if no context points
        businessContext = `

ACTUAL BUSINESS CONTEXT FOR THIS SYSTEM:
${systemContext}

This context describes the specific business functions and processes that this database supports.`;
        console.log('Using legacy Firebase system context');
      } else {
        console.log('No system context available - using system name for context');
      }
    } catch (contextError) {
      console.error('Error fetching detailed system context:', contextError);
      // Fallback to provided context
      if (systemContext && systemContext.trim()) {
        businessContext = `

ACTUAL BUSINESS CONTEXT FOR THIS SYSTEM:
${systemContext}

This context describes the specific business functions and processes that this database supports.`;
        console.log('Using fallback system context due to error');
      }
    }

    // Prepare prompt for AI classification with comprehensive business context
    let systemOverview = '';
    let businessEntityContext = '';

    // Always use comprehensive context-driven approach
    systemOverview = `You are classifying database fields for the "${systemName}" system.

COMPREHENSIVE SYSTEM ANALYSIS:
${businessContext}

This system requires detailed analysis based on the comprehensive business context provided above. Use the system description, user personas, and service functionality to understand the business purpose of each database field.`;

    // Generate business entity context based on actual system context and table analysis
    const uniqueTables = Array.from(new Set(pageData.map(record => record.tableName)));
    businessEntityContext = `Based on the comprehensive system context and table names in this ${systemName} system, here are the key business entities:
${uniqueTables.map(tableName => {
      // Use context-driven analysis for each table
      return `- ${tableName}: Analyze this table in the context of the system description, user personas, and services described above`;
    }).join('\n')}

CONTEXT-DRIVEN TABLE ANALYSIS:
For each table, consider:
1. How does this table support the specific business functions described in the system context?
2. Which user personas (from the context) would interact with this table?
3. How does this table relate to the services and functionality described in the context?
4. What business processes (mentioned in the context) does this table enable?`;

    const prompt = `CRITICAL: You are FORBIDDEN from using ANY uncertain language. BANNED WORDS include: "likely", "maybe", "probably", "appears", "seems", "could", "might", "most likely", "presumably", "possibly", "perhaps", "would", "should", "may", "can be", "tends to", "suggests", "indicates", "implies", "typically", "generally", "usually", "often", "sometimes", "occasionally", "conceivably", "supposedly". Use ONLY definitive statements.

${systemOverview}

COMPREHENSIVE SYSTEM ANALYSIS - YOU ARE AN EXPERT IN THIS SPECIFIC BUSINESS:
You are using Gemini-2.5-pro with 1M token context window. You are now an expert consultant who has deep knowledge of this specific business system. You understand the exact business processes, the specific user types, and how each service operates. When you analyze database fields, you think like someone who has worked with this system for years and understands every business nuance described in the context above.

CRITICAL: You must write as if you are intimately familiar with this specific business. Reference the exact business processes, mention the specific user types by name, and explain how each field impacts the particular services described. Do not use generic business language - use the specific terminology and concepts from the context as if this is your area of expertise.

DEFINITIVE LANGUAGE REQUIREMENT: NEVER use uncertain language. BANNED WORDS/PHRASES include: "likely", "maybe", "probably", "appears to", "seems to", "could be", "might be", "potentially", "most likely", "presumably", "supposedly", "possibly", "conceivably", "perhaps", "may be", "would be", "should be", "tends to", "suggests", "indicates", "implies", "often", "typically", "generally", "usually", "commonly", "frequently", "sometimes", "occasionally", "might", "could", "would", "should", "may", "can be", "is likely", "appears", "seems", "looks like", "sounds like", "feels like". Write with absolute certainty and expertise. You KNOW this business and you KNOW what each field does.

CRITICAL CLASSIFICATION RULES:

1. DATABASE KEY FIELDS (CONFIDENTIAL):
   - PRIMARY KEYS: Any field that serves as a primary key for a table
   - FOREIGN KEYS: Any field that references primary keys in other tables (usually ends with "ID", "Key", or "_id")
   - Fields like "UserID", "CustomerID", "OrderID", "ProductKey", "ReferenceID", etc.
   - Classification: CONFIDENTIAL - reveals database relationships and business structure
   - Reasoning: Exposes database architecture, business relationships, and data linking patterns that could be used to understand system structure

2. SYSTEM METADATA FIELDS - CONTEXT-AWARE CLASSIFICATION:

   a) USER IDENTITY AUDIT FIELDS - CONTEXT DEPENDENT:
      - "CreatedBy", "UpdatedBy", "ModifiedBy", "DeletedBy", "ApprovedBy", "ReviewedBy"
      - ANALYSIS REQUIRED: Determine if these contain personal identifiers or system IDs
      - IF contains usernames, email addresses, or personal names: CONFIDENTIAL + PERSONAL DATA
      - IF contains system user IDs or role codes: CONFIDENTIAL + NOT PERSONAL DATA
      - Reasoning: User identity fields can contain personal data depending on the identification method used

   b) USER ID FIELDS - CONTEXT DEPENDENT:
      - "CreatorId", "UserId", "ClientId", "TenantId", "OwnerId", "AssigneeId", etc.
      - ANALYSIS REQUIRED: Determine the nature of the ID system
      - IF IDs are linked to personal accounts or individual users: CONFIDENTIAL + PERSONAL DATA
      - IF IDs are system-generated tokens or organizational codes: CONFIDENTIAL + NOT PERSONAL DATA
      - Reasoning: User IDs can be personal data if they relate to identifiable individuals

   c) CLIENT/TENANT NAMES - CONTEXT DEPENDENT:
      - "ClientName", "TenantName", "CompanyName", "OrganizationName"
      - ANALYSIS REQUIRED: Determine if names identify individuals or organizations
      - IF names identify individual persons or sole proprietors: CONFIDENTIAL + PERSONAL DATA
      - IF names identify companies, organizations, or business entities: CONFIDENTIAL + NOT PERSONAL DATA
      - Reasoning: Names can be personal data when they identify natural persons

   d) FREE TEXT FIELDS - HIGH RISK FOR PERSONAL DATA:
      - "Comments", "Description", "Notes", "Arguments", "Parameters", "ExtraProperties", "Remarks", "Details"
      - ANALYSIS REQUIRED: Assess the business context and purpose
      - DEFAULT ASSUMPTION: CONFIDENTIAL + PERSONAL DATA (unless context clearly indicates otherwise)
      - Reasoning: Free text fields have high potential to contain personal information, names, or identifying details

   e) SYSTEM CONTROL FIELDS (SECRET):
      - "IsActive", "IsDeleted", "IsEnabled", "IsVisible", "IsPublished", "IsApproved", "Status", "Version"
      - Fields controlling system behavior, visibility, and operational state
      - Classification: SECRET - reveals internal system logic and administrative controls
      - Reasoning: Exposes system architecture, business rules, and administrative decision-making patterns

   f) TIMESTAMP FIELDS (CONFIDENTIAL):
      - "CreatedOn", "UpdatedOn", "LastModified", "DeletedOn", "AccessedOn"
      - Classification: CONFIDENTIAL - operational metadata but not personal data
      - Reasoning: Shows activity patterns and timeline but doesn't identify individuals

3. AUTHENTICATION & SECURITY FIELDS (TOP SECRET):
   - Passwords, tokens, keys, hashes, secrets, API keys, session data
   - Classification: TOP SECRET - critical security credentials

4. BUSINESS DATA CLASSIFICATION:
   - Financial data (amounts, bills, payments): CONFIDENTIAL - business sensitive
   - Personal identifiers (names, emails, phones): CONFIDENTIAL - individual privacy
   - Business names, categories, project names: CONFIDENTIAL - even if publicly available, they're confidential in business context
   - Configuration/paths/files: SECRET - system architecture exposure

5. PUBLIC CLASSIFICATION (USE SPARINGLY):
   - ONLY for truly generic, non-identifying system elements
   - Standard UI labels ("Submit", "Cancel", "Next", "Previous")
   - Generic status values ("Active", "Inactive", "Pending", "Complete") - ONLY if they don't reveal business processes
   - Common data types and system references that reveal nothing about business operations
   - Names of Organizations Public Available Licences Certificates ETC . If Anything i can find in the public domain without access , it should be classified as Public .
   -Lets say i have a system that offers me to get some certifications , licences , certificates , etc . If i can find the name of the organization in the public domain , it should be classified as Public .
   
   IMPORTANT: Just because something is "available publicly" doesn't make it PUBLIC classification:
   - Employee names on company website → Still CONFIDENTIAL in internal systems
   - Product names in public catalogs → Still CONFIDENTIAL in internal databases  
   - Department names publicly known → Still CONFIDENTIAL in internal assignments
   - Client names in public records → Still CONFIDENTIAL in business relationships
   
   PUBLIC is ONLY for generic system elements with zero business identification value.

4. PRACTICAL CLASSIFICATION PRINCIPLE:
   - Use the LOWEST appropriate classification level that still protects the data
   - Basic audit trails (who/when) are CONFIDENTIAL - needed for regulatory compliance
   - System control mechanisms are SECRET - reveal internal architecture
   - Only use TOP SECRET for authentication credentials and critical security data
   - Consider operational necessity vs security risk

${businessEntityContext}

BEFORE CLASSIFICATION - ASSUME THE ROLE OF A BUSINESS INSIDER:
You are now a senior consultant who has worked extensively with this specific business system. You understand every nuance of how it operates. You have been provided with comprehensive system context above including:
${detailedSystemContext ? `
ACTUAL SYSTEM CONTEXT PROVIDED:
${detailedSystemContext}

As a business expert in this domain, you must demonstrate your deep understanding by using the EXACT terminology, user persona names, service names, and business processes described above. Write as if you have intimate knowledge of how this business operates and how each database field supports the specific operations described.` : 'No detailed context provided - use system name only.'}

FIELDS TO CLASSIFY AS A BUSINESS DOMAIN EXPERT:

${pageData.map(record => `
RECORD ID: ${record.id} (internal tracking only - ignore this)
DATABASE FIELD: ${record.tableName}.${record.columnName}
DATA TYPE: ${record.dataType}
ANALYZE AS BUSINESS EXPERT: How does this field support the specific business operations described in the context?
CONNECT TO BUSINESS: Which exact user personas and services from the context rely on this field?
BUSINESS IMPACT: How would exposing this field affect the particular business outcomes described in the context?
`).join('\n')}

CRITICAL CLASSIFICATION ANALYSIS REQUIREMENTS:

CONTEXT-DEPENDENT FIELD ANALYSIS:
You MUST perform deep contextual analysis for these problematic field types:

1. USER IDENTITY FIELDS ("CreatedBy", "UpdatedBy", "ModifiedBy"):
   - Analyze the system context to determine if these store personal names or system identifiers
   - Look for clues in the business context about user management approach
   - Consider if the system serves individual users vs organizational users
   - PERSONAL DATA if: stores usernames, email addresses, or personal names
   - NOT PERSONAL DATA if: stores system-generated IDs, role codes, or department codes

2. USER ID FIELDS ("CreatorId", "UserId", "ClientId", "TenantId"):
   - Determine if IDs are linked to identifiable individuals or system entities
   - Consider the business model: B2C systems more likely to have personal user IDs
   - PERSONAL DATA if: IDs can be traced back to individual persons
   - NOT PERSONAL DATA if: IDs represent organizational accounts, system roles, or anonymous tokens

3. NAME FIELDS ("ClientName", "TenantName", "CompanyName"):
   - Distinguish between individual person names and organizational names
   - Consider the business context: who are the primary users/customers?
   - PERSONAL DATA if: names identify natural persons, sole proprietors, or individual professionals
   - NOT PERSONAL DATA if: names identify companies, organizations, departments, or business entities

4. FREE TEXT FIELDS ("Comments", "Description", "Notes", "Arguments", "Parameters"):
   - Assess the business purpose and typical content of these fields
   - Consider who enters the data and what they typically write
   - DEFAULT to PERSONAL DATA unless context clearly indicates otherwise
   - PERSONAL DATA if: fields allow user-generated content, feedback, or personal notes
   - NOT PERSONAL DATA if: fields contain only system-generated codes, technical parameters, or standardized categories

ENHANCED REASONING REQUIREMENTS:

You MUST analyze each DATABASE FIELD individually using the comprehensive business context provided above. DO NOT use generic templates or copy-paste reasoning.

IMPORTANT: The "RECORD ID" is just our internal tracking - IGNORE IT. Focus only on the actual database column name.

For each DATABASE FIELD, demonstrate your expertise in this specific business by:
1. Analyzing the field as someone who understands this exact business domain
2. Naming the SPECIFIC user personas from the context who interact with this field (use their exact names/types)
3. Explaining how this field supports the SPECIFIC services mentioned in the context by name
4. Describing the business impact using the EXACT processes and workflows from the context
5. Connecting the field to the PARTICULAR business outcomes described in the context
6. Using the business terminology as if you are an industry expert in this specific domain
7. Showing how the field affects the SPECIFIC business operations and service delivery described

BUSINESS EXPERT REQUIREMENTS:
- Write as if you have deep expertise in this specific business domain
- Use the EXACT names of user personas, services, and processes from the context
- Reference specific business outcomes and impacts mentioned in the context
- Demonstrate understanding of how this business operates by using context-specific language
- Connect each field to the particular business value and service delivery described
- Show knowledge of the specific workflows, user interactions, and business processes
- Never use generic terms - always use the specific business language from the context
- Write with absolute certainty - COMPLETELY BANNED: "likely", "maybe", "probably", "appears", "seems", "could", "might", "most likely", "presumably", "possibly", "perhaps", "would", "should", "may", "can be", "tends to", "suggests", "indicates", "implies", "typically", "generally", "usually", "often", "sometimes"

CONFIDENTIALITY REASONING - DETAILED AND SPECIFIC:
- MANDATORY: Write detailed, specific reasoning that explains the exact nature of the data
- For USER IDENTITY fields: Specify whether the field contains personal names, usernames, email addresses, or system IDs
- For USER ID fields: Explain whether the IDs are linked to individual persons or organizational entities
- For NAME fields: Clarify whether names identify natural persons or business entities
- For FREE TEXT fields: Describe the typical content and explain why it may or may not contain personal data
- Quote the EXACT user personas mentioned in the context (not "authorized professionals" but the actual persona names/types)
- Reference the SPECIFIC business processes and services by name from the context
- Explain how exposing this field would impact the PARTICULAR business operations described in the context
- Connect the field to the EXACT services and their business impact as described in the context
- Use the business terminology from the context as if you work in this industry
- Example: "This CreatedBy field stores [personal usernames/system IDs] for [exact service name from context]. Given that [specific user persona from context] relies on this for [specific business process from context], and the field contains [personal identifiers/system codes], exposure would compromise [specific business impact from context]."

PERSONAL DATA REASONING - CONTEXT-AWARE ANALYSIS:
- MANDATORY: Explicitly state what type of data the field contains and why it is/isn't personal data
- For USER IDENTITY fields: "This field contains [personal usernames/email addresses/system IDs] because..."
- For USER ID fields: "This field contains [individual user identifiers/organizational account codes] because..."
- For NAME fields: "This field contains [individual person names/company names/department names] because..."
- For FREE TEXT fields: "This field allows [user-generated content/system parameters/standardized codes] which [can/cannot] contain personal information because..."
- Write as if you understand exactly how this business operates and who uses each field
- Name the SPECIFIC user personas from the context (not generic "users" but the actual personas described)
- Explain how the field relates to the SPECIFIC workflows and business processes mentioned in the context
- Reference the EXACT services and how personal data flows through them as described in the context
- Show understanding of the business impact by connecting to specific operations from the context
- Example: "This CreatedBy field [contains personal usernames/contains system role IDs] because [specific business reason from context]. The [exact user persona name from context] uses this when [specific workflow from context], and since it stores [personal identifiers/system codes], it [is/is not] personal data affecting [specific service/business outcome from context]."

CRITICAL BUSINESS EXPERTISE REQUIREMENT:
You are now a domain expert in this specific business. Every reasoning must demonstrate deep understanding by:
- Using the EXACT names of user personas from the context (not "users" or "professionals")
- Referencing the SPECIFIC services and business processes by their exact names from the context
- Explaining business impact using the particular terminology and concepts from the context
- Showing how each field affects the specific business outcomes and service delivery described
- Writing as if you have years of experience working with this exact business system
- Speaking with absolute certainty - ZERO TOLERANCE for uncertain language: "likely", "maybe", "probably", "appears", "seems", "could", "might", "potentially", "most likely", "presumably", "possibly", "perhaps", "would", "should", "may", "can be", "tends to", "suggests", "indicates", "implies", "typically", "generally", "usually", "often", "sometimes", "occasionally"

CRITICAL: Every field must have DIFFERENT reasoning that reflects its specific name, table context, and data purpose.

SPECIFIC CLASSIFICATION EXAMPLES:

GOOD EXAMPLES - CONTEXT-AWARE REASONING:
✓ "The CreatedBy field in UserAccounts table stores individual user email addresses that identify specific persons who created accounts. Since this system serves individual customers and the field contains personal email addresses, this is personal data."

✓ "The ClientName field in Projects table stores company names like 'ABC Corporation' and 'XYZ Ltd' representing business entities. Since these are organizational names rather than individual person names, this is not personal data."

✓ "The Comments field in SupportTickets table allows customers to enter free-form text describing their issues. This field regularly contains personal information such as names, contact details, and personal circumstances, making it personal data."

✓ "The UserId field in AuditLog table contains system-generated numeric identifiers (e.g., 'USR_12345') that reference user accounts. While these IDs are linked to individual users, they are system-generated tokens rather than direct personal identifiers."

BAD EXAMPLES - VAGUE REASONING:
✗ "This field contains user information that could be sensitive" (too vague)
✗ "This field might contain personal data depending on usage" (uncertain language)
✗ "This field is used by authorized professionals for business operations" (generic terms)
✗ "This field appears to store user-related data" (uncertain language)

ANTI-TEMPLATING RULES:
- No two database columns should have identical or similar reasoning
- Each reasoning must mention the specific COLUMN NAME or its unique purpose
- For KEY FIELDS: Always identify if it's a primary key or foreign key and explain the specific relationship it creates
- NEVER use generic terms like "authorized professionals", "users", "business operations" - use the EXACT persona names and business terms from the context
- NEVER say "system personas" - use the actual persona names and roles described in the context
- ABSOLUTELY FORBIDDEN uncertain language: "likely", "maybe", "probably", "appears to", "seems to", "could be", "might be", "potentially", "most likely", "presumably", "possibly", "perhaps", "would be", "should be", "may be", "can be", "tends to", "suggests", "indicates", "implies", "typically", "generally", "usually", "often", "sometimes", "occasionally" - write with absolute certainty
- Use concrete, specific descriptions of what each COLUMN actually contains
- Reference the actual table name and COLUMN NAME for each database field
- Never reference the internal record ID (like "KXBllF9TepnA0Cx9NWs0") in your reasoning

PRIMARY/FOREIGN KEY DETECTION:
- Fields ending in "ID", "Key", "_id", "Ref" are usually keys
- Fields named exactly like other table names + "ID" are foreign keys
- Fields like "ID", "PrimaryKey", "UniqueID" in a table are primary keys
- All key fields should be classified as CONFIDENTIAL minimum

FINAL BUSINESS EXPERTISE VERIFICATION:
Before responding, verify that EVERY reasoning demonstrates you are a domain expert by:
1. Using the EXACT names of user personas from the context (never generic terms like "users")
2. Referencing SPECIFIC services and business processes by their exact names from the context
3. Explaining business impact using the PARTICULAR terminology from the system description
4. Showing deep understanding of how this specific business operates
5. Connecting each field to the EXACT business outcomes and service delivery described in the context
6. Writing as if you have intimate knowledge of this business domain
7. Speaking with absolute certainty - NO uncertain language whatsoever

DECISION TREES FOR PROBLEMATIC FIELD TYPES:

1. FOR FIELDS ENDING IN "By" (CreatedBy, UpdatedBy, ModifiedBy):
   - Does the system context indicate individual user accounts? → PERSONAL DATA
   - Does the system context indicate role-based or department-based tracking? → NOT PERSONAL DATA
   - Does the business serve individual customers/citizens? → PERSONAL DATA
   - Does the business serve only organizations/departments? → NOT PERSONAL DATA

2. FOR FIELDS ENDING IN "Id" (UserId, ClientId, CreatorId):
   - Are these IDs linked to individual person accounts? → PERSONAL DATA
   - Are these IDs organizational/system identifiers? → NOT PERSONAL DATA
   - Does the system context mention individual users/customers? → PERSONAL DATA
   - Does the system context mention only business entities? → NOT PERSONAL DATA

3. FOR NAME FIELDS (ClientName, TenantName, CompanyName):
   - Does the business context indicate individual customers? → PERSONAL DATA
   - Does the business context indicate corporate clients? → NOT PERSONAL DATA
   - Are these names of natural persons? → PERSONAL DATA
   - Are these names of legal entities/organizations? → NOT PERSONAL DATA

4. FOR FREE TEXT FIELDS (Comments, Description, Notes):
   - Can users enter personal information in these fields? → PERSONAL DATA
   - Are these fields system-generated or restricted to codes? → NOT PERSONAL DATA
   - Do these fields allow user-generated content? → PERSONAL DATA
   - Are these fields technical parameters only? → NOT PERSONAL DATA

BUSINESS DOMAIN EXPERTISE CHECK:
- Every reasoning must sound like it comes from someone who works in this specific business
- Use the exact business language and terminology from the context
- Reference the specific user personas, services, and processes by their actual names
- Demonstrate understanding of the particular business value and operational impact
- Show knowledge of how each field supports the specific business outcomes described
- Write with complete confidence - COMPLETELY ELIMINATE all uncertain language: "likely", "maybe", "probably", "appears", "seems", "could", "might", "most likely", "presumably", "possibly", "perhaps", "would", "should", "may", "can be", "tends to", "suggests", "indicates", "implies", "typically", "generally", "usually", "often", "sometimes", "occasionally", "conceivably", "supposedly"

JSON format: {"records": [{"recordId": "...", "confidentialityLevel": "...", "confidentialityReasoning": "...", "hasPersonalData": true/false, "personalDataReasoning": "..."}]}`;

    let processedRecords;

    try {
      console.log(`Sending prompt to AI with context length: ${detailedSystemContext.length} characters`);
      console.log(`Processing ${pageData.length} database records`);
      console.log(`Estimated prompt length: ${prompt.length} characters`);
      console.log(`Detailed system context being used: ${detailedSystemContext.substring(0, 500)}...`);

      // Use gemini-2.5-pro with full 1M context window for comprehensive analysis
      console.log(`Processing ${pageData.length} records with full 1M context window`);

      // Create a fresh model instance to reset context between calls
      const freshModel = google('gemini-2.5-pro');

      const result = await generateObject({
        model: freshModel,
        prompt: prompt,
        schema: PageClassificationSchema,
        maxTokens: 1000000,
        // Add experimental settings for large context processing
        experimental_telemetry: {
          isEnabled: true,
          recordInputs: false,
          recordOutputs: false,
        },
      });

      console.log('AI response received:', result.object);
      processedRecords = result.object.records;

    } catch (aiError) {
      console.error('AI classification failed:', aiError);
      
      return NextResponse.json(
        { 
          error: 'AI classification service unavailable',
          details: 'The AI classification service is currently unavailable. Please try again later.',
          systemId,
          pageNumber,
          recordsProcessed: 0
        },
        { status: 503 }
      );
    }

    // Return classification results
    return NextResponse.json({
      success: true,
      feature: 'page_classification',
      systemId,
      pageNumber,
      startIndex,
      endIndex,
      recordsProcessed: pageData.length,
      classifications: processedRecords
    });

  } catch (error) {
    console.error('Error in page classification:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error during page classification' },
      { status: 500 }
    );
  }
}
